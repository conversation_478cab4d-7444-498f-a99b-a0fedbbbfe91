<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmos.pbms.dao.am.AmQuestionLeaderDAO">
  <resultMap id="BaseResultMap" type="com.cmos.pbms.beans.am.AmQuestionLeader">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="amq_id" jdbcType="VARCHAR" property="amqId" />
    <result column="amql_type" jdbcType="INTEGER" property="amqlType" />
    <result column="amg_info" jdbcType="VARCHAR" property="amgInfo" />
    <result column="org_type" jdbcType="INTEGER" property="orgType" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="isdeleted" jdbcType="INTEGER" property="isdeleted" />
    <result column="createdby" jdbcType="VARCHAR" property="createdby" />
    <result column="createddate" jdbcType="TIMESTAMP" property="createddate" />
    <result column="modifiedby" jdbcType="VARCHAR" property="modifiedby" />
    <result column="modifieddate" jdbcType="TIMESTAMP" property="modifieddate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, amq_id, amql_type, amg_info, org_type, user_id, isdeleted, createdby, createddate, modifiedby, modifieddate
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from am_question_leader
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from am_question_leader
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.cmos.pbms.beans.am.AmQuestionLeader">
    insert into am_question_leader (id, amq_id, amql_type, amg_info, org_type,
      user_id, isdeleted, createdby, 
      createddate, modifiedby, modifieddate
      )
    values (#{id,jdbcType=VARCHAR}, #{amqId,jdbcType=VARCHAR}, #{amqlType,jdbcType=INTEGER}, #{amgInfo,jdbcType=VARCHAR}, #{orgType,jdbcType=INTEGER},
      #{userId,jdbcType=VARCHAR}, #{isdeleted,jdbcType=INTEGER}, #{createdby,jdbcType=VARCHAR}, 
      #{createddate,jdbcType=TIMESTAMP}, #{modifiedby,jdbcType=VARCHAR}, #{modifieddate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into am_question_leader (id, amq_id, amql_type, amg_info, org_type,
    user_id, isdeleted, createdby,
    createddate, modifiedby, modifieddate)
    values
    <foreach collection="amQuestionLeaderList" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.amqId,jdbcType=VARCHAR}, #{item.amqlType,jdbcType=INTEGER}, #{item.amgInfo,jdbcType=VARCHAR}, #{item.orgType,jdbcType=INTEGER},
      #{item.userId,jdbcType=VARCHAR}, #{item.isdeleted,jdbcType=INTEGER}, #{item.createdby,jdbcType=VARCHAR},
      #{item.createddate,jdbcType=TIMESTAMP}, #{item.modifiedby,jdbcType=VARCHAR}, #{item.modifieddate,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="com.cmos.pbms.beans.am.AmQuestionLeader">
    insert into am_question_leader
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="amqId != null">
        amq_id,
      </if>
      <if test="amqlType != null">
        amql_type,
      </if>
      <if test="amgInfo != null">
        amg_info,
      </if>
      <if test="orgType != null">
        org_type,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="isdeleted != null">
        isdeleted,
      </if>
      <if test="createdby != null">
        createdby,
      </if>
      <if test="createddate != null">
        createddate,
      </if>
      <if test="modifiedby != null">
        modifiedby,
      </if>
      <if test="modifieddate != null">
        modifieddate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="amqId != null">
        #{amqId,jdbcType=VARCHAR},
      </if>
      <if test="amqlType != null">
        #{amqlType,jdbcType=INTEGER},
      </if>
      <if test="amgInfo != null">
        #{amgInfo,jdbcType=VARCHAR},
      </if>
      <if test="orgType != null">
        #{orgType,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=INTEGER},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null">
        #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null">
        #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null">
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cmos.pbms.beans.am.AmQuestionLeader">
    update am_question_leader
    <set>
      <if test="amqId != null">
        amq_id = #{amqId,jdbcType=VARCHAR},
      </if>
      <if test="amqlType != null">
        amql_type = #{amqlType,jdbcType=INTEGER},
      </if>
      <if test="amgInfo != null">
        amg_info = #{amgInfo,jdbcType=VARCHAR},
      </if>
      <if test="orgType != null">
        org_type = #{orgType,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="isdeleted != null">
        isdeleted = #{isdeleted,jdbcType=INTEGER},
      </if>
      <if test="createdby != null">
        createdby = #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null">
        createddate = #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null">
        modifiedby = #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null">
        modifieddate = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cmos.pbms.beans.am.AmQuestionLeader">
    update am_question_leader
    set amq_id = #{amqId,jdbcType=VARCHAR},
      amql_type = #{amqlType,jdbcType=INTEGER},
      amg_info = #{amgInfo,jdbcType=VARCHAR},
      org_type = #{orgType,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=VARCHAR},
      isdeleted = #{isdeleted,jdbcType=INTEGER},
      createdby = #{createdby,jdbcType=VARCHAR},
      createddate = #{createddate,jdbcType=TIMESTAMP},
      modifiedby = #{modifiedby,jdbcType=VARCHAR},
      modifieddate = #{modifieddate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="deleteQuestionLeaderByAmqId">
    update am_question_leader
    set isdeleted = 1,
    modifiedby = #{modifiedby,jdbcType=VARCHAR},
    modifieddate = #{modifieddate,jdbcType=TIMESTAMP}
    where amq_id = #{amqId,jdbcType=VARCHAR}
    and id > ''
  </update>

  <select id="selectAmQuestionLeaderListByAmqId" parameterType="java.lang.String" resultType="com.cmos.pbms.beans.dto.AmQuestionLeaderDTO">
    select
      a.id,
      a.amq_id amqId,
      a.amql_type amqlType,
      a.amg_info amgInfo,
      a.org_type orgType,
      a.user_id userId,
      b.username userName
    from
        am_question_leader a
        inner join sys_users b on b.id = a.user_id
    where amq_id = #{amqId,jdbcType=VARCHAR}
    and isdeleted = 0
  </select>
  <select id="selectAmQuestionAmgInfoListByAmId" resultType="com.cmos.pbms.beans.dto.AmQuestionAmgInfoListDTO">
    SELECT
        DISTINCT
        a.amq_id amqId,
        a.amg_info amgInfo
    FROM
        am_question_leader a
    INNER JOIN am_question b ON a.amq_id = b.id AND b.isdeleted = 0 AND b.am_id = #{amId,jdbcType=VARCHAR}
    WHERE
        a.isdeleted = 0
  </select>
</mapper>