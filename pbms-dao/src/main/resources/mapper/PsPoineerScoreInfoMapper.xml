<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmos.pbms.dao.ps.PsPoineerScoreInfoDAO">
    <resultMap id="BaseResultMap" type="com.cmos.pbms.beans.ps.PsPoineerScoreInfo">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="poineer_month" jdbcType="VARCHAR" property="poineerMonth"/>
        <result column="statement_status" jdbcType="TINYINT" property="statementStatus"/>
        <result column="isdeleted" jdbcType="INTEGER" property="isdeleted"/>
        <result column="createdby" jdbcType="VARCHAR" property="createdby"/>
        <result column="createddate" jdbcType="TIMESTAMP" property="createddate"/>
        <result column="modifiedby" jdbcType="VARCHAR" property="modifiedby"/>
        <result column="modifieddate" jdbcType="TIMESTAMP" property="modifieddate"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, poineer_month, statement_status, isdeleted, createdby, createddate, modifiedby,
        modifieddate
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ps_poineer_score_info
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from ps_poineer_score_info
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmos.pbms.beans.ps.PsPoineerScoreInfo">
        insert into ps_poineer_score_info (id, poineer_month, statement_status,
          isdeleted, createdby, createddate,
          modifiedby, modifieddate)
        values (#{id,jdbcType=VARCHAR}, #{poineerMonth,jdbcType=VARCHAR}, #{statementStatus,jdbcType=TINYINT},
          #{isdeleted,jdbcType=INTEGER}, #{createdby,jdbcType=VARCHAR}, #{createddate,jdbcType=TIMESTAMP},
          #{modifiedby,jdbcType=VARCHAR}, #{modifieddate,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" parameterType="com.cmos.pbms.beans.ps.PsPoineerScoreInfo">
        insert into ps_poineer_score_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="poineerMonth != null">
                poineer_month,
            </if>
            <if test="statementStatus != null">
                statement_status,
            </if>
            <if test="isdeleted != null">
                isdeleted,
            </if>
            <if test="createdby != null">
                createdby,
            </if>
            <if test="createddate != null">
                createddate,
            </if>
            <if test="modifiedby != null">
                modifiedby,
            </if>
            <if test="modifieddate != null">
                modifieddate,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="poineerMonth != null">
                #{poineerMonth,jdbcType=VARCHAR},
            </if>
            <if test="statementStatus != null">
                #{statementStatus,jdbcType=TINYINT},
            </if>
            <if test="isdeleted != null">
                #{isdeleted,jdbcType=INTEGER},
            </if>
            <if test="createdby != null">
                #{createdby,jdbcType=VARCHAR},
            </if>
            <if test="createddate != null">
                #{createddate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedby != null">
                #{modifiedby,jdbcType=VARCHAR},
            </if>
            <if test="modifieddate != null">
                #{modifieddate,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.cmos.pbms.beans.ps.PsPoineerScoreInfo">
        update ps_poineer_score_info
        <set>
            <if test="poineerMonth != null">
                poineer_month = #{poineerMonth,jdbcType=VARCHAR},
            </if>
            <if test="statementStatus != null">
                statement_status = #{statementStatus,jdbcType=TINYINT},
            </if>
            <if test="isdeleted != null">
                isdeleted = #{isdeleted,jdbcType=INTEGER},
            </if>
            <if test="createdby != null">
                createdby = #{createdby,jdbcType=VARCHAR},
            </if>
            <if test="createddate != null">
                createddate = #{createddate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedby != null">
                modifiedby = #{modifiedby,jdbcType=VARCHAR},
            </if>
            <if test="modifieddate != null">
                modifieddate = #{modifieddate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.cmos.pbms.beans.ps.PsPoineerScoreInfo">
        update ps_poineer_score_info
        set poineer_month = #{poineerMonth,jdbcType=VARCHAR},
          statement_status = #{statementStatus,jdbcType=TINYINT},
          isdeleted = #{isdeleted,jdbcType=INTEGER},
          createdby = #{createdby,jdbcType=VARCHAR},
          createddate = #{createddate,jdbcType=TIMESTAMP},
          modifiedby = #{modifiedby,jdbcType=VARCHAR},
          modifieddate = #{modifieddate,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="selectPoineerScoreByDate" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ps_poineer_score_info
        where poineer_month = #{poineerMonth,jdbcType=VARCHAR}
        <if test="null != statementStatus">
            and statement_status = #{statementStatus,jdbcType=TINYINT}
        </if>
    </select>
</mapper>