<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmos.pbms.dao.news.SpecialDAO">
    <resultMap id="BaseResultMap" type="com.cmos.pbms.beans.news.Special">
        <id column="id" jdbcType="VARCHAR" property="id"></id>
        <result column="province" jdbcType="VARCHAR" property="province"></result>
        <result column="orgid" jdbcType="VARCHAR" property="orgid"></result>
        <result column="userid" jdbcType="VARCHAR" property="userid"></result>
        <result column="obj_type" jdbcType="INTEGER" property="objType"></result>
        <result column="specname" jdbcType="VARCHAR" property="specname"></result>
        <result column="summary" jdbcType="VARCHAR" property="summary"></result>
        <result column="themepic" jdbcType="VARCHAR" property="themepic"></result>
        <result column="remark" jdbcType="VARCHAR" property="remark"></result>
        <result column="istop" jdbcType="INTEGER" property="istop"></result>
        <result column="toptime" jdbcType="TIMESTAMP" property="toptime"></result>
        <result column="specstatus" jdbcType="INTEGER" property="specstatus"></result>
        <result column="isdeleted" jdbcType="INTEGER" property="isdeleted"></result>
        <result column="createdby" jdbcType="VARCHAR" property="createdby"></result>
        <result column="createddate" jdbcType="TIMESTAMP" property="createddate"></result>
        <result column="modifiedby" jdbcType="VARCHAR" property="modifiedby"></result>
        <result column="modifieddate" jdbcType="TIMESTAMP" property="modifieddate"></result>
        <result column="obj_category" jdbcType="VARCHAR" property="objCategory"></result>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"></result>
    </resultMap>
    <sql id="Base_Column_List">
        id, province, orgid, userid, obj_type, specname, summary, themepic, remark, istop, toptime,
specstatus, isdeleted, createdby, createddate, modifiedby, modifieddate, obj_category, order_no
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from news_special
where id = #{id,jdbcType=VARCHAR}
and isdeleted = 0
    </select>
    <select id="selectListByParams" resultType="com.cmos.pbms.beans.dto.NewsSpecialListDTO">
        SELECT ns.id, ns.specname,
               org.orgsname AS `orgName`,
        u.username AS `creatorName`, ns.createddate, ns.specstatus, ns.istop,
                                                     ns.themepic, ns.obj_type AS objType,
                                                     ns.obj_category objCategory,
                                                     sdi.itemtext objCategoryDesc, ns.order_no orderNo
FROM pm_organization org LEFT JOIN news_special ns ON ns.orgid = org.id LEFT JOIN sys_users u ON u.id = ns.createdby LEFT JOIN sys_dictionaryitems sdi ON sdi.codestr = ns.obj_category
AND dictionaryid = #{dictId} and sdi.isdeleted = 0
WHERE ns.isdeleted = 0 <if test="orgCode != null and orgCode != ''">
AND org.codestr LIKE #{orgCode} || '%' </if> <if test="specname != null and specname != ''">
AND ns.specname LIKE '%' || #{specname} || '%' </if> <if test="objCategory != null and objCategory != ''">
AND ns.obj_category = #{objCategory} </if> <if test="creatorName != null and creatorName != ''">
AND u.username LIKE '%' || #{creatorName} || '%' </if> <if test="createdateStart != null and createdateEnd != null">
AND ns.createddate &gt;= #{createdateStart}
AND ns.createddate &lt; #{createdateEnd} </if> <if test="orgid != null and orgid != ''"> <choose> <when test="includeSubOrg != null">
AND (org.id = #{orgid}
OR org.codestr LIKE #{orgid} || '%') </when> <otherwise>
AND ns.orgid = #{orgid} </otherwise> </choose> </if> <if test="specstatus != null">
AND ns.specstatus = #{specstatus} </if> <choose> <when test="objType == null">
AND ns.obj_type IN (1, 2) </when> <otherwise>
AND ns.obj_type = #{objType} </otherwise> </choose>
ORDER BY ns.order_no
    </select>
    <select id="selectListByProvince" resultType="com.cmos.pbms.beans.dto.NewsSpecialListDTO">
        SELECT ns.id, ns.specname, org.orgsname AS `orgName`, u.username AS `creatorName`, ns.createddate, ns.specstatus, ns.istop, ns.themepic, ns.obj_type AS objType, ns.obj_category objCategory
FROM news_special ns LEFT JOIN pm_organization org ON org.id = ns.orgid LEFT JOIN sys_users u ON u.id = ns.createdby
WHERE ns.isdeleted = 0
AND ns.specstatus = 1 <if test="province != null">
AND ns.province = #{province} </if>
ORDER BY ns.specstatus DESC, ns.istop DESC, ns.toptime DESC, ns.createddate DESC LIMIT 0,5
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        UPDATE news_special
SET isdeleted = 1
WHERE id = #{id,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteRealByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM news_special
WHERE id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.cmos.pbms.beans.news.Special">
        insert into news_special (id, province, orgid,
userid, obj_type, specname,
summary, themepic,
remark, istop, toptime,
specstatus, isdeleted, createdby,
createddate, modifiedby, modifieddate,
obj_category, order_no)
values (#{id,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{orgid,jdbcType=VARCHAR},
#{userid,jdbcType=VARCHAR}, #{objType,jdbcType=INTEGER}, #{specname,jdbcType=VARCHAR},
#{summary,jdbcType=VARCHAR},  #{themepic,jdbcType=VARCHAR},
#{remark,jdbcType=VARCHAR}, #{istop,jdbcType=INTEGER}, #{toptime,jdbcType=TIMESTAMP},
#{specstatus,jdbcType=INTEGER}, #{isdeleted,jdbcType=INTEGER}, #{createdby,jdbcType=VARCHAR},
#{createddate,jdbcType=TIMESTAMP}, #{modifiedby,jdbcType=VARCHAR}, #{modifieddate,jdbcType=TIMESTAMP},
#{objCategory,jdbcType=VARCHAR}, #{orderNo,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.cmos.pbms.beans.news.Special">
        insert into news_special
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="orgid != null">
                orgid,
            </if>
            <if test="userid != null">
                userid,
            </if>
            <if test="objType != null">
                obj_type,
            </if>
            <if test="specname != null">
                specname,
            </if>
            <if test="summary != null">
                summary,
            </if>
            <if test="themepic != null">
                themepic,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="specstatus != null">
                specstatus,
            </if>
            <if test="istop != null">
                istop,
            </if>
            <if test="toptime != null">
                toptime,
            </if>
            <if test="isdeleted != null">
                isdeleted,
            </if>
            <if test="createdby != null">
                createdby,
            </if>
            <if test="createddate != null">
                createddate,
            </if>
            <if test="modifiedby != null">
                modifiedby,
            </if>
            <if test="modifieddate != null">
                modifieddate,
            </if>
            <if test="objCategory != null">
                obj_category,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="orgid != null">
                #{orgid,jdbcType=VARCHAR},
            </if>
            <if test="userid != null">
                #{userid,jdbcType=VARCHAR},
            </if>
            <if test="objType != null">
                #{objType,jdbcType=INTEGER},
            </if>
            <if test="specname != null">
                #{specname,jdbcType=VARCHAR},
            </if>
            <if test="summary != null">
                #{summary,jdbcType=VARCHAR},
            </if>
            <if test="themepic != null">
                #{themepic,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="istop != null">
                #{istop,jdbcType=INTEGER},
            </if>
            <if test="toptime != null">
                #{toptime,jdbcType=TIMESTAMP},
            </if>
            <if test="specstatus != null">
                #{specstatus,jdbcType=INTEGER},
            </if>
            <if test="isdeleted != null">
                #{isdeleted,jdbcType=INTEGER},
            </if>
            <if test="createdby != null">
                #{createdby,jdbcType=VARCHAR},
            </if>
            <if test="createddate != null">
                #{createddate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedby != null">
                #{modifiedby,jdbcType=VARCHAR},
            </if>
            <if test="modifieddate != null">
                #{modifieddate,jdbcType=TIMESTAMP},
            </if>
            <if test="objCategory != null">
                #{objCategory,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cmos.pbms.beans.news.Special">
        update news_special
        <set>
            <if test="province != null">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="orgid != null">
                orgid = #{orgid,jdbcType=VARCHAR},
            </if>
            <if test="userid != null">
                userid = #{userid,jdbcType=VARCHAR},
            </if>
            <if test="objType != null">
                obj_type = #{objType,jdbcType=INTEGER},
            </if>
            <if test="specname != null">
                specname = #{specname,jdbcType=VARCHAR},
            </if>
            <if test="summary != null">
                summary = #{summary,jdbcType=VARCHAR},
            </if>
            <if test="themepic != null">
                themepic = #{themepic,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="istop != null">
                istop = #{istop,jdbcType=INTEGER},
            </if>
            <if test="toptime != null">
                toptime = #{toptime,jdbcType=TIMESTAMP},
            </if>
            <if test="specstatus != null">
                specstatus = #{specstatus,jdbcType=INTEGER},
            </if>
            <if test="isdeleted != null">
                isdeleted = #{isdeleted,jdbcType=INTEGER},
            </if>
            <if test="createdby != null">
                createdby = #{createdby,jdbcType=VARCHAR},
            </if>
            <if test="createddate != null">
                createddate = #{createddate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedby != null">
                modifiedby = #{modifiedby,jdbcType=VARCHAR},
            </if>
            <if test="modifieddate != null">
                modifieddate = #{modifieddate,jdbcType=TIMESTAMP},
            </if>
            <if test="objCategory != null">
                obj_category = #{objCategory,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cmos.pbms.beans.news.Special">
        update news_special
set province = #{province,jdbcType=VARCHAR},
orgid = #{orgid,jdbcType=VARCHAR},
userid = #{userid,jdbcType=VARCHAR},
obj_type = #{objType,jdbcType=INTEGER},
specname = #{specname,jdbcType=VARCHAR},
summary = #{summary,jdbcType=VARCHAR},
themepic = #{themepic,jdbcType=VARCHAR},
remark = #{remark,jdbcType=VARCHAR},
specstatus = #{specstatus,jdbcType=INTEGER},
istop = #{istop,jdbcType=INTEGER},
toptime = #{toptime,jdbcType=TIMESTAMP},
isdeleted = #{isdeleted,jdbcType=INTEGER},
createdby = #{createdby,jdbcType=VARCHAR},
createddate = #{createddate,jdbcType=TIMESTAMP},
modifiedby = #{modifiedby,jdbcType=VARCHAR},
modifieddate = #{modifieddate,jdbcType=TIMESTAMP},
obj_category = #{objCategory,jdbcType=VARCHAR},
order_no = #{orderNo,jdbcType=INTEGER}
where id = #{id,jdbcType=VARCHAR}
    </update>
    <select id="selectListBySpecialName" resultType="com.cmos.pbms.beans.dto.NewsSpecialListDTO">
        SELECT ns.id, ns.specname, po.orgsname AS orgName, su.username AS creatorNam, ns.createddate, ns.specstatus,
        ns.themepic, ns.obj_type AS objType, nsn.playTimes, nsn.fileNum, ns.obj_category AS objCategory, nsn.pushTime
        FROM news_special ns, pm_organization po, (
        SELECT sn.specialid, SUM(nn.readcount) playTimes, COUNT(nn.id) fileNum, MAX(
        <choose>
            <when test="objType == 3">nn.pushtime</when>
            <otherwise>nn.review_time</otherwise>
        </choose>
        ) pushTime
        FROM news_specialnews sn,
        <choose>
            <when test="objType == 3">news_news</when>
            <otherwise>pl_studysource</otherwise>
        </choose>
        nn
        WHERE sn.newsid = nn.id
        AND nn.isdeleted = 0
        AND sn.isdeleted = 0
        <choose>
            <when test="objType == 3">
                AND nn.newsstatus in (3, 4)
            </when>
            <otherwise>
                AND nn.review_status in (3, 4)
            </otherwise>
        </choose>
        GROUP BY sn.specialid) nsn, sys_users su
        WHERE ns.orgid = po.id
        AND ns.id = nsn.specialid
        AND ns.createdby = su.id
        AND ns.isdeleted = 0
        AND ns.specstatus = 1 <if test="orgCode != null and orgCode != ''">
        AND org.codestr LIKE #{orgCode} || '%'
    </if>
        <if test="specialName != null and specialName != ''">
            AND ns.specname LIKE '%' || #{specialName} || '%'
        </if>
        <if test="objType != null">
            AND ns.obj_type = #{objType}
        </if>
        ORDER BY po.levelnum ASC, ns.createddate DESC
    </select>
    <select id="selectMediaListForAPP" resultType="com.cmos.pbms.beans.pl.StudySourceForAPP">
        SELECT ss.id, ss.title, ss.storepath, ss.readcount, ss.themepic, ss.summary, ss.duration, pm.orgsname orgname, dicitem.itemtext strcontenttype, ss.createddate, ss.enable_review enablereview, ss.enable_thumbs enablethumbs, ss.readcount, ss.reviews_count reviewscount, ss.thumbs_count thumbscount, ss.make_from makeFrom, ss.turn_from turnFrom, ss.review_time reviewTime
FROM pl_studysource ss INNER JOIN pm_organization pm ON pm.id = ss.orgid INNER JOIN sys_dictionaryitems dicitem ON dicitem.codestr = ss.contenttype
AND dicitem.isdeleted = 0 INNER JOIN news_specialnews ns ON ns.newsid = ss.id
AND ns.isdeleted = 0
WHERE ss.isdeleted = 0 and ss.ssstatus = 1
AND ns.specialid = #{specialId, jdbcType=VARCHAR} <if test="title != null and title != ''">
AND ss.title LIKE '%' || #{title, jdbcType=VARCHAR} || '%' </if>
ORDER BY ss.id DESC
    </select>
    <select id="selectSpecialSelect" resultType="com.cmos.pbms.beans.dto.NewsSpecialListDTO">
        SELECT ns.id, ns.specname
FROM news_special ns, pm_organization po
WHERE ns.orgid = po.id
AND ns.isdeleted = 0
AND ns.specstatus = 1 <if test="orgCode != null and orgCode != ''">
AND org.codestr LIKE #{orgCode} || '%' </if> <if test="specialName != null and specialName != ''">
AND ns.specname LIKE '%' || #{specialName} || '%' </if> <if test="objType != null">
AND ns.obj_type = #{objType} </if> <if test="objCategory != null">
AND ns.obj_category = #{objCategory} </if>
ORDER BY po.levelnum ASC, ns.createddate DESC
    </select>
    <select id="selectSpecialByObjId" resultType="com.cmos.pbms.beans.news.Special">
        SELECT ns.id, ns.province, ns.orgid, ns.userid, ns.obj_type objType, ns.specname, ns.summary, ns.themepic, ns.remark, ns.istop, ns.toptime, ns.specstatus, ns.isdeleted, ns.createdby, ns.createddate, ns.modifiedby, ns.modifieddate, ns.obj_category objCategory
FROM news_special ns, news_specialnews nsn
WHERE ns.id = nsn.specialid
AND ns.isdeleted = 0
AND nsn.isdeleted = 0
AND ns.specstatus = 1
AND nsn.newsid = #{objId} LIMIT 0,1
    </select>
    <select id="selectNewsListForAPP" resultType="com.cmos.pbms.beans.dto.NewsDetailDTO">
        SELECT pm.orgsname orgname, dicitem.itemtext strcontenttype, ns.id, ns.category, ns.orgid, ns.userid, ns.province, ns.enablereview, ns.enablethumbs, ns.istop, ns.toptime, ns.themepic, ns.newsstatus, ns.title, ns.content, ns.readcount, ns.reviewscount, ns.thumbscount, ns.pushtime, ns.isdeleted, ns.createdby, ns.createddate, ns.auditby, ns.auditdate, ns.modifiedby, ns.modifieddate, ns.drafter_name drafterName, ns.com_name comName, ns.dept_name deptName, ns.news_from newsFrom, ns.make_from makeFrom, ns.turn_from turnFrom, ns.layout_style layoutStyle, ns.submit_user submitUser, ns.submit_time submitTime, ns.refuse_reason refuseReason, ns.enable_status enableStatus, ns.public_scope publicScope
FROM news_news ns INNER JOIN pm_organization pm ON pm.id = ns.orgid INNER JOIN sys_dictionaryitems dicitem ON dicitem.codestr = ns.category
AND dicitem.isdeleted = 0 INNER JOIN news_specialnews nsn ON nsn.newsid = ns.id
AND nsn.isdeleted = 0
WHERE ns.isdeleted = 0 and ns.newsstatus IN (3, 4)
AND nsn.specialid = #{specialId, jdbcType=VARCHAR} <if test="title != null and title != ''">
AND ns.title LIKE '%' || #{title, jdbcType=VARCHAR} || '%' </if>
ORDER BY ns.id DESC
    </select>
</mapper>
