<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmos.pbms.dao.sys.FootPointDetailDAO">
    <resultMap id="BaseResultMap" type="com.cmos.pbms.beans.sys.FootPointDetail">
        <id column="id" jdbcType="VARCHAR" property="id"></id>
        <result column="point_id" jdbcType="VARCHAR" property="pointId"></result>
        <result column="operation" jdbcType="VARCHAR" property="operation"></result>
        <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime"></result>
        <result column="operate_content" jdbcType="VARCHAR" property="operateContent"></result>
        <result column="ext_fld1" jdbcType="VARCHAR" property="extFld1"></result>
        <result column="ext_fld2" jdbcType="VARCHAR" property="extFld2"></result>
        <result column="ext_fld3" jdbcType="VARCHAR" property="extFld3"></result>
        <result column="created_date" jdbcType="TIMESTAMP" property="createdDate"></result>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"></result>
        <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate"></result>
        <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy"></result>
    </resultMap>
    <sql id="Base_Column_List">
        id, point_id, operation, operate_time, operate_content, ext_fld1, ext_fld2, ext_fld3,
created_date, created_by, modified_date, modified_by
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from sys_footpoint_detail
where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from sys_footpoint_detail
where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.cmos.pbms.beans.sys.FootPointDetail">
        insert into sys_footpoint_detail (id, point_id, operation,
operate_time, operate_content, ext_fld1,
ext_fld2, ext_fld3, created_date,
created_by, modified_date, modified_by
)
values (#{id,jdbcType=VARCHAR}, #{pointId,jdbcType=VARCHAR}, #{operation,jdbcType=VARCHAR},
#{operateTime,jdbcType=TIMESTAMP}, #{operateContent,jdbcType=VARCHAR}, #{extFld1,jdbcType=VARCHAR},
#{extFld2,jdbcType=VARCHAR}, #{extFld3,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP},
#{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}
)
    </insert>
    <insert id="insertSelective" parameterType="com.cmos.pbms.beans.sys.FootPointDetail">
        insert into sys_footpoint_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="pointId != null">
                point_id,
            </if>
            <if test="operation != null">
                operation,
            </if>
            <if test="operateTime != null">
                operate_time,
            </if>
            <if test="operateContent != null">
                operate_content,
            </if>
            <if test="extFld1 != null">
                ext_fld1,
            </if>
            <if test="extFld2 != null">
                ext_fld2,
            </if>
            <if test="extFld3 != null">
                ext_fld3,
            </if>
            <if test="createdDate != null">
                created_date,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="modifiedDate != null">
                modified_date,
            </if>
            <if test="modifiedBy != null">
                modified_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="pointId != null">
                #{pointId,jdbcType=VARCHAR},
            </if>
            <if test="operation != null">
                #{operation,jdbcType=VARCHAR},
            </if>
            <if test="operateTime != null">
                #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operateContent != null">
                #{operateContent,jdbcType=VARCHAR},
            </if>
            <if test="extFld1 != null">
                #{extFld1,jdbcType=VARCHAR},
            </if>
            <if test="extFld2 != null">
                #{extFld2,jdbcType=VARCHAR},
            </if>
            <if test="extFld3 != null">
                #{extFld3,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="modifiedDate != null">
                #{modifiedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedBy != null">
                #{modifiedBy,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cmos.pbms.beans.sys.FootPointDetail">
        update sys_footpoint_detail
        <set>
            <if test="pointId != null">
                point_id = #{pointId,jdbcType=VARCHAR},
            </if>
            <if test="operation != null">
                operation = #{operation,jdbcType=VARCHAR},
            </if>
            <if test="operateTime != null">
                operate_time = #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operateContent != null">
                operate_content = #{operateContent,jdbcType=VARCHAR},
            </if>
            <if test="extFld1 != null">
                ext_fld1 = #{extFld1,jdbcType=VARCHAR},
            </if>
            <if test="extFld2 != null">
                ext_fld2 = #{extFld2,jdbcType=VARCHAR},
            </if>
            <if test="extFld3 != null">
                ext_fld3 = #{extFld3,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="modifiedDate != null">
                modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedBy != null">
                modified_by = #{modifiedBy,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cmos.pbms.beans.sys.FootPointDetail">
        update sys_footpoint_detail
set point_id = #{pointId,jdbcType=VARCHAR},
operation = #{operation,jdbcType=VARCHAR},
operate_time = #{operateTime,jdbcType=TIMESTAMP},
operate_content = #{operateContent,jdbcType=VARCHAR},
ext_fld1 = #{extFld1,jdbcType=VARCHAR},
ext_fld2 = #{extFld2,jdbcType=VARCHAR},
ext_fld3 = #{extFld3,jdbcType=VARCHAR},
created_date = #{createdDate,jdbcType=TIMESTAMP},
created_by = #{createdBy,jdbcType=VARCHAR},
modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
modified_by = #{modifiedBy,jdbcType=VARCHAR}
where id = #{id,jdbcType=VARCHAR}
    </update>
    <select id="selectDetailByPointId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM sys_footpoint_detail
WHERE point_id = #{pointId}
ORDER BY created_date DESC
    </select>
    <select id="sumTopRankByType" resultType="com.cmos.pbms.beans.dto.TopRankRepDTO">
        SELECT count(sup.id) readcount, total.obj_id objId, news.title objTitle
FROM sys_footpoint_detail sup LEFT JOIN sys_footpoint total ON total.id = sup.point_id <if test="childObjType == 10101">LEFT JOIN news_news news ON news.id = total.obj_id</if> <if test="childObjType == 10201">LEFT JOIN pl_studysource news ON news.id = total.obj_id</if> LEFT JOIN pm_organization pz ON pz.id = news.orgid
AND pz.isdeleted = 0
AND pz.orgstatus = 1
WHERE (sup.operation = '查阅'
OR sup.operation = '观看')
AND pz.province &lt;&gt; #{excludeProvince} <if test="orgName != null">
AND pz.orgsname LIKE '%' || #{orgName} || '%'</if> <if test="currentOrgCode != null">
AND pz.codestr LIKE #{currentOrgCode} || '%'</if> <if test="orgIdList != null">
AND pz.id IN <foreach collection="orgIdList" open="(" separator=", " close=")" item="orgId">#{orgId}</foreach> </if>
AND total.child_obj_type = #{childObjType}
AND sup.created_date BETWEEN #{startDate}
AND #{endDate}
GROUP BY objId
ORDER BY readCount DESC <if test="limit != null">LIMIT #{limit}</if>
    </select>
</mapper>