<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmos.pbms.dao.vd.VdConventioneerDAO">
  <resultMap id="BaseResultMap" type="com.cmos.pbms.beans.vd.VdConventioneer">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="vd_id" jdbcType="VARCHAR" property="vdId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="xy_user_id" jdbcType="VARCHAR" property="xyUserId" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="is_join" jdbcType="SMALLINT" property="isJoin" />
    <result column="isdeleted" jdbcType="INTEGER" property="isdeleted" />
    <result column="createdby" jdbcType="VARCHAR" property="createdby" />
    <result column="createddate" jdbcType="TIMESTAMP" property="createddate" />
    <result column="modifiedby" jdbcType="VARCHAR" property="modifiedby" />
    <result column="modifieddate" jdbcType="TIMESTAMP" property="modifieddate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, vd_id, user_id, xy_user_id, phone_number, user_name, is_join, isdeleted,
    createdby, createddate, modifiedby, modifieddate
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vd_conventioneer
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectByVdId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from vd_conventioneer
    where vd_id = #{vdId,jdbcType=VARCHAR}
    and isdeleted = 0
  </select>
  <select id="selectConventioneerListDTOSByVdId" parameterType="java.lang.String" resultType="com.cmos.pbms.beans.dto.VdConventioneerListDTO">
    select
       id id,
       vd_id vdId,
       user_id userId,
       xy_user_id xyUserId,
       phone_number phoneNumber,
       user_name userName,
       is_join isJoin
    from vd_conventioneer
    where vd_id = #{vdId,jdbcType=VARCHAR}
    and isdeleted = 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from vd_conventioneer
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.cmos.pbms.beans.vd.VdConventioneer">
    insert into vd_conventioneer (id, vd_id, user_id,
      xy_user_id, phone_number, user_name, 
      is_join, isdeleted,
      createdby, createddate, modifiedby, 
      modifieddate)
    values (#{id,jdbcType=VARCHAR}, #{vdId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR},
      #{xyUserId,jdbcType=VARCHAR}, #{phoneNumber,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, 
      #{isJoin,jdbcType=SMALLINT}, #{isdeleted,jdbcType=INTEGER},
      #{createdby,jdbcType=VARCHAR}, #{createddate,jdbcType=TIMESTAMP}, #{modifiedby,jdbcType=VARCHAR}, 
      #{modifieddate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into vd_conventioneer (id, vd_id, user_id,
    xy_user_id, phone_number, user_name,
    is_join, isdeleted,
    createdby, createddate)
    values
    <foreach collection="vdConventioneers" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.vdId,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR},
      #{item.xyUserId,jdbcType=VARCHAR}, #{item.phoneNumber,jdbcType=VARCHAR}, #{item.userName,jdbcType=VARCHAR},
      #{item.isJoin,jdbcType=SMALLINT}, #{item.isdeleted,jdbcType=INTEGER},
      #{item.createdby,jdbcType=VARCHAR}, #{item.createddate,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="com.cmos.pbms.beans.vd.VdConventioneer">
    insert into vd_conventioneer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vdId != null">
        vd_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="xyUserId != null">
        xy_user_id,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="isJoin != null">
        is_join,
      </if>
      <if test="isdeleted != null">
        isdeleted,
      </if>
      <if test="createdby != null">
        createdby,
      </if>
      <if test="createddate != null">
        createddate,
      </if>
      <if test="modifiedby != null">
        modifiedby,
      </if>
      <if test="modifieddate != null">
        modifieddate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="vdId != null">
        #{vdId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="xyUserId != null">
        #{xyUserId,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="isJoin != null">
        #{isJoin,jdbcType=SMALLINT},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=INTEGER},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null">
        #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null">
        #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null">
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cmos.pbms.beans.vd.VdConventioneer">
    update vd_conventioneer
    <set>
      <if test="vdId != null">
        vd_id = #{vdId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="xyUserId != null">
        xy_user_id = #{xyUserId,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="isJoin != null">
        is_join = #{isJoin,jdbcType=SMALLINT},
      </if>
      <if test="isdeleted != null">
        isdeleted = #{isdeleted,jdbcType=INTEGER},
      </if>
      <if test="createdby != null">
        createdby = #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null">
        createddate = #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null">
        modifiedby = #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null">
        modifieddate = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cmos.pbms.beans.vd.VdConventioneer">
    update vd_conventioneer
    set vd_id = #{vdId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      xy_user_id = #{xyUserId,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      is_join = #{isJoin,jdbcType=SMALLINT},
      isdeleted = #{isdeleted,jdbcType=INTEGER},
      createdby = #{createdby,jdbcType=VARCHAR},
      createddate = #{createddate,jdbcType=TIMESTAMP},
      modifiedby = #{modifiedby,jdbcType=VARCHAR},
      modifieddate = #{modifieddate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateToDeleteByVdId"  >
    update vd_conventioneer
    set
      isdeleted = 1
      modifiedby = #{modifiedby,jdbcType=VARCHAR},
      modifieddate = #{modifieddate,jdbcType=TIMESTAMP}
    where id >''
    and vd_id = #{vdId,jdbcType=VARCHAR}
  </update>
</mapper>