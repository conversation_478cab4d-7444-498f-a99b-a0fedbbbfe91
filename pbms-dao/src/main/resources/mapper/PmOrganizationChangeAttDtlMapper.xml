<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmos.pbms.dao.pm.PmChangeAttDtlDAO">
  <resultMap id="BaseResultMap" type="com.cmos.pbms.beans.pm.PmChangeAttDtl">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="org_change_id" jdbcType="VARCHAR" property="orgChangeId" />
    <result column="att_type" jdbcType="VARCHAR" property="attType" />
    <result column="att_code" jdbcType="VARCHAR" property="attCode" />
    <result column="att_name" jdbcType="VARCHAR" property="attName" />
    <result column="att_from_value" jdbcType="VARCHAR" property="attFromValue" />
    <result column="att_to_value" jdbcType="VARCHAR" property="attToValue" />
    <result column="isdeleted" jdbcType="INTEGER" property="isdeleted" />
    <result column="createdby" jdbcType="VARCHAR" property="createdby" />
    <result column="createddate" jdbcType="TIMESTAMP" property="createddate" />
    <result column="modifiedby" jdbcType="VARCHAR" property="modifiedby" />
    <result column="modifieddate" jdbcType="TIMESTAMP" property="modifieddate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, org_change_id, att_type, att_code, att_name, att_from_value, att_to_value, isdeleted,
    createdby, createddate, modifiedby, modifieddate
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pm_change_att_dtl
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectOneByChangeId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from
      pm_change_att_dtl
    where
      org_change_id = #{orgChangeId,jdbcType=VARCHAR}
      and att_type = #{attType,jdbcType=VARCHAR}
      and att_code = #{attCode,jdbcType=VARCHAR}
  </select>
  <select id="selectByChangeIdAndAttType" parameterType="java.lang.String" resultType="com.cmos.pbms.beans.dto.PmChangeAttDtlListDTO">
    select
      a.id id,
      a.org_change_id orgChangeId,
      a.att_type attType,
      a.att_code attCode,
      a.att_name attName,
      a.att_from_value attFromValue,
      a.att_to_value attToValue
    from
      pm_change_att_dtl a
    where
        isdeleted = 0
      and a.org_change_id = #{orgChangeId,jdbcType=VARCHAR}
      <if test="null != attType">
        and a.att_type = #{attType,jdbcType=VARCHAR}
      </if>
  </select>
  <select id="selectByChangeId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pm_change_att_dtl
    where org_change_id = #{orgChangeId,jdbcType=VARCHAR}
    and att_type = #{attType,jdbcType=VARCHAR}
    <if test="null != isdeleted">
      and isdeleted = #{isdeleted,jdbcType=INTEGER}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from pm_change_att_dtl
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.cmos.pbms.beans.pm.PmChangeAttDtl">
    insert into pm_change_att_dtl (id, org_change_id, att_type,
      att_code, att_name, att_from_value, 
      att_to_value, isdeleted, createdby, 
      createddate, modifiedby, modifieddate
      )
    values (#{id,jdbcType=VARCHAR}, #{orgChangeId,jdbcType=VARCHAR}, #{attType,jdbcType=VARCHAR},
      #{attCode,jdbcType=VARCHAR}, #{attName,jdbcType=VARCHAR}, #{attFromValue,jdbcType=VARCHAR}, 
      #{attToValue,jdbcType=VARCHAR}, #{isdeleted,jdbcType=INTEGER}, #{createdby,jdbcType=VARCHAR}, 
      #{createddate,jdbcType=TIMESTAMP}, #{modifiedby,jdbcType=VARCHAR}, #{modifieddate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into pm_change_att_dtl (id, org_change_id, att_type,
    att_code, att_name, att_from_value, att_to_value, isdeleted, createdby, createddate)
    values
    <foreach collection="pmChangeAttDtlList" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orgChangeId,jdbcType=VARCHAR}, #{item.attType,jdbcType=VARCHAR},
      #{item.attCode,jdbcType=VARCHAR}, #{item.attName,jdbcType=VARCHAR}, #{item.attFromValue,jdbcType=VARCHAR},
      #{item.attToValue,jdbcType=VARCHAR}, #{item.isdeleted,jdbcType=INTEGER}, #{item.createdby,jdbcType=VARCHAR},
      #{item.createddate,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="com.cmos.pbms.beans.pm.PmChangeAttDtl">
    insert into pm_change_att_dtl
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgChangeId != null">
        org_change_id,
      </if>
      <if test="attType != null">
        att_type,
      </if>
      <if test="attCode != null">
        att_code,
      </if>
      <if test="attName != null">
        att_name,
      </if>
      <if test="attFromValue != null">
        att_from_value,
      </if>
      <if test="attToValue != null">
        att_to_value,
      </if>
      <if test="isdeleted != null">
        isdeleted,
      </if>
      <if test="createdby != null">
        createdby,
      </if>
      <if test="createddate != null">
        createddate,
      </if>
      <if test="modifiedby != null">
        modifiedby,
      </if>
      <if test="modifieddate != null">
        modifieddate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orgChangeId != null">
        #{orgChangeId,jdbcType=VARCHAR},
      </if>
      <if test="attType != null">
        #{attType,jdbcType=VARCHAR},
      </if>
      <if test="attCode != null">
        #{attCode,jdbcType=VARCHAR},
      </if>
      <if test="attName != null">
        #{attName,jdbcType=VARCHAR},
      </if>
      <if test="attFromValue != null">
        #{attFromValue,jdbcType=VARCHAR},
      </if>
      <if test="attToValue != null">
        #{attToValue,jdbcType=VARCHAR},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=INTEGER},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null">
        #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null">
        #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null">
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cmos.pbms.beans.pm.PmChangeAttDtl">
    update pm_change_att_dtl
    <set>
      <if test="orgChangeId != null">
        org_change_id = #{orgChangeId,jdbcType=VARCHAR},
      </if>
      <if test="attType != null">
        att_type = #{attType,jdbcType=VARCHAR},
      </if>
      <if test="attCode != null">
        att_code = #{attCode,jdbcType=VARCHAR},
      </if>
      <if test="attName != null">
        att_name = #{attName,jdbcType=VARCHAR},
      </if>
      <if test="attFromValue != null">
        att_from_value = #{attFromValue,jdbcType=VARCHAR},
      </if>
      <if test="attToValue != null">
        att_to_value = #{attToValue,jdbcType=VARCHAR},
      </if>
      <if test="isdeleted != null">
        isdeleted = #{isdeleted,jdbcType=INTEGER},
      </if>
      <if test="createdby != null">
        createdby = #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null">
        createddate = #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null">
        modifiedby = #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null">
        modifieddate = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cmos.pbms.beans.pm.PmChangeAttDtl">
    update pm_change_att_dtl
    set org_change_id = #{orgChangeId,jdbcType=VARCHAR},
      att_type = #{attType,jdbcType=VARCHAR},
      att_code = #{attCode,jdbcType=VARCHAR},
      att_name = #{attName,jdbcType=VARCHAR},
      att_from_value = #{attFromValue,jdbcType=VARCHAR},
      att_to_value = #{attToValue,jdbcType=VARCHAR},
      isdeleted = #{isdeleted,jdbcType=INTEGER},
      createdby = #{createdby,jdbcType=VARCHAR},
      createddate = #{createddate,jdbcType=TIMESTAMP},
      modifiedby = #{modifiedby,jdbcType=VARCHAR},
      modifieddate = #{modifieddate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateToDeleteByOrgChangeId">
    update pm_change_att_dtl
    set
      isdeleted = 1,
      modifiedby = #{modifiedby,jdbcType=VARCHAR},
      modifieddate = #{modifieddate,jdbcType=TIMESTAMP}
    where id > ''
    and org_change_id = #{orgChangeId,jdbcType=VARCHAR}
    and att_type = #{attType,jdbcType=VARCHAR}
    and isdeleted = 0
  </update>

  <select id="selectChangeDetailByOrgId" resultType="com.cmos.pbms.beans.dto.PmChangeAttDtlListDTO">
    SELECT a.serial_number serialNumber, a.audit_date operationTime, b.att_type attType, b.att_code attCode, b.att_name attName, b.att_from_value attFromValue, b.att_to_value attToValue
    FROM pm_change_info a, pm_change_att_dtl b
    WHERE a.id = b.org_change_id
    AND a.org_id = #{orgId, jdbcType=VARCHAR}
    AND a.isdeleted = 0
    AND b.isdeleted = 0
    AND a.change_state = 3
    AND a.change_type = 1
    AND a.audit_date BETWEEN #{queryStart,jdbcType=TIMESTAMP} AND #{queryEnd,jdbcType=TIMESTAMP}
    ORDER BY a.id, b.id
  </select>
</mapper>