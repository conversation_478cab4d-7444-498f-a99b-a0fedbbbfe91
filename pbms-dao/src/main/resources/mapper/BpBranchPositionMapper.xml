<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmos.pbms.dao.bp.BpBranchPositionDAO">
  <resultMap id="BaseResultMap" type="com.cmos.pbms.beans.bp.BpBranchPosition">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="position_title" jdbcType="VARCHAR" property="positionTitle" />
    <result column="position_description" jdbcType="VARCHAR" property="positionDescription" />
    <result column="order_no" jdbcType="INTEGER" property="orderNo" />
    <result column="data_status" jdbcType="INTEGER" property="dataStatus" />
    <result column="enable_status" jdbcType="INTEGER" property="enableStatus" />
    <result column="isdeleted" jdbcType="INTEGER" property="isdeleted" />
    <result column="createdby" jdbcType="VARCHAR" property="createdby" />
    <result column="createddate" jdbcType="TIMESTAMP" property="createddate" />
    <result column="modifiedby" jdbcType="VARCHAR" property="modifiedby" />
    <result column="modifieddate" jdbcType="TIMESTAMP" property="modifieddate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, org_id, position_title, position_description, order_no, data_status, enable_status, 
    isdeleted, createdby, createddate, modifiedby, modifieddate
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bp_branch_position
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <update id="deleteByPrimaryKey" parameterType="java.lang.String">
    update bp_branch_position
    set isdeleted = 1
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="insert" parameterType="com.cmos.pbms.beans.bp.BpBranchPosition">
    insert into bp_branch_position (id, org_id, position_title, 
      position_description, order_no, data_status, 
      enable_status, isdeleted, createdby, 
      createddate, modifiedby, modifieddate
      )
    values (#{id,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR}, #{positionTitle,jdbcType=VARCHAR}, 
      #{positionDescription,jdbcType=VARCHAR}, #{orderNo,jdbcType=INTEGER}, #{dataStatus,jdbcType=INTEGER}, 
      #{enableStatus,jdbcType=INTEGER}, #{isdeleted,jdbcType=INTEGER}, #{createdby,jdbcType=VARCHAR}, 
      #{createddate,jdbcType=TIMESTAMP}, #{modifiedby,jdbcType=VARCHAR}, #{modifieddate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cmos.pbms.beans.bp.BpBranchPosition">
    insert into bp_branch_position
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="positionTitle != null">
        position_title,
      </if>
      <if test="positionDescription != null">
        position_description,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="dataStatus != null">
        data_status,
      </if>
      <if test="enableStatus != null">
        enable_status,
      </if>
      <if test="isdeleted != null">
        isdeleted,
      </if>
      <if test="createdby != null">
        createdby,
      </if>
      <if test="createddate != null">
        createddate,
      </if>
      <if test="modifiedby != null">
        modifiedby,
      </if>
      <if test="modifieddate != null">
        modifieddate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="positionTitle != null">
        #{positionTitle,jdbcType=VARCHAR},
      </if>
      <if test="positionDescription != null">
        #{positionDescription,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=INTEGER},
      </if>
      <if test="dataStatus != null">
        #{dataStatus,jdbcType=INTEGER},
      </if>
      <if test="enableStatus != null">
        #{enableStatus,jdbcType=INTEGER},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=INTEGER},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null">
        #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null">
        #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null">
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cmos.pbms.beans.bp.BpBranchPosition">
    update bp_branch_position
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="positionTitle != null">
        position_title = #{positionTitle,jdbcType=VARCHAR},
      </if>
      <if test="positionDescription != null">
        position_description = #{positionDescription,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=INTEGER},
      </if>
      <if test="dataStatus != null">
        data_status = #{dataStatus,jdbcType=INTEGER},
      </if>
      <if test="enableStatus != null">
        enable_status = #{enableStatus,jdbcType=INTEGER},
      </if>
      <if test="isdeleted != null">
        isdeleted = #{isdeleted,jdbcType=INTEGER},
      </if>
      <if test="createdby != null">
        createdby = #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null">
        createddate = #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null">
        modifiedby = #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null">
        modifieddate = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cmos.pbms.beans.bp.BpBranchPosition">
    update bp_branch_position
    set org_id = #{orgId,jdbcType=VARCHAR},
      position_title = #{positionTitle,jdbcType=VARCHAR},
      position_description = #{positionDescription,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=INTEGER},
      data_status = #{dataStatus,jdbcType=INTEGER},
      enable_status = #{enableStatus,jdbcType=INTEGER},
      isdeleted = #{isdeleted,jdbcType=INTEGER},
      createdby = #{createdby,jdbcType=VARCHAR},
      createddate = #{createddate,jdbcType=TIMESTAMP},
      modifiedby = #{modifiedby,jdbcType=VARCHAR},
      modifieddate = #{modifieddate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="selectPositionBannerByOrgId" parameterType="java.lang.String" resultType="com.cmos.pbms.beans.dto.BpBranchPositionBannerDTO">
    SELECT a.id positionId, a.position_title positionTitle, b.id fileId, b.url fileUrl, b.attname fileName
    FROM bp_branch_position a
    INNER JOIN attachments b ON a.id = b.objid
    WHERE a.isdeleted = 0
    AND a.enable_status = 1
    AND a.data_status = 1
    AND b.isdeleted = 0
    AND a.org_id = #{orgId,jdbcType=VARCHAR}
    ORDER BY a.order_no ASC, a.id DESC
  </select>

  <select id="selectPositionListByOrgId" parameterType="java.lang.String" resultType="com.cmos.pbms.beans.dto.BpBranchPositionListDTO">
    SELECT
    id, org_id orgId, position_title positionTitle, position_description positionDescription, order_no orderNo, data_status dataStatus
    FROM bp_branch_position
    WHERE isdeleted = 0
    AND org_id = #{orgId,jdbcType=VARCHAR}
    ORDER BY order_no ASC, id DESC
  </select>
</mapper>