<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmos.pbms.dao.sk.SkQuestionsDAO">
    <resultMap id="BaseResultMap" type="com.cmos.pbms.beans.sk.SkQuestions">
        <id column="id" jdbcType="VARCHAR" property="id"></id>
        <result column="bank_id" jdbcType="VARCHAR" property="bankId"></result>
        <result column="chapter" jdbcType="VARCHAR" property="chapter"></result>
        <result column="question_type" jdbcType="INTEGER" property="questionType"></result>
        <result column="status" jdbcType="INTEGER" property="status"></result>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"></result>
        <result column="createdby" jdbcType="VARCHAR" property="createdby"></result>
        <result column="createddate" jdbcType="TIMESTAMP" property="createddate"></result>
        <result column="modifiedby" jdbcType="VARCHAR" property="modifiedby"></result>
        <result column="modifieddate" jdbcType="TIMESTAMP" property="modifieddate"></result>
        <result column="stem" jdbcType="VARCHAR" property="stem"></result>
        <result column="analysis" jdbcType="VARCHAR" property="analysis"></result>
        <result column="is_enable" jdbcType="INTEGER" property="isEnable"></result>
        <result column="push_status" jdbcType="INTEGER" property="pushStatus"></result>
        <result column="push_date" jdbcType="TIMESTAMP" property="pushDate"></result>
    </resultMap>
    <sql id="Base_Column_List">
        id, bank_id, chapter, question_type, status, is_enable, is_delete, createdby, createddate, modifiedby,
modifieddate, stem, analysis, push_status, push_date
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from sk_questions
where id = #{id,jdbcType=VARCHAR}
    </select>
    <update id="deleteByPrimaryKey" parameterType="java.lang.String">
        update sk_questions set is_delete = 1 where id = #{id,jdbcType=VARCHAR}
    </update>
    <insert id="insert" parameterType="com.cmos.pbms.beans.sk.SkQuestions">
        insert into sk_questions (id, bank_id, chapter,
question_type, status, is_enable,
is_delete, createdby, createddate,
modifiedby, modifieddate, stem,
analysis, push_status, push_date
)
values (#{id,jdbcType=VARCHAR}, #{bankId,jdbcType=VARCHAR}, #{chapter,jdbcType=VARCHAR},
#{questionType,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{isEnable,jdbcType=INTEGER},
#{isDelete,jdbcType=INTEGER}, #{createdby,jdbcType=VARCHAR}, #{createddate,jdbcType=TIMESTAMP},
#{modifiedby,jdbcType=VARCHAR}, #{modifieddate,jdbcType=TIMESTAMP}, #{stem,jdbcType=VARCHAR},
#{analysis,jdbcType=VARCHAR}, #{pushStatus,jdbcType=INTEGER}, #{pushDate,jdbcType=TIMESTAMP}
)
    </insert>
    <insert id="insertSelective" parameterType="com.cmos.pbms.beans.sk.SkQuestions">
        insert into sk_questions
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="bankId != null">
                bank_id,
            </if>
            <if test="chapter != null">
                chapter,
            </if>
            <if test="questionType != null">
                question_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="isEnable != null or isEnable == 0">
                is_enable,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="createdby != null">
                createdby,
            </if>
            <if test="createddate != null">
                createddate,
            </if>
            <if test="modifiedby != null">
                modifiedby,
            </if>
            <if test="modifieddate != null">
                modifieddate,
            </if>
            <if test="stem != null">
                stem,
            </if>
            <if test="analysis != null">
                analysis,
            </if>
            <if test="pushStatus != null">
                push_status,
            </if>
            <if test="pushDate != null">
                push_date,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="bankId != null">
                #{bankId,jdbcType=VARCHAR},
            </if>
            <if test="chapter != null">
                #{chapter,jdbcType=VARCHAR},
            </if>
            <if test="questionType != null">
                #{questionType,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="isEnable != null or isEnable == 0">
                #{isEnable,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="createdby != null">
                #{createdby,jdbcType=VARCHAR},
            </if>
            <if test="createddate != null">
                #{createddate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedby != null">
                #{modifiedby,jdbcType=VARCHAR},
            </if>
            <if test="modifieddate != null">
                #{modifieddate,jdbcType=TIMESTAMP},
            </if>
            <if test="stem != null">
                #{stem,jdbcType=VARCHAR},
            </if>
            <if test="analysis != null">
                #{analysis,jdbcType=VARCHAR},
            </if>
            <if test="pushStatus != null">
                #{pushStatus,jdbcType=INTEGER},
            </if>
            <if test="pushDate != null">
                #{pushDate,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cmos.pbms.beans.sk.SkQuestions">
        update sk_questions
        <set>
            <if test="bankId != null">
                bank_id = #{bankId,jdbcType=VARCHAR},
            </if>
            <if test="chapter != null">
                chapter = #{chapter,jdbcType=VARCHAR},
            </if>
            <if test="questionType != null">
                question_type = #{questionType,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="isEnable != null or isEnable == 0">
                is_enable = #{isEnable,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="createdby != null">
                createdby = #{createdby,jdbcType=VARCHAR},
            </if>
            <if test="createddate != null">
                createddate = #{createddate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedby != null">
                modifiedby = #{modifiedby,jdbcType=VARCHAR},
            </if>
            <if test="modifieddate != null">
                modifieddate = #{modifieddate,jdbcType=TIMESTAMP},
            </if>
            <if test="stem != null">
                stem = #{stem,jdbcType=VARCHAR},
            </if>
            <if test="analysis != null">
                analysis = #{analysis,jdbcType=VARCHAR},
            </if>
            <if test="pushStatus != null">
                push_status = #{analysis,pushStatus=INTEGER},
            </if>
            <if test="pushDate != null">
                push_date = #{pushDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cmos.pbms.beans.sk.SkQuestions">
        update sk_questions
set bank_id = #{bankId,jdbcType=VARCHAR},
chapter = #{chapter,jdbcType=VARCHAR},
question_type = #{questionType,jdbcType=INTEGER},
status = #{status,jdbcType=INTEGER},
is_enable = #{isEnable,jdbcType=INTEGER},
is_delete = #{isDelete,jdbcType=INTEGER},
createdby = #{createdby,jdbcType=VARCHAR},
createddate = #{createddate,jdbcType=TIMESTAMP},
modifiedby = #{modifiedby,jdbcType=VARCHAR},
modifieddate = #{modifieddate,jdbcType=TIMESTAMP},
stem = #{stem,jdbcType=VARCHAR},
analysis = #{analysis,jdbcType=VARCHAR},
push_status = #{pushStatus,jdbcType=INTEGER},
push_date = #{pushDate,jdbcType=TIMESTAMP}
where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updatePushStatusByIds" parameterType="java.util.Map">
        update sk_questions
set
modifiedby = #{userId,jdbcType=VARCHAR},
modifieddate = #{currTime,jdbcType=TIMESTAMP},
push_status = #{pushStatus,jdbcType=INTEGER},
push_date = #{pushDate,jdbcType=TIMESTAMP}
where id in
        <foreach collection="Ids" open="(" separator="," close=")" item="id">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <select id="selectByPushId" parameterType="java.lang.String" resultType="com.cmos.pbms.beans.dto.SkQusetionsDTO">
        select
skq.id, skq.bank_id as bankId,
skq.question_type as questionType,
skq.chapter as chapter,
skq.status,
skq.is_enable as isEnable,
skq.stem,
skq.analysis
from sk_questions skq inner join sk_push_questions skpq on skq.id = skpq.question_id
where skq.is_delete = 0
and skpq.is_delete = 0
and skpq.push_id = #{pushId,jdbcType=VARCHAR}
order by skpq.question_push_num asc
    </select>
    <select id="selectDtoByParams" parameterType="java.util.Map" resultType="com.cmos.pbms.beans.dto.SkQusetionsDTO">
        select skq.id, skq.bank_id as bankId, skib.title as itembank, skq.question_type as questionType, skq.chapter as chapter, skq.status, skq.is_enable as isEnable, skq.stem, skq.analysis, su.username as createdby, skq.createddate, skq.push_status pushStatus, skq.push_date pushDate from sk_questions skq left join sk_itembank skib on skq.bank_id = skib.id left join sys_users su on skq.createdby = su.id where skq.is_delete = 0 and skib.is_delete = 0 <if test="itembank != null"> and skib.title like '%' || #{itembank, jdbcType=VARCHAR} || '%' </if> <if test="questionType != null"> and skq.question_type = #{questionType, jdbcType=INTEGER} </if> <if test="status != null or status == 0"> and skq.status = #{status, jdbcType=INTEGER} </if> <if test="isEnable != null or isEnable == 0"> and skq.is_enable = #{isEnable, jdbcType=INTEGER} </if> <if test="pushStatus != null or pushStatus == 0"> and skq.push_status = #{pushStatus, jdbcType=INTEGER} </if> <if test="startTime != null"> and skq.push_date &gt;= #{startTime} </if> <if test="endTime != null"> and skq.push_date &lt; #{endTime} </if> order by skq.createddate desc
    </select>
    <select id="selectDtoById" parameterType="java.lang.String" resultType="com.cmos.pbms.beans.dto.SkQusetionsDTO">
        select
skq.id, skq.bank_id as bankId, skib.title as itembank,
skq.question_type as questionType, skq.chapter as chapter,
skq.status, skq.is_enable as isEnable, skq.stem, skq.analysis,
cu.username as createdby, skq.createddate, mu.username as modifiedby, skq.modifieddate
from sk_questions skq left join sk_itembank skib on skq.bank_id = skib.id
left join sys_users cu on skq.createdby = cu.id
left join sys_users mu on skq.modifiedby = mu.id
where skq.id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectDtoByPushIdForWeb" parameterType="java.lang.String" resultType="com.cmos.pbms.beans.dto.SkQusetionsDTO">
        select
skq.id, skq.bank_id as bankId,
skib.title as itembank, skq.question_type as questionType,
skq.chapter as chapter, skq.status, skq.is_enable as isEnable, skq.stem, skq.analysis,
su.username as createdby, skq.createddate
from sk_questions skq
left join sk_itembank skib on skq.bank_id = skib.id
left join sys_users su on skq.createdby = su.id
inner join sk_push_questions skpq on skq.id = skpq.question_id
where skpq.is_delete = 0 and skpq.push_id = #{pushId,jdbcType=VARCHAR}
    </select>
</mapper>