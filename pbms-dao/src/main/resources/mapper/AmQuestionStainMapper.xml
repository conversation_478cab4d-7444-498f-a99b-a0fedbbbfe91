<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmos.pbms.dao.am.AmQuestionStainDAO">
  <resultMap id="BaseResultMap" type="com.cmos.pbms.beans.am.AmQuestionStain">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="amq_id" jdbcType="VARCHAR" property="amqId" />
    <result column="ams_id" jdbcType="VARCHAR" property="amsId" />
    <result column="isdeleted" jdbcType="INTEGER" property="isdeleted" />
    <result column="createdby" jdbcType="VARCHAR" property="createdby" />
    <result column="createddate" jdbcType="TIMESTAMP" property="createddate" />
    <result column="modifiedby" jdbcType="VARCHAR" property="modifiedby" />
    <result column="modifieddate" jdbcType="TIMESTAMP" property="modifieddate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, amq_id, ams_id, isdeleted, createdby, createddate, modifiedby, modifieddate
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from am_question_stain
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from am_question_stain
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.cmos.pbms.beans.am.AmQuestionStain">
    insert into am_question_stain (id, amq_id, ams_id, 
      isdeleted, createdby, createddate, 
      modifiedby, modifieddate)
    values (#{id,jdbcType=VARCHAR}, #{amqId,jdbcType=VARCHAR}, #{amsId,jdbcType=VARCHAR},
      #{isdeleted,jdbcType=INTEGER}, #{createdby,jdbcType=VARCHAR}, #{createddate,jdbcType=TIMESTAMP}, 
      #{modifiedby,jdbcType=VARCHAR}, #{modifieddate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertBaseBatch" parameterType="java.util.List">
    insert into am_question_stain (id, amq_id, ams_id,
    isdeleted, createdby, createddate)
    values
    <foreach collection="amQuestionStains" item="amQuestionStain" separator=",">
      (#{amQuestionStain.id,jdbcType=VARCHAR}, #{amQuestionStain.amqId,jdbcType=VARCHAR}, #{amQuestionStain.amsId,jdbcType=VARCHAR},
      #{amQuestionStain.isdeleted,jdbcType=INTEGER}, #{amQuestionStain.createdby,jdbcType=VARCHAR}, #{amQuestionStain.createddate,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="com.cmos.pbms.beans.am.AmQuestionStain">
    insert into am_question_stain
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="amqId != null">
        amq_id,
      </if>
      <if test="amsId != null">
        ams_id,
      </if>
      <if test="isdeleted != null">
        isdeleted,
      </if>
      <if test="createdby != null">
        createdby,
      </if>
      <if test="createddate != null">
        createddate,
      </if>
      <if test="modifiedby != null">
        modifiedby,
      </if>
      <if test="modifieddate != null">
        modifieddate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="amqId != null">
        #{amqId,jdbcType=VARCHAR},
      </if>
      <if test="amsId != null">
        #{amsId,jdbcType=VARCHAR},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=INTEGER},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null">
        #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null">
        #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null">
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cmos.pbms.beans.am.AmQuestionStain">
    update am_question_stain
    <set>
      <if test="amqId != null">
        amq_id = #{amqId,jdbcType=VARCHAR},
      </if>
      <if test="amsId != null">
        ams_id = #{amsId,jdbcType=VARCHAR},
      </if>
      <if test="isdeleted != null">
        isdeleted = #{isdeleted,jdbcType=INTEGER},
      </if>
      <if test="createdby != null">
        createdby = #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null">
        createddate = #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null">
        modifiedby = #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null">
        modifieddate = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cmos.pbms.beans.am.AmQuestionStain">
    update am_question_stain
    set amq_id = #{amqId,jdbcType=VARCHAR},
      ams_id = #{amsId,jdbcType=VARCHAR},
      isdeleted = #{isdeleted,jdbcType=INTEGER},
      createdby = #{createdby,jdbcType=VARCHAR},
      createddate = #{createddate,jdbcType=TIMESTAMP},
      modifiedby = #{modifiedby,jdbcType=VARCHAR},
      modifieddate = #{modifieddate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="deleteByAmsId">
    update am_question_stain
    set
      isdeleted = 1,
      modifiedby = #{modifiedby,jdbcType=VARCHAR},
      modifieddate = #{modifieddate,jdbcType=TIMESTAMP}
    where id > ''
      and ams_id = #{amsId,jdbcType=VARCHAR}
  </update>
  <select id="selectByAmqId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from am_question_stain
    where isdeleted = 0
    and amq_id = #{amqId,jdbcType=VARCHAR}
  </select>
  <select id="selectAmQuestionStainListByAmId" resultType="com.cmos.pbms.beans.dto.AmQuestionStainListDTO">
    SELECT
        a.id id,
        a.amq_id amqId,
        a.ams_id amsId
    FROM
        am_question_stain a
    INNER JOIN am_question b ON a.amq_id = b.id AND b.isdeleted = 0 AND b.am_id = #{amId,jdbcType=VARCHAR}
    WHERE
        a.isdeleted = 0
  </select>
  <select id="selectAmqIdsByAmId" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT
        distinct
        b.amq_id
    FROM
        am_question a
    INNER JOIN am_question_stain b ON a.id = b.amq_id
    AND b.isdeleted = 0
    WHERE
        a.isdeleted = 0
    AND a.am_id = #{amId,jdbcType=VARCHAR}
  </select>
</mapper>