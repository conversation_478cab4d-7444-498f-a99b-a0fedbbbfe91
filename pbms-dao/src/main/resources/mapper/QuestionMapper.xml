<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmos.pbms.dao.dd.QuestionDAO">
    <resultMap id="BaseResultMap" type="com.cmos.pbms.beans.dd.Question">
        <id column="id" jdbcType="VARCHAR" property="id"></id>
        <result column="subjectid" jdbcType="VARCHAR" property="subjectid"></result>
        <result column="quename" jdbcType="VARCHAR" property="quename"></result>
        <result column="quetype" jdbcType="INTEGER" property="quetype"></result>
        <result column="question" jdbcType="VARCHAR" property="question"></result>
        <result column="ordernum" jdbcType="INTEGER" property="ordernum"></result>
        <result column="isdeleted" jdbcType="INTEGER" property="isdeleted"></result>
        <result column="createdby" jdbcType="VARCHAR" property="createdby"></result>
        <result column="createddate" jdbcType="TIMESTAMP" property="createddate"></result>
        <result column="modifiedby" jdbcType="VARCHAR" property="modifiedby"></result>
        <result column="modifieddate" jdbcType="TIMESTAMP" property="modifieddate"></result>
    </resultMap>
    <sql id="Base_Column_List">
        id, subjectid, quename, quetype, question, ordernum, isdeleted, createdby, createddate,
modifiedby, modifieddate
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from dd_question
where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectPublicBySubUser" resultType="hashmap">
        SELECT
sub.subname title,users.username,sett.profilephoto, aner.isanonymous
from dd_answerer aner
JOIN dd_subject sub on sub.id=aner.subjectid
LEFT JOIN sys_users users on users.id=aner.userid
LEFT JOIN sys_usersettings sett on sett.userid=users.id
where aner.subjectid =#{subjectid,jdbcType=VARCHAR}  and aner.userid = #{userid,jdbcType=VARCHAR}
    </select>
    <select id="selectAnswerBySubUser" resultType="hashmap">
        SELECT que.id, que.quename, que.question, que.quetype, ans.content, TO_CHAR(ans.createddate, 'YYYY-MM-DD HH24:MI') createddate, ans.replicontent, TO_CHAR(aner.replitime, 'YYYY-MM-DD HH24:MI') replitime from dd_question que join dd_answerer aner on aner.subjectid=que.subjectid and aner.userid = #{userid, jdbcType=VARCHAR} left join dd_answers ans on ans.questionid=que.id and ans.userid=aner.userid where que.subjectid = #{subjectid, jdbcType=VARCHAR}
    </select>
    <select id="selectBySubjectId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
id, quename, quetype, question, ordernum
from dd_question
where subjectid = #{subjectid,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from dd_question
where id = #{id,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteBySubjectId" parameterType="java.lang.String">
        delete from dd_question
where subjectid = #{subjectid,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.cmos.pbms.beans.dd.Question">
        insert into dd_question (id, subjectid, quename,
quetype, question, ordernum,
isdeleted, createdby, createddate,
modifiedby, modifieddate)
values (#{id,jdbcType=VARCHAR}, #{subjectid,jdbcType=VARCHAR}, #{quename,jdbcType=VARCHAR},
#{quetype,jdbcType=INTEGER}, #{question,jdbcType=VARCHAR}, #{ordernum,jdbcType=INTEGER},
#{isdeleted,jdbcType=INTEGER}, #{createdby,jdbcType=VARCHAR}, #{createddate,jdbcType=TIMESTAMP},
#{modifiedby,jdbcType=VARCHAR}, #{modifieddate,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.cmos.pbms.beans.dd.Question">
        insert into dd_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="subjectid != null">
                subjectid,
            </if>
            <if test="quename != null">
                quename,
            </if>
            <if test="quetype != null">
                quetype,
            </if>
            <if test="question != null">
                question,
            </if>
            <if test="ordernum != null">
                ordernum,
            </if>
            <if test="isdeleted != null">
                isdeleted,
            </if>
            <if test="createdby != null">
                createdby,
            </if>
            <if test="createddate != null">
                createddate,
            </if>
            <if test="modifiedby != null">
                modifiedby,
            </if>
            <if test="modifieddate != null">
                modifieddate,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="subjectid != null">
                #{subjectid,jdbcType=VARCHAR},
            </if>
            <if test="quename != null">
                #{quename,jdbcType=VARCHAR},
            </if>
            <if test="quetype != null">
                #{quetype,jdbcType=INTEGER},
            </if>
            <if test="question != null">
                #{question,jdbcType=VARCHAR},
            </if>
            <if test="ordernum != null">
                #{ordernum,jdbcType=INTEGER},
            </if>
            <if test="isdeleted != null">
                #{isdeleted,jdbcType=INTEGER},
            </if>
            <if test="createdby != null">
                #{createdby,jdbcType=VARCHAR},
            </if>
            <if test="createddate != null">
                #{createddate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedby != null">
                #{modifiedby,jdbcType=VARCHAR},
            </if>
            <if test="modifieddate != null">
                #{modifieddate,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch" parameterType="java.util.List">
        insert into dd_question (id, subjectid, quename,
quetype, question, ordernum,
isdeleted, createdby, createddate,
modifiedby, modifieddate)
values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.subjectid,jdbcType=VARCHAR}, #{item.quename,jdbcType=VARCHAR},
#{item.quetype,jdbcType=INTEGER}, #{item.question,jdbcType=VARCHAR}, #{item.ordernum,jdbcType=INTEGER},
#{item.isdeleted,jdbcType=INTEGER}, #{item.createdby,jdbcType=VARCHAR},
#{item.createddate,jdbcType=TIMESTAMP},
#{item.modifiedby,jdbcType=VARCHAR}, #{item.modifieddate,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cmos.pbms.beans.dd.Question">
        update dd_question
        <set>
            <if test="subjectid != null">
                subjectid = #{subjectid,jdbcType=VARCHAR},
            </if>
            <if test="quename != null">
                quename = #{quename,jdbcType=VARCHAR},
            </if>
            <if test="quetype != null">
                quetype = #{quetype,jdbcType=INTEGER},
            </if>
            <if test="question != null">
                question = #{question,jdbcType=VARCHAR},
            </if>
            <if test="ordernum != null">
                ordernum = #{ordernum,jdbcType=INTEGER},
            </if>
            <if test="isdeleted != null">
                isdeleted = #{isdeleted,jdbcType=INTEGER},
            </if>
            <if test="createdby != null">
                createdby = #{createdby,jdbcType=VARCHAR},
            </if>
            <if test="createddate != null">
                createddate = #{createddate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedby != null">
                modifiedby = #{modifiedby,jdbcType=VARCHAR},
            </if>
            <if test="modifieddate != null">
                modifieddate = #{modifieddate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cmos.pbms.beans.dd.Question">
        update dd_question
set subjectid = #{subjectid,jdbcType=VARCHAR},
quename = #{quename,jdbcType=VARCHAR},
quetype = #{quetype,jdbcType=INTEGER},
question = #{question,jdbcType=VARCHAR},
ordernum = #{ordernum,jdbcType=INTEGER},
isdeleted = #{isdeleted,jdbcType=INTEGER},
createdby = #{createdby,jdbcType=VARCHAR},
createddate = #{createddate,jdbcType=TIMESTAMP},
modifiedby = #{modifiedby,jdbcType=VARCHAR},
modifieddate = #{modifieddate,jdbcType=TIMESTAMP}
where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>