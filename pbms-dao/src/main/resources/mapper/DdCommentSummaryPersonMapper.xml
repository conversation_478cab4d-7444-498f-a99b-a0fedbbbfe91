<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmos.pbms.dao.dd.DdCommentSummaryPersonDAO">
    <resultMap id="BaseResultMap" type="com.cmos.pbms.beans.dd.DdCommentSummaryPerson">
        <id column="id" jdbcType="VARCHAR" property="id"></id>
        <result column="sum_dtl_id" jdbcType="VARCHAR" property="sumDtlId"></result>
        <result column="order_num" jdbcType="VARCHAR" property="orderNum"></result>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"></result>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"></result>
        <result column="user_id" jdbcType="VARCHAR" property="userId"></result>
        <result column="hrid" jdbcType="VARCHAR" property="hrId"></result>
        <result column="user_name" jdbcType="VARCHAR" property="userName"></result>
        <result column="stage" jdbcType="VARCHAR" property="stage"></result>
        <result column="province" jdbcType="VARCHAR" property="province"></result>
        <result column="sex" jdbcType="TINYINT" property="sex"></result>
        <result column="position_str" jdbcType="VARCHAR" property="positionStr"></result>
        <result column="is_join" jdbcType="TINYINT" property="isJoin"></result>
        <result column="result" jdbcType="TINYINT" property="result"></result>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"></result>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"></result>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"></result>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"></result>
        <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy"></result>
        <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate"></result>
    </resultMap>
    <sql id="Base_Column_List">
        id, sum_dtl_id, order_num, org_id, org_name, user_id, hrid, user_name, stage, province, sex, position_str, is_join, result, remarks,
is_delete, create_by, create_date, modified_by, modified_date
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from dd_ddcomment_summary_person
where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectBySumDtlIdAndHrId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from dd_ddcomment_summary_person
where sum_dtl_id = #{sumDtlId,jdbcType=VARCHAR}
and hrid = #{hrId,jdbcType=VARCHAR}
and is_delete = 0
    </select>
    <select id="selectBySumDtlId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from dd_ddcomment_summary_person
where sum_dtl_id = #{sumDtlId,jdbcType=VARCHAR}
and is_delete = 0
order by id
    </select>
    <select id="selectMaxOrderNumBySumDtlIdAndHrId" parameterType="java.lang.String" resultType="java.lang.Integer">
        select
IFNULL(max(order_num),0)
from dd_ddcomment_summary_person
where sum_dtl_id = #{sumDtlId,jdbcType=VARCHAR}
and is_delete = 0
    </select>
    <select id="selectBySummaryId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from dd_ddcomment_summary_person
where sum_dtl_id in (select id from dd_ddcomment_summary_org_relation where summary_id =
#{summaryId,jdbcType=VARCHAR} and is_delete = 0)
and is_delete = 0
order by id
    </select>
    <select id="selectAllNoHrIdBySummaryId" parameterType="java.lang.String" resultType="java.lang.String">
        select
hrid
from dd_ddcomment_summary_person
where sum_dtl_id in (select id from dd_ddcomment_summary_org_relation where summary_id =
#{summaryId,jdbcType=VARCHAR} and is_delete = 0)
and is_delete = 1
order by id
    </select>
    <select id="selectListByParam" resultType="com.cmos.pbms.beans.dto.DdCommentSummaryPersonListDTO">
        SELECT a.id, a.sum_dtl_id sumDtlId, a.order_num orderNum, a.org_id orgId, a.org_name orgName, a.user_id userId, a.hrid hrId, a.user_name userName, a.stage stage, a.province province, a.sex sex, a.position_str positionStr, a.is_join isJoin, a.result result, a.remarks remarks, e.itemtext provinceStr
FROM dd_ddcomment_summary_person a INNER JOIN (
SELECT a.id, a.summary_id, b.orgfname
FROM dd_ddcomment_summary_org_relation a, pm_organization b
WHERE a.org_id = b.id and a.is_delete = 0) b on a.sum_dtl_id = b.id <if test="null != sumDtlId and '' != sumDtlId">and b.id = #{sumDtlId, jdbcType=VARCHAR}</if> INNER JOIN dd_ddcomment_summary c on b.summary_id = c.id and c.is_delete = 0 and c.id = #{summaryId, jdbcType=VARCHAR} INNER JOIN (
SELECT a.codestr, a.itemtext
FROM sys_dictionaryitems a, sys_dictionaries b
WHERE a.dictionaryid = b.id
AND a.isdeleted = 0
AND b.isdeleted = 0
AND b.codestr = 'PROVINCE') e on a.province = e.codestr
WHERE a.is_delete = 0 <if test="null != result">
AND a.result = #{result, jdbcType=TINYINT} </if> <if test="null != isJoin">
AND a.is_join = #{isJoin, jdbcType=TINYINT} </if> <if test="null != orgId">
AND a.org_id = #{orgId, jdbcType=VARCHAR} </if> <if test="null != userName and '' != userName"> and a.user_name LIKE '%' || #{userName, jdbcType=VARCHAR} || '%' </if> order by a.id
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from dd_ddcomment_summary_person
where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.cmos.pbms.beans.dd.DdCommentSummaryPerson">
        insert into dd_ddcomment_summary_person (id, sum_dtl_id, order_num, org_id, org_name,
user_id, hrid, user_name, stage, province, sex,
position_str, is_join, result,
remarks, is_delete, create_by,
create_date, modified_by, modified_date
)
values (#{id,jdbcType=VARCHAR}, #{sumDtlId,jdbcType=VARCHAR}, #{orderNum,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR},
#{userId,jdbcType=VARCHAR}, #{hrId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{stage,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{sex,jdbcType=TINYINT},
#{positionStr,jdbcType=VARCHAR}, #{isJoin,jdbcType=TINYINT}, #{result,jdbcType=TINYINT},
#{remarks,jdbcType=VARCHAR}, #{isDelete,jdbcType=TINYINT}, #{createBy,jdbcType=VARCHAR},
#{createDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}
)
    </insert>
    <insert id="insertBatch" parameterType="java.util.List">
        insert into dd_ddcomment_summary_person (id, sum_dtl_id, order_num, org_id, org_name,
user_id, hrid, user_name, stage, province, sex,
position_str, is_join, result,
remarks, is_delete, create_by,
create_date)
values
        <foreach collection="ddCommentSummaryPeoples" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.sumDtlId,jdbcType=VARCHAR}, #{item.orderNum,jdbcType=VARCHAR},
#{item.orgId,jdbcType=VARCHAR}, #{item.orgName,jdbcType=VARCHAR},
#{item.userId,jdbcType=VARCHAR}, #{item.hrId,jdbcType=VARCHAR}, #{item.userName,jdbcType=VARCHAR},
#{item.stage,jdbcType=VARCHAR}, #{item.province,jdbcType=VARCHAR}, #{item.sex,jdbcType=TINYINT},
#{item.positionStr,jdbcType=VARCHAR}, #{item.isJoin,jdbcType=TINYINT}, #{item.result,jdbcType=TINYINT},
#{item.remarks,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=TINYINT}, #{item.createBy,jdbcType=VARCHAR},
#{item.createDate,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertSelective" parameterType="com.cmos.pbms.beans.dd.DdCommentSummaryPerson">
        insert into dd_ddcomment_summary_person
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="sumDtlId != null">
                sum_dtl_id,
            </if>
            <if test="orderNum != null">
                order_num,
            </if>
            <if test="orgId != null">
                org_id,
            </if>
            <if test="orgName != null">
                org_name,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="hrId != null">
                hrid,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="stage != null">
                stage,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="positionStr != null">
                position_str,
            </if>
            <if test="isJoin != null">
                is_join,
            </if>
            <if test="result != null">
                result,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createDate != null">
                create_date,
            </if>
            <if test="modifiedBy != null">
                modified_by,
            </if>
            <if test="modifiedDate != null">
                modified_date,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="sumDtlId != null">
                #{sumDtlId,jdbcType=VARCHAR},
            </if>
            <if test="orderNum != null">
                #{orderNum,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="hrId != null">
                #{hrId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="stage != null">
                #{stage,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=TINYINT},
            </if>
            <if test="positionStr != null">
                #{positionStr,jdbcType=VARCHAR},
            </if>
            <if test="isJoin != null">
                #{isJoin,jdbcType=TINYINT},
            </if>
            <if test="result != null">
                #{result,jdbcType=TINYINT},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedBy != null">
                #{modifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="modifiedDate != null">
                #{modifiedDate,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cmos.pbms.beans.dd.DdCommentSummaryPerson">
        update dd_ddcomment_summary_person
        <set>
            <if test="sumDtlId != null">
                sum_dtl_id = #{sumDtlId,jdbcType=VARCHAR},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null">
                org_id = #{orgId,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                org_name = #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="hrId != null">
                hrid = #{hrId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="stage != null">
                stage = #{stage,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                sex = #{sex,jdbcType=TINYINT},
            </if>
            <if test="positionStr != null">
                position_str = #{positionStr,jdbcType=VARCHAR},
            </if>
            <if test="isJoin != null">
                is_join = #{isJoin,jdbcType=TINYINT},
            </if>
            <if test="result != null">
                result = #{result,jdbcType=TINYINT},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                create_date = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedBy != null">
                modified_by = #{modifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="modifiedDate != null">
                modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="baseUpdateByPrimaryKeySelective" parameterType="com.cmos.pbms.beans.dd.DdCommentSummaryPerson">
        update dd_ddcomment_summary_person
        <set>
            <if test="sumDtlId != null">
                sum_dtl_id = #{sumDtlId,jdbcType=VARCHAR},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null">
                org_id = #{orgId,jdbcType=VARCHAR},
            </if>
            org_name = #{orgName,jdbcType=VARCHAR},
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="hrId != null">
                hrid = #{hrId,jdbcType=VARCHAR},
            </if>
            user_name = #{userName,jdbcType=VARCHAR},
stage = #{stage,jdbcType=VARCHAR},
            <if test="province != null">
                province = #{province,jdbcType=VARCHAR},
            </if>
            sex = #{sex,jdbcType=TINYINT},
position_str = #{positionStr,jdbcType=VARCHAR},
is_join = #{isJoin,jdbcType=TINYINT},
result = #{result,jdbcType=TINYINT},
remarks = #{remarks,jdbcType=VARCHAR},
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                create_date = #{createDate,jdbcType=TIMESTAMP},
            </if>
            modified_by = #{modifiedBy,jdbcType=VARCHAR},
modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cmos.pbms.beans.dd.DdCommentSummaryPerson">
        update dd_ddcomment_summary_person
set sum_dtl_id = #{sumDtlId,jdbcType=VARCHAR},
order_num = #{orderNum,jdbcType=VARCHAR},
org_id = #{orgId,jdbcType=VARCHAR},
org_name = #{orgName,jdbcType=VARCHAR},
user_id = #{userId,jdbcType=VARCHAR},
hrid = #{hrId,jdbcType=VARCHAR},
user_name = #{userName,jdbcType=VARCHAR},
stage = #{stage,jdbcType=VARCHAR},
province = #{province,jdbcType=VARCHAR},
sex = #{sex,jdbcType=TINYINT},
position_str = #{positionStr,jdbcType=VARCHAR},
is_join = #{isJoin,jdbcType=TINYINT},
result = #{result,jdbcType=TINYINT},
remarks = #{remarks,jdbcType=VARCHAR},
is_delete = #{isDelete,jdbcType=TINYINT},
create_by = #{createBy,jdbcType=VARCHAR},
create_date = #{createDate,jdbcType=TIMESTAMP},
modified_by = #{modifiedBy,jdbcType=VARCHAR},
modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="deleteBySummaryId">
        update dd_ddcomment_summary_person
set is_delete = 1,
modified_by = #{modifiedBy,jdbcType=VARCHAR},
modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
where id > ''
and sum_dtl_id in (select id from dd_ddcomment_summary_org_relation where summary_id= #{summaryId,jdbcType=VARCHAR})
    </update>
</mapper>