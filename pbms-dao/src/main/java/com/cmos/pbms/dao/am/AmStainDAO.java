package com.cmos.pbms.dao.am;

import com.cmos.pbms.beans.am.AmStain;
import com.cmos.pbms.beans.dto.AmStainListDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AmStainDAO {
    int deleteByPrimaryKey(String id);

    int insert(AmStain amStain);

    int insertSelective(AmStain amStain);

    AmStain selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(AmStain amStain);

    int updateByPrimaryKey(AmStain amStain);

    List<AmStainListDTO> selectAmStainListByAmId(@Param("amId") String amId, @Param("content") String content);
}