package com.cmos.pbms.dao.common;

import com.cmos.pbms.beans.common.OnestTempInfo;
import com.cmos.pbms.beans.dto.OnestTransferDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface OnestTempInfoDAO {
    int deleteByPrimaryKey(String id);

    int deleteByDataType(String dataType);

    int insert(OnestTempInfo record);

    int insertSelective(OnestTempInfo record);

    OnestTempInfo selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(OnestTempInfo record);

    int updateByPrimaryKey(OnestTempInfo record);

    int insertBatch(List<OnestTempInfo> onestList);

    List<OnestTransferDTO> selectNoTransfer(Map<String, Object> params);

    List<OnestTempInfo> selectByDataType(@Param("dataType") String dataType);

    int updateDataByParam(Map<String, Object> params);

    List<OnestTransferDTO> selectDataByTableAndColumn(@Param("tableName") String tableName, @Param("columnName") String columnName, @Param("bucketName") String bucketName);

    int deleteByTableAndColumn(@Param("tableName") String tableName, @Param("columnName") String columnName);

    List<OnestTempInfo> selectByDataTypeAndTinyType(@Param("tableName") String tableName, @Param("columnName") String columnName);
}