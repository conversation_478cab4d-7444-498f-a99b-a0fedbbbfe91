package com.cmos.pbms.dao.me;

import com.cmos.pbms.beans.dto.PlanLogsDTO;
import com.cmos.pbms.beans.me.PlanLogs;

import java.util.List;
import java.util.Map;

public interface PlanLogsDAO {
    int deleteByPrimaryKey(String id);

    int insert(PlanLogs record);

    int insertSelective(PlanLogs record);

    int insertList(List<PlanLogs> list);

    PlanLogs selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(PlanLogs record);

    int updateByPrimaryKey(PlanLogs record);

    /**
     * @方法名：selectListByParams
     * @方法作用：根据条件分页查询三会一课配置变更日志
     * @方法参数：Map<String,Object>
     * @返回结果：List<PlanLogsDTO>
     * @作者：牛文钻
     * @日期：2018/7/2
     */
    List<PlanLogsDTO> selectListByParams(Map<String, Object> params);
}