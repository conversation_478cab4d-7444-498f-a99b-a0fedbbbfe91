package com.cmos.pbms.dao.tc;

import com.cmos.pbms.beans.dto.CountThusAndReviews;
import com.cmos.pbms.beans.dto.TcTopicInfoListDTO;
import com.cmos.pbms.beans.dto.TcTopicInfoSimpleDTO;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.beans.tc.TcTopicInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TcTopicInfoDAO {
    int deleteByPrimaryKey(String id);

    int insert(TcTopicInfo tcTopicInfo);

    int insertSelective(TcTopicInfo tcTopicInfo);

    TcTopicInfo selectByPrimaryKey(String id);

    List<TcTopicInfoSimpleDTO> selectNoExpertTeamAnswer(@Param("expertTeam") List<Users> expertTeam, @Param("createdDate") Date createdDate);

    int updateByPrimaryKeySelective(TcTopicInfo tcTopicInfo);

    int updateLastNotifyTimeByPrimaryKey(@Param("lastNotifyTime") Date lastNotifyTime, @Param("id") String id);

    int updateByPrimaryKey(TcTopicInfo tcTopicInfo);

    List<TcTopicInfoListDTO> selectByTypeAndTitle(@Param("type") String type, @Param("currentUserId") String currentUserId, @Param("topicType") String topicType, @Param("title") String title, @Param("parentId") String parentId);

    TcTopicInfoListDTO selectById(@Param("id") String id, @Param("currentUserId") String currentUserId);

    List<CountThusAndReviews> selectCountThusAndReviewsAndCollection(@Param("list") List<TcTopicInfoListDTO> list, @Param("currentUserId") String currentUserId);

    int addThumbAndReview(@Param("id") String id, @Param("thumbCount") Integer thumbCount, @Param("reviewsCount") Integer reviewsCount, @Param("readCount") Integer readCount, @Param("shireCount") Integer shireCount);

    void addThumbAndReviewForReviews(@Param("reviewId") String reviewId, @Param("thumbCount") Integer thumbCount, @Param("reviewsCount") Integer reviewsCount);
}