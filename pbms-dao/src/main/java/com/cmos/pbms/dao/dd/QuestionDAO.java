package com.cmos.pbms.dao.dd;

import com.cmos.pbms.beans.dd.Question;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface QuestionDAO {
    int deleteByPrimaryKey(String id);

    int deleteBySubjectId(String subjectid);

    int insert(Question record);

    int insertSelective(Question record);

    int insertBatch(List<Question> list);

    Question selectByPrimaryKey(String id);

    List<Map<String, Object>> selectAnswerBySubUser(@Param("subjectid") String subjectid, @Param("userid") String userid);

    Map<String, Object> selectPublicBySubUser(@Param("subjectid") String subjectid, @Param("userid") String userid);

    List<Question> selectBySubjectId(String subjectid);

    int updateByPrimaryKeySelective(Question record);

    int updateByPrimaryKey(Question record);

}