package com.cmos.pbms.dao.news;

import com.cmos.pbms.beans.news.SpecialNews;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface SpecialNewsDAO {
    int deleteByPrimaryKey(String id);

    int deleteBatchBySpecialId(String specid);

    int delRelateByNewsid(@Param("specialid") String specialid, @Param("newsid") String newsid);

    int insert(SpecialNews record);

    int insertSelective(SpecialNews record);

    int insertBatch(List<SpecialNews> specialNewsList);

    SpecialNews selectByPrimaryKey(String id);

    List<SpecialNews> selectBySpecialId(String specialid);

    int updateByPrimaryKeySelective(SpecialNews record);

    int updateByPrimaryKey(SpecialNews record);

    SpecialNews selectBySpecialIdAndNewsId(@Param("specialId") String specialId, @Param("newsId") String newsId);

    int deleteByNewsId(@Param("newsId") String newsId, @Param("modifiedby") String modifiedby, @Param("modifieddate") Date modifieddate);
}