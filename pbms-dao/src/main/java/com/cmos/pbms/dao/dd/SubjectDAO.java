package com.cmos.pbms.dao.dd;

import com.cmos.pbms.beans.dd.Subject;
import com.cmos.pbms.beans.dto.BranchInfoCountReportDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface SubjectDAO {
    int deleteByPrimaryKey(String id);

    int deleteRealByPrimaryKey(String id);

    int insert(Subject record);

    int insertSelective(Subject record);

    Map<String, Object> selectByPrimaryKey(String id);

    Subject selectBeanByPrimaryKey(String id);

    Map<String, Object> selectByIdForAPP(String id);

    List<Map<String, Object>> selectMemberById(String id);

    List<Subject> selectAll();

    List<Subject> selectByCondition(Map<String, Object> params);

    int updateByPrimaryKeySelective(Subject record);

    int updateByPrimaryKey(Subject record);

    List<BranchInfoCountReportDTO> selectDypyList(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
}