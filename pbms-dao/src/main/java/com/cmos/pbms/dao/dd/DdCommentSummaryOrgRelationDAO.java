package com.cmos.pbms.dao.dd;

import com.cmos.pbms.beans.dd.DdCommentSummaryOrgRelation;
import com.cmos.pbms.beans.dto.DdCommentSummaryOrgRelationDetailDTO;
import com.cmos.pbms.beans.dto.DdCommentSummaryOrgRelationListDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface DdCommentSummaryOrgRelationDAO {
    int deleteByPrimaryKey(String id);

    int deleteBySummaryId(@Param("summaryId") String summaryId, @Param("modifiedBy") String modifiedBy, @Param("modifiedDate") Date modifiedDate);

    int insert(DdCommentSummaryOrgRelation ddCommentSummaryOrgRelation);

    int insertSelective(DdCommentSummaryOrgRelation ddCommentSummaryOrgRelation);

    DdCommentSummaryOrgRelation selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(DdCommentSummaryOrgRelation ddCommentSummaryOrgRelation);

    int updateByPrimaryKey(DdCommentSummaryOrgRelation ddCommentSummaryOrgRelation);

    Integer insertBatch(@Param("ddCommentSummaryOrgRelations") List<DdCommentSummaryOrgRelation> ddCommentSummaryOrgRelations);

    List<DdCommentSummaryOrgRelationListDTO> selectListDTOBySummaryId(@Param("summaryId") String summaryId, @Param("sumDtlId") String sumDtlId, @Param("taskStatus") Integer taskStatus, @Param("orgId") String orgId);

    List<DdCommentSummaryOrgRelationListDTO> selectListDTOByOrgId(@Param("orgId") String orgId, @Param("topic") String topic, @Param("commentYear") String commentYear, @Param("taskStatus") Integer taskStatus);

    List<DdCommentSummaryOrgRelationListDTO> selectListDTOByOrgIdToReview(@Param("orgId") String orgId, @Param("topic") String topic, @Param("commentYear") String commentYear, @Param("taskStatus") Integer taskStatus);

    DdCommentSummaryOrgRelationDetailDTO selectDdCommentSummaryOrgRelationDetail(@Param("sumDtlId") String sumDtlId);
}