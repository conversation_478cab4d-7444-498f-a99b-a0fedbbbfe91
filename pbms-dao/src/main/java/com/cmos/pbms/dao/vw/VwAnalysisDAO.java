package com.cmos.pbms.dao.vw;

import com.cmos.pbms.beans.vw.VwAnalysis;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VwAnalysisDAO {
    int deleteByPrimaryKey(String id);

    int insert(VwAnalysis vwAnalysis);

    int insertBatch(@Param("vwAnalyses") List<VwAnalysis> vwAnalyses);

    int insertSelective(VwAnalysis vwAnalysis);

    VwAnalysis selectByPrimaryKey(String id);

    List<VwAnalysis> selectByQdatadate(@Param("qDatadate") String qDatadate);

    List<VwAnalysis> selectBaseByQsId(@Param("qsId") String qsId);

    int updateByPrimaryKeySelective(VwAnalysis vwAnalysis);

    int updateByPrimaryKey(VwAnalysis vwAnalysis);
}