package com.cmos.pbms.dao.bp;

import com.cmos.pbms.beans.bp.BpPartyCommando;
import com.cmos.pbms.beans.dto.BpPartyCommandoDetailDTO;
import com.cmos.pbms.beans.dto.BpPartyCommandoListDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface BpPartyCommandoDAO {
    int deleteByPrimaryKey(String id);

    int insert(BpPartyCommando bpPartyCommando);

    int insertSelective(BpPartyCommando bpPartyCommando);

    BpPartyCommando selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(BpPartyCommando bpPartyCommando);

    int updateByPrimaryKey(BpPartyCommando bpPartyCommando);

    List<BpPartyCommandoListDTO> selectBpPartyCommandoList(@Param("bpcTitle") String bpcTitle,
                                                           @Param("orgId") String orgId,
                                                           @Param("bpcDuty") String bpcDuty,
                                                           @Param("bpcVision") String bpcVision,
                                                           @Param("startDate") Date startDate,
                                                           @Param("endDate") Date endDate,
                                                           @Param("orgCode") String orgCode);

    List<BpPartyCommandoListDTO> selectBpPartyCommandoListForApp(@Param("orgId") String orgId, @Param("queryDate") Date queryDate);

    int updateIsTop(@Param("id") String id, @Param("modifiedby") String modifiedby, @Param("modifieddate") Date modifieddate);

    BpPartyCommandoDetailDTO selectBpPartyCommandoDetail(String id);
}