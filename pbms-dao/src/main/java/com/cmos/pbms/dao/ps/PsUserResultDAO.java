package com.cmos.pbms.dao.ps;

import com.cmos.pbms.beans.dto.PsUserScoreDTO;
import com.cmos.pbms.beans.ps.PsUserResult;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface PsUserResultDAO {
    int deleteByPrimaryKey(String id);

    int insert(PsUserResult psUserResult);

    int insertSelective(PsUserResult psUserResult);

    PsUserResult selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(PsUserResult psUserResult);

    int updateByPrimaryKey(PsUserResult psUserResult);

    int insertBatch(@Param("psUserResultList") List<PsUserResult> psUserResultList);

    void deleteByScoreDateLogic(@Param("scoreDate") Integer scoreDate, @Param("modifiedby") String modifiedby, @Param("modifieddate") Date modifieddate);

    void deleteByPudsId(@Param("pudsId") String pudsId, @Param("modifiedby") String modifiedby, @Param("modifieddate") Date modifieddate);

    List<PsUserScoreDTO> selectUserScoreDetail(@Param("pudsId") String pudsId, @Param("itemLevel") Integer itemLevel);
}