package com.cmos.pbms.dao.rep;

import com.cmos.pbms.beans.rep.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ReportDao {
    /**
     * 读书活动发起次数排行Top100
     */
    List<ScoreRanking> selectScoreRanking(Map<String, Object> params);

    /**
     * 新闻评论数、点赞数Top100
     */
    List<CommentThumbs> selectCommentThumbs(Map<String, Object> params);

    /**
     * 新闻评论人员清单
     */
    List<UserCommentThumbs> selectUserComments(@Param("sponsor") String sponsor, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 新闻点赞人员清单
     */
    List<UserCommentThumbs> selectUserThumbs(@Param("sponsor") String sponsor, @Param("startTime") Date startTime, @Param("endTime") Date endTime);


    /**
     * 支部党员党课参与情况
     */
    List<LectureParticipation> selectLectureParticipation(Map<String, Object> params);

    /**
     * 优秀学习心得数排行Top100
     */
    List<ExcellentLearning> selectExcellentLearning(Map<String, Object> params);

    /**
     * 优秀读书笔记数排行Top100
     */
    List<ExcellentLearning> selectExcellentNotes(Map<String, Object> params);

    /**
     * 年度单次活动参与人数排行top100
     */
    List<CeParticipate> selectCeParticipate(Map<String, Object> params);

}
