package com.cmos.pbms.dao.sys;

import com.cmos.pbms.beans.dto.SysPublicConfigListDTO;
import com.cmos.pbms.beans.sys.SysPublicConfig;

import java.util.List;

public interface SysPublicConfigDAO {
    int deleteByPrimaryKey(String id);

    int insert(SysPublicConfig sysPublicConfig);

    int insertSelective(SysPublicConfig sysPublicConfig);

    SysPublicConfig selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(SysPublicConfig sysPublicConfig);

    int updateByPrimaryKey(SysPublicConfig sysPublicConfig);

    List<SysPublicConfigListDTO> selectSysPublicConfigList();
}