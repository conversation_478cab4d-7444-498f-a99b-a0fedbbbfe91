package com.cmos.pbms.dao.rep;

import com.cmos.pbms.beans.rep.ReportRecord;
import com.cmos.pbms.beans.rep.Statistics;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface StatisticsDao {

    /****
     * 获取一段时间的新闻发布量数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 各个党组织的新闻发布量数据汇总
     */
    List<Statistics> getNewsPublicationData(@Param("startTime") Date startTime,
                                            @Param("endTime") Date endTime);

    /****
     * 获取一段时间的党课开办期数数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 各个党组织的党课开办期数数据汇总
     */
    List<Statistics> getLectureSetupPeriodsData(@Param("startTime") Date startTime,
                                                @Param("endTime") Date endTime);

    /****
     * 获取一段时间的读书活动发起次数数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 各个党组织的读书活动发起次数数据汇总
     */
    List<Statistics> getReadingFrequencyData(@Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime);

    /****
     * 获取一段时间的社区活动发起次数数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 各个党组织的社区活动发起次数数据汇总
     */
    List<Statistics> getActivitiesFrequencyData(@Param("startTime") Date startTime,
                                                @Param("endTime") Date endTime);

    /****
     * 依据指标ID、统计年份、统计月份获取统计数据记录的ID值
     * @param indicatorId    指标ID
     * @param indicatorYear  统计年份
     * @param indicatorMonth 统计月份
     * @return 原记录的ID值
     */
    List<String> getRecordIds(@Param("indicatorId") String indicatorId,
                              @Param("indicatorYear") Integer indicatorYear,
                              @Param("indicatorMonth") Integer indicatorMonth);

    /***
     * 获取某一指标指定月度的统计数据，并依据所属分公司进行汇总
     * @param indicatorcode 指标编码
     * @param indicatoryear 统计年份
     * @param indicatormonth 统计月份
     * @param orgcode 权限范围
     * @return 报表统计数据
     */
    List<ReportRecord> getRootRepByMonth(@Param("indicatorcode") String indicatorcode,
                                         @Param("indicatoryear") int indicatoryear,
                                         @Param("indicatormonth") int indicatormonth,
                                         @Param("orgcode") String orgcode);

    /***
     * 获取指定分公司下各级党组织某一指标指定月份的统计数据，
     * @param comid 分公司党组织id
     * @param indicatorcode 指标编码
     * @param indicatoryear 统计年份
     * @param indicatormonth 统计月份
     * @param orgcode 权限范围
     * @return 报表统计数据
     */
    List<ReportRecord> getComRepByMonth(@Param("comid") String comid,
                                        @Param("indicatorcode") String indicatorcode,
                                        @Param("indicatoryear") int indicatoryear,
                                        @Param("indicatormonth") int indicatormonth,
                                        @Param("orgcode") String orgcode);

    /***
     * 获取某一指标指定年度的统计数据，并依据所属分公司进行汇总
     * @param indicatorcode 指标编码
     * @param indicatoryear 统计年份
     * @param orgcode 权限范围
     * @return 报表统计数据
     */
    List<ReportRecord> getRootRepByYear(@Param("indicatorcode") String indicatorcode,
                                        @Param("indicatoryear") int indicatoryear,
                                        @Param("orgcode") String orgcode);

    /***
     * 获取指定分公司下各级党组织某一指标指定年度的统计数据，
     * @param comid 分公司党组织id
     * @param indicatorcode 指标编码
     * @param indicatoryear 统计年份
     * @param orgcode 权限范围
     * @return 报表统计数据
     */
    List<ReportRecord> getComRepByYear(@Param("comid") String comid,
                                       @Param("indicatorcode") String indicatorcode,
                                       @Param("indicatoryear") int indicatoryear,
                                       @Param("orgcode") String orgcode);

    /***
     * 依据ID集合删除记录
     * @param ids 待删除的记录ID集合
     * @return 受影响的行数
     */
    int deleteByIds(List<String> ids);

    /***
     * 指入统计结果记录
     * @param record
     * @return
     */
    int insertRecord(Statistics record);
}
