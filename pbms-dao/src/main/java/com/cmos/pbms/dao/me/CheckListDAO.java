package com.cmos.pbms.dao.me;

import com.cmos.pbms.beans.dto.CheckListDTO;
import com.cmos.pbms.beans.me.CheckList;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CheckListDAO {
    int deleteByParams(@Param("id") String id, @Param("userid") String userid, @Param("modifieddate") Date modifieddate);

    int insert(CheckList record);

    int insertSelective(CheckList record);

    CheckList selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CheckList record);

    int updateByPrimaryKey(CheckList record);

    List<CheckListDTO> selectCheckListByParams(Map<String, Object> params);

    List<CheckList> selectCheckInfoByMeetingAndAuthor(@Param("meetingid") String meetingid, @Param("createdby") String createdby);

    List<String> selectIDsByMeetingId(String meetingId);

    /**
     * @方法名：deleteByMeetingId
     * @方法作用：根据会议id逻辑删除相关信息
     * @方法参数：会议ID
     * @返回结果：
     * @作者：牛文钻
     * @日期：2018/9/26
     */
    void deleteByMeetingId(@Param("ids") List<String> ids, @Param("meetingId") String meetingId);

    int recoveryByMeetingId(@Param("meetingId") String meetingId, @Param("modifiedby") String modifiedby, @Param("modifieddate") Date modifieddate);
}