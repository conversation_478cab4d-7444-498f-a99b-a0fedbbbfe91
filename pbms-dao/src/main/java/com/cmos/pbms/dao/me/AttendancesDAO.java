package com.cmos.pbms.dao.me;

import com.cmos.pbms.beans.me.Attendances;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AttendancesDAO {
    int deleteByPrimaryKey(String id);

    int insert(Attendances record);

    int insertSelective(Attendances record);

    Attendances selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(Attendances record);

    int updateByPrimaryKey(Attendances record);

    List<Attendances> selectByMeetingId(@Param("meetingId") String meetingId, @Param("isPersonAll") Boolean isPersonAll, @Param("personType") String personType, @Param("belongMeeting") Integer belongMeeting, @Param("attendancesPersonType") Integer attendancesPersonType);

    int insertCollect(@Param("attendancesList") List<Attendances> attendancesList);

    int deleteByCollect(@Param("meetingId") String meetingId);

    List<Attendances> selectByIdCollection(@Param("ids") List<String> ids);

    int deleteByMeetingIdAndUserId(@Param("meetingId") String meetingId, @Param("userIdList") List<String> userIdList);

    int deleteParticipantsByMeetingId(@Param("meetingId") String meetingId);
}
