package com.cmos.pbms.dao.st;

import com.cmos.pbms.beans.resumption.VwQuestionTmp;
import com.cmos.pbms.beans.resumption.VwQuestionTmpDTO;
import com.cmos.pbms.beans.vw.VwQuestionItem;

import java.util.List;

public interface SelfTestingDAO{
    List<VwQuestionTmp> selectVwQuestionTmp(VwQuestionTmp vwQuestionTmp);

    VwQuestionTmp getVwQuestionTmpBYId(String id);

    Integer updateQuestionTmp(VwQuestionTmpDTO questionTmp);

    List<VwQuestionItem> getVwQuestionItemTmpByqId(String qId);
    //
    void deleteVwQuestionItemTmpByqId(String qId);
}
