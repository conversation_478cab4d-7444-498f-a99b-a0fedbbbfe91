package com.cmos.pbms.dao.bh;

import com.cmos.pbms.beans.bh.BhOrgHonor;
import com.cmos.pbms.beans.dto.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface BhOrgHonorDAO {
    int deleteByPrimaryKey(String id);

    int insert(BhOrgHonor bhOrgHonor);

    int insertSelective(BhOrgHonor bhOrgHonor);

    BhOrgHonor selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(BhOrgHonor bhOrgHonor);

    int updateByPrimaryKey(BhOrgHonor bhOrgHonor);

    List<AuditBhOrgHonorListDTO> selectAuditBhOrgHonorList(@Param("currentUserId") String currentUserId, @Param("currentUserRoleId") String currentUserRoleId, @Param("honorName") String honorName, @Param("issuerType") String issuerType, @Param("bhState") Integer bhState, @Param("orgId") String orgId);

    AuditBhOrgHonorDetailDTO selectAuditBhOrgHonorDetailById(String id);

    List<BhOrgHonorListDTO> selectBhOrgHonorList(@Param("honorName") String honorName, @Param("issuerType") String issuerType, @Param("bhState") Integer bhState, @Param("orgId") String orgId);

    BhOrgHonorDetailDTO selectBhOrgHonorDetailById(String id);

    List<BhOrgHonor> selectOrgHonorByOrgIdAndDate(@Param("orgId") String orgId, @Param("currTime") Date currTime);

    List<BhOrgHonor> selectOrgHonorByOrgIdAndDateScope(@Param("orgId") String orgId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<CountDTO> selectCountDTOByOrgIdAndDate(@Param("orgId") String orgId, @Param("date") Date date);
}