package com.cmos.pbms.beans.an.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
public class AnTaskItemVo implements Serializable {
    private Integer num;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date itemTaskEndTime;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date itemTaskStartTime;
    private String itemTaskType;
    private String itemTaskName;
    private String itemTaskRequirement;
    private String tag;
    private String amid;
    private String amQuestionId;
    private String createdby;
    private String taskQuarter;
    private String templateStartTime;
    private String templateEndTime;
}
