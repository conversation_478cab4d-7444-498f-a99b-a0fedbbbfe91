package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

import java.util.ArrayList;
import java.util.List;

public class BranchInfoCountReportDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String itemName;

    private String specname;

    private String category;

    private Integer objType;

    private Integer public_count;

    private Integer read_count;

    private Integer public_all_count;

    private Integer read_all_count;

    private List<BranchInfoCountReportDTO> children = null;

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getSpecname() {
        return specname;
    }

    public void setSpecname(String specname) {
        this.specname = specname;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Integer getObjType() {
        return objType;
    }

    public void setObjType(Integer objType) {
        this.objType = objType;
    }

    public Integer getPublic_count() {
        return public_count;
    }

    public void setPublic_count(Integer public_count) {
        this.public_count = public_count;
    }

    public Integer getRead_count() {
        return read_count;
    }

    public void setRead_count(Integer read_count) {
        this.read_count = read_count;
    }

    public Integer getPublic_all_count() {
        return public_all_count;
    }

    public void setPublic_all_count(Integer public_all_count) {
        this.public_all_count = public_all_count;
    }

    public Integer getRead_all_count() {
        return read_all_count;
    }

    public void setRead_all_count(Integer read_all_count) {
        this.read_all_count = read_all_count;
    }

    public List<BranchInfoCountReportDTO> getChildren() {
        return children;
    }

    public void setChildren(List<BranchInfoCountReportDTO> children) {
        this.children = children;
    }

    public void addChildren(BranchInfoCountReportDTO child) {
        if (null == children)
            children = new ArrayList<>();
        this.children.add(child);
    }

    @Override
    public String toString() {
        return "BranchInfoCountReportDTO{" +
                "itemName='" + itemName + '\'' +
                ", specname='" + specname + '\'' +
                ", category='" + category + '\'' +
                ", public_count=" + public_count +
                ", read_count=" + read_count +
                ", public_all_count=" + public_all_count +
                ", read_all_count=" + read_all_count +
                ", children=" + children +
                '}';
    }
}
