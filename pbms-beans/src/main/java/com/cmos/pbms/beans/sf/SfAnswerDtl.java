package com.cmos.pbms.beans.sf;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

public class SfAnswerDtl extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    private String sfcId;

    private String sfqId;

    private Integer optionValue;

    private Integer isdeleted;

    private String createdby;

    private Date createddate;

    private String modifiedby;

    private Date modifieddate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSfcId() {
        return sfcId;
    }

    public void setSfcId(String sfcId) {
        this.sfcId = sfcId;
    }

    public String getSfqId() {
        return sfqId;
    }

    public void setSfqId(String sfqId) {
        this.sfqId = sfqId;
    }

    public Integer getOptionValue() {
        return optionValue;
    }

    public void setOptionValue(Integer optionValue) {
        this.optionValue = optionValue;
    }

    public Integer getIsdeleted() {
        return isdeleted;
    }

    public void setIsdeleted(Integer isdeleted) {
        this.isdeleted = isdeleted;
    }

    public String getCreatedby() {
        return createdby;
    }

    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    public Date getCreateddate() {
        return createddate;
    }

    public void setCreateddate(Date createddate) {
        this.createddate = createddate;
    }

    public String getModifiedby() {
        return modifiedby;
    }

    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }
}