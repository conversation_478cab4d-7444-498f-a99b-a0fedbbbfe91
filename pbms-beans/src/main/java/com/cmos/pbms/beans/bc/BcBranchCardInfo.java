package com.cmos.pbms.beans.bc;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

public class BcBranchCardInfo extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    private String dataDate;

    private String title;

    private Date endDate;

    private String fortressDesc;

    private Integer isdeleted;

    private String createdby;

    private Date createddate;

    private String modifiedby;

    private Date modifieddate;

    private Integer isExtract;

    private Integer isUserScore;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDataDate() {
        return dataDate;
    }

    public void setDataDate(String dataDate) {
        this.dataDate = dataDate;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getFortressDesc() {
        return fortressDesc;
    }

    public void setFortressDesc(String fortressDesc) {
        this.fortressDesc = fortressDesc;
    }

    public Integer getIsdeleted() {
        return isdeleted;
    }

    public void setIsdeleted(Integer isdeleted) {
        this.isdeleted = isdeleted;
    }

    public String getCreatedby() {
        return createdby;
    }

    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    public Date getCreateddate() {
        return createddate;
    }

    public void setCreateddate(Date createddate) {
        this.createddate = createddate;
    }

    public String getModifiedby() {
        return modifiedby;
    }

    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }

    public Integer getIsExtract() {
        return isExtract;
    }

    public void setIsExtract(Integer isExtract) {
        this.isExtract = isExtract;
    }

    public Integer getIsUserScore() {
        return isUserScore;
    }

    public void setIsUserScore(Integer isUserScore) {
        this.isUserScore = isUserScore;
    }
}