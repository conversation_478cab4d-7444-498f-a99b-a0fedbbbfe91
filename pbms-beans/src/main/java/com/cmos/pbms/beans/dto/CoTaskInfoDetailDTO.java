package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;
import com.cmos.pbms.beans.common.Attachments;

import java.util.Date;
import java.util.List;

/**
 * @ Author     ：liqs
 * @ Date       ：Created in 11:25 2019/4/30
 * @ Description：${description}
 * @ Modified By：
 * @Version: $version$
 */
public class CoTaskInfoDetailDTO extends GenericBean {
    private String id;

    private String taskName;

    private String createOrgId;

    private String createUserId;

    private String createUserName;

    private Integer taskStats;

    private String taskType;

    private String dataType;

    private String dataTemplate;

    private String dataTemplateName;

    private String participantType;

    private Integer isNotify;

    private Date lastNotifyTime;

    private Integer notifyTime;

    private Date requestFinishedTime;

    private Integer emergencyLevel;

    private String sheetEffectiveRows;

    private String requirement;

    private String participantIds;

    private String participantLabels;

    private Integer isDistribute;

    private String distributeId;

    private Integer isDelete;

    private String createdby;

    private Date createddate;

    private String modifiedby;

    private Date modifieddate;

    private List<CoTaskDetailListDTO> coTaskInfoDetailDTOS;

    private List<SelectBean> selectTreeDTOS;

    private List<SelectBean> userLebalSelectTreeDTOS;

    private List<Attachments> attachmentsList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getCreateOrgId() {
        return createOrgId;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public Integer getTaskStats() {
        return taskStats;
    }

    public void setTaskStats(Integer taskStats) {
        this.taskStats = taskStats;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataTemplate() {
        return dataTemplate;
    }

    public void setDataTemplate(String dataTemplate) {
        this.dataTemplate = dataTemplate;
    }

    public String getDataTemplateName() {
        return dataTemplateName;
    }

    public void setDataTemplateName(String dataTemplateName) {
        this.dataTemplateName = dataTemplateName;
    }

    public String getParticipantType() {
        return participantType;
    }

    public void setParticipantType(String participantType) {
        this.participantType = participantType;
    }

    public Integer getIsNotify() {
        return isNotify;
    }

    public void setIsNotify(Integer isNotify) {
        this.isNotify = isNotify;
    }

    public Date getLastNotifyTime() {
        return lastNotifyTime;
    }

    public void setLastNotifyTime(Date lastNotifyTime) {
        this.lastNotifyTime = lastNotifyTime;
    }

    public Integer getNotifyTime() {
        return notifyTime;
    }

    public void setNotifyTime(Integer notifyTime) {
        this.notifyTime = notifyTime;
    }

    public Date getRequestFinishedTime() {
        return requestFinishedTime;
    }

    public void setRequestFinishedTime(Date requestFinishedTime) {
        this.requestFinishedTime = requestFinishedTime;
    }

    public Integer getEmergencyLevel() {
        return emergencyLevel;
    }

    public void setEmergencyLevel(Integer emergencyLevel) {
        this.emergencyLevel = emergencyLevel;
    }

    public String getSheetEffectiveRows() {
        return sheetEffectiveRows;
    }

    public void setSheetEffectiveRows(String sheetEffectiveRows) {
        this.sheetEffectiveRows = sheetEffectiveRows;
    }

    public String getRequirement() {
        return requirement;
    }

    public void setRequirement(String requirement) {
        this.requirement = requirement;
    }

    public String getParticipantIds() {
        return participantIds;
    }

    public void setParticipantIds(String participantIds) {
        this.participantIds = participantIds;
    }

    public String getParticipantLabels() {
        return participantLabels;
    }

    public void setParticipantLabels(String participantLabels) {
        this.participantLabels = participantLabels;
    }

    public Integer getIsDistribute() {
        return isDistribute;
    }

    public void setIsDistribute(Integer isDistribute) {
        this.isDistribute = isDistribute;
    }

    public String getDistributeId() {
        return distributeId;
    }

    public void setDistributeId(String distributeId) {
        this.distributeId = distributeId;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getCreatedby() {
        return createdby;
    }

    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    public Date getCreateddate() {
        return createddate;
    }

    public void setCreateddate(Date createddate) {
        this.createddate = createddate;
    }

    public String getModifiedby() {
        return modifiedby;
    }

    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }

    public List<CoTaskDetailListDTO> getCoTaskInfoDetailDTOS() {
        return coTaskInfoDetailDTOS;
    }

    public void setCoTaskInfoDetailDTOS(List<CoTaskDetailListDTO> coTaskInfoDetailDTOS) {
        this.coTaskInfoDetailDTOS = coTaskInfoDetailDTOS;
    }

    public List<SelectBean> getSelectTreeDTOS() {
        return selectTreeDTOS;
    }

    public void setSelectTreeDTOS(List<SelectBean> selectTreeDTOS) {
        this.selectTreeDTOS = selectTreeDTOS;
    }

    public List<SelectBean> getUserLebalSelectTreeDTOS() {
        return userLebalSelectTreeDTOS;
    }

    public void setUserLebalSelectTreeDTOS(List<SelectBean> userLebalSelectTreeDTOS) {
        this.userLebalSelectTreeDTOS = userLebalSelectTreeDTOS;
    }

    public List<Attachments> getAttachmentsList() {
        return attachmentsList;
    }

    public void setAttachmentsList(List<Attachments> attachmentsList) {
        this.attachmentsList = attachmentsList;
    }
}