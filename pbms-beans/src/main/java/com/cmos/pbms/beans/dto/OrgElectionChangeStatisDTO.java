package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

/**
 * 换届、缺席委员多维度统计报表检索结果DTO
 */
public class OrgElectionChangeStatisDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    private Date startDate;

    private Date endDate;

    private String orgId;

    private String orgName;

    private Integer orgType;

    private Date electionChangeDate;

    private Integer totalNum;

    private Integer shujiNum;

    private Integer weiyuanNum;

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public Date getElectionChangeDate() {
        return electionChangeDate;
    }

    public void setElectionChangeDate(Date electionChangeDate) {
        this.electionChangeDate = electionChangeDate;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public Integer getShujiNum() {
        return shujiNum;
    }

    public void setShujiNum(Integer shujiNum) {
        this.shujiNum = shujiNum;
    }

    public Integer getWeiyuanNum() {
        return weiyuanNum;
    }

    public void setWeiyuanNum(Integer weiyuanNum) {
        this.weiyuanNum = weiyuanNum;
    }
}
