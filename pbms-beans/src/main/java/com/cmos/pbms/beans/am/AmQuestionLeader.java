package com.cmos.pbms.beans.am;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

public class AmQuestionLeader extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    private String amqId;

    private Integer amqlType;

    private String amgInfo;

    private Integer orgType;

    private String userId;

    private Integer isdeleted;

    private String createdby;

    private Date createddate;

    private String modifiedby;

    private Date modifieddate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAmqId() {
        return amqId;
    }

    public void setAmqId(String amqId) {
        this.amqId = amqId;
    }

    public Integer getAmqlType() {
        return amqlType;
    }

    public void setAmqlType(Integer amqlType) {
        this.amqlType = amqlType;
    }

    public String getAmgInfo() {
        return amgInfo;
    }

    public void setAmgInfo(String amgInfo) {
        this.amgInfo = amgInfo;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getIsdeleted() {
        return isdeleted;
    }

    public void setIsdeleted(Integer isdeleted) {
        this.isdeleted = isdeleted;
    }

    public String getCreatedby() {
        return createdby;
    }

    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    public Date getCreateddate() {
        return createddate;
    }

    public void setCreateddate(Date createddate) {
        this.createddate = createddate;
    }

    public String getModifiedby() {
        return modifiedby;
    }

    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }
}