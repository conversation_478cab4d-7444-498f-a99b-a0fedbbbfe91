package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

public class OrgStatisticsListDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    private String orgfname;

    private Date foundDate;

    private Date electionChangeDate;

    private Date lastChangeDate;

    private Integer orgStatus;

    private String province;

    private String provinceStr;

    private String changeId;

    private String parentName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrgfname() {
        return orgfname;
    }

    public void setOrgfname(String orgfname) {
        this.orgfname = orgfname;
    }

    public Date getFoundDate() {
        return foundDate;
    }

    public void setFoundDate(Date foundDate) {
        this.foundDate = foundDate;
    }

    public Date getElectionChangeDate() {
        return electionChangeDate;
    }

    public void setElectionChangeDate(Date electionChangeDate) {
        this.electionChangeDate = electionChangeDate;
    }

    public Date getLastChangeDate() {
        return lastChangeDate;
    }

    public void setLastChangeDate(Date lastChangeDate) {
        this.lastChangeDate = lastChangeDate;
    }

    public Integer getOrgStatus() {
        return orgStatus;
    }

    public void setOrgStatus(Integer orgStatus) {
        this.orgStatus = orgStatus;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvinceStr() {
        return provinceStr;
    }

    public void setProvinceStr(String provinceStr) {
        this.provinceStr = provinceStr;
    }

    public String getChangeId() {
        return changeId;
    }

    public void setChangeId(String changeId) {
        this.changeId = changeId;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }
}
