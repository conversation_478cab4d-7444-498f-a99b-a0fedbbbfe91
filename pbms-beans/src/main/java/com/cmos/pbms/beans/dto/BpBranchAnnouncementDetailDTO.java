package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;
import com.cmos.pbms.beans.common.Attachments;

import java.util.Date;
import java.util.List;

public class BpBranchAnnouncementDetailDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    private String orgId;

    private String orgfname;

    private String orgName;

    private String bpaRangeId;

    private String bpaRangeCode;

    private String bpaRangeName;

    private String bpaRangeOrgName;

    private String bpaType;

    private String bpaTypeStr;

    private String bpaTitle;

    private String bpaContent;

    private Integer isTop;

    private Integer orderNo;

    private Date startDate;

    private Date endDate;

    private Integer bpaStatus;

    private String createdby;

    private String createdUserName;

    private Date createddate;

    private List<Attachments> fileList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgfname() {
        return orgfname;
    }

    public void setOrgfname(String orgfname) {
        this.orgfname = orgfname;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getBpaRangeId() {
        return bpaRangeId;
    }

    public void setBpaRangeId(String bpaRangeId) {
        this.bpaRangeId = bpaRangeId;
    }

    public String getBpaRangeCode() {
        return bpaRangeCode;
    }

    public void setBpaRangeCode(String bpaRangeCode) {
        this.bpaRangeCode = bpaRangeCode;
    }

    public String getBpaRangeName() {
        return bpaRangeName;
    }

    public void setBpaRangeName(String bpaRangeName) {
        this.bpaRangeName = bpaRangeName;
    }

    public String getBpaRangeOrgName() {
        return bpaRangeOrgName;
    }

    public void setBpaRangeOrgName(String bpaRangeOrgName) {
        this.bpaRangeOrgName = bpaRangeOrgName;
    }

    public String getBpaType() {
        return bpaType;
    }

    public void setBpaType(String bpaType) {
        this.bpaType = bpaType;
    }

    public String getBpaTypeStr() {
        return bpaTypeStr;
    }

    public void setBpaTypeStr(String bpaTypeStr) {
        this.bpaTypeStr = bpaTypeStr;
    }

    public String getBpaTitle() {
        return bpaTitle;
    }

    public void setBpaTitle(String bpaTitle) {
        this.bpaTitle = bpaTitle;
    }

    public String getBpaContent() {
        return bpaContent;
    }

    public void setBpaContent(String bpaContent) {
        this.bpaContent = bpaContent;
    }

    public Integer getIsTop() {
        return isTop;
    }

    public void setIsTop(Integer isTop) {
        this.isTop = isTop;
    }

    public Integer getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getBpaStatus() {
        return bpaStatus;
    }

    public void setBpaStatus(Integer bpaStatus) {
        this.bpaStatus = bpaStatus;
    }

    public String getCreatedby() {
        return createdby;
    }

    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    public String getCreatedUserName() {
        return createdUserName;
    }

    public void setCreatedUserName(String createdUserName) {
        this.createdUserName = createdUserName;
    }

    public Date getCreateddate() {
        return createddate;
    }

    public void setCreateddate(Date createddate) {
        this.createddate = createddate;
    }

    public List<Attachments> getFileList() {
        return fileList;
    }

    public void setFileList(List<Attachments> fileList) {
        this.fileList = fileList;
    }
}
