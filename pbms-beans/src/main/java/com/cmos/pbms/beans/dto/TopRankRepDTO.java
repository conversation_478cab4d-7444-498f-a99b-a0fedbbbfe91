package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

/**
 * TOP排行榜-新闻/视频统计报表DTO
 */
public class TopRankRepDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String objId;

    private String objTitle;

    private String objType;

    private Integer readCount;

    private Integer indexNo;

    public String getObjId() {
        return objId;
    }

    public void setObjId(String objId) {
        this.objId = objId;
    }

    public String getObjTitle() {
        return objTitle;
    }

    public void setObjTitle(String objTitle) {
        this.objTitle = objTitle;
    }

    public String getObjType() {
        return objType;
    }

    public void setObjType(String objType) {
        this.objType = objType;
    }

    public Integer getReadCount() {
        return readCount;
    }

    public void setReadCount(Integer readCount) {
        this.readCount = readCount;
    }

    public Integer getIndexNo() {
        return indexNo;
    }

    public void setIndexNo(Integer indexNo) {
        this.indexNo = indexNo;
    }
}
