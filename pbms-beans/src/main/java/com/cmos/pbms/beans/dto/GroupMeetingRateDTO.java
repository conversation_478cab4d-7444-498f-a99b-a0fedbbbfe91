package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

/**
 * 党小组会议完成情况
 */
public class GroupMeetingRateDTO extends GenericBean {
    private static final long serialVersionUID = 1L;

    private String orgid;

    private String orgsname;

    private String orgfname;

    private Integer totalNum;

    private Integer finishNum;

    private String rate;

    public String getOrgid() {
        return orgid;
    }

    public void setOrgid(String orgid) {
        this.orgid = orgid;
    }

    public String getOrgsname() {
        return orgsname;
    }

    public void setOrgsname(String orgsname) {
        this.orgsname = orgsname;
    }

    public String getOrgfname() {
        return orgfname;
    }

    public void setOrgfname(String orgfname) {
        this.orgfname = orgfname;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public Integer getFinishNum() {
        return finishNum;
    }

    public void setFinishNum(Integer finishNum) {
        this.finishNum = finishNum;
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    @Override
    public String toString() {
        return "GroupMeetingRateDTO{" +
                "orgid='" + orgid + '\'' +
                ", orgsname='" + orgsname + '\'' +
                ", orgfname='" + orgfname + '\'' +
                ", totalNum=" + totalNum +
                ", finishNum=" + finishNum +
                ", rate='" + rate + '\'' +
                '}';
    }
}
