package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

/**
 * 积分排名-个人排名统计报表DTO
 */
public class PerScoreRankRepDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String userId;

    private String userName;

    private Integer indexNo;

    private String orgfName;

    private String orgsName;

    private String orgName;

    private Integer score;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getIndexNo() {
        return indexNo;
    }

    public void setIndexNo(Integer indexNo) {
        this.indexNo = indexNo;
    }

    public String getOrgfName() {
        return orgfName;
    }

    public void setOrgfName(String orgfName) {
        this.orgfName = orgfName;
    }

    public String getOrgsName() {
        return orgsName;
    }

    public void setOrgsName(String orgsName) {
        this.orgsName = orgsName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }
}
