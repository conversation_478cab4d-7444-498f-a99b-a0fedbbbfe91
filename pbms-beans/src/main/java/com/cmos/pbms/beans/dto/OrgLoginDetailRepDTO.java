package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

/**
 * 各支部党员登录明细-统计报表DTO
 */
public class OrgLoginDetailRepDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String userScoreDtlId;

    /**
     * 登录用户ID
     */
    private String userId;

    /**
     * 登录用户姓名
     */
    private String userName;

    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 登录用户所在组织ID
     */
    private String orgId;

    /**
     * 登录用户所在组织名称
     */
    private String orgName;

    /**
     * 登录用户所在组织的所在党支部ID
     */
    private String branchOrgId;

    /**
     * 登录用户所在组织的所在党支部名称
     */
    private String branchOrgName;

    /**
     * 登录用户所在组织的所在分公司名称
     */
    private String provinceOrgName;

    public String getUserScoreDtlId() {
        return userScoreDtlId;
    }

    public void setUserScoreDtlId(String userScoreDtlId) {
        this.userScoreDtlId = userScoreDtlId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(Date loginTime) {
        this.loginTime = loginTime;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getBranchOrgId() {
        return branchOrgId;
    }

    public void setBranchOrgId(String branchOrgId) {
        this.branchOrgId = branchOrgId;
    }

    public String getBranchOrgName() {
        return branchOrgName;
    }

    public void setBranchOrgName(String branchOrgName) {
        this.branchOrgName = branchOrgName;
    }

    public String getProvinceOrgName() {
        return provinceOrgName;
    }

    public void setProvinceOrgName(String provinceOrgName) {
        this.provinceOrgName = provinceOrgName;
    }
}
