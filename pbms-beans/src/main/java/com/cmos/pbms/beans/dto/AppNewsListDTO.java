package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

import java.util.Date;
import java.util.List;

/**
 * APP端栏目新闻列表业务实体类
 *
 * <AUTHOR>
 */
public class AppNewsListDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String specialId;

    private String specialName;

    private String id;

    private String title;

    private String themepic;

    private Integer filetype;

    private String theType;

    private Integer readcount;

    private String pushtime;

    private Date pushDate;

    private Date auditdate;

    private Date createddate;

    //所属党组织
    private String org;

    // 所属党组织全称
    private String orgfName;

    //来源
    private String newsFrom;

    private String deptName;
    private String objTheme;

    /**
     * 出处
     */
    private String makeFrom;

    /**
     * 来源
     */
    private String turnFrom;

    // 布局样式
    private String layoutStyle;

    // 新闻内容
    private String content;

    // 提交人
    private String submitUser;

    // 提交时间
    private Date submitTime;

    // 审核不通过原因
    private String refuseReason;

    // 启用状态：0-启用，1-停用
    private Integer enableStatus;

    // 可见范围-APP：1-公开；2-本级及以下可见
    private Integer publicScope;

    private Integer istop;

    private String orderNo;

    private String category;

    // 是否必读（0-否，1-是）
    private Integer isRequiredReading;
    // 推荐阅读时长
    private Integer suggestDuration;
    private Integer suggestRate;

    private String userCollectionId;

    // 新闻内容图片
    private List<String> contentPicList;

    public String getObjTheme() {
        return objTheme;
    }

    public void setObjTheme(String objTheme) {
        this.objTheme = objTheme;
    }

    public String getSpecialId() {
        return specialId;
    }

    public void setSpecialId(String specialId) {
        this.specialId = specialId;
    }

    public String getSpecialName() {
        return specialName;
    }

    public void setSpecialName(String specialName) {
        this.specialName = specialName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getThemepic() {
        return themepic;
    }

    public void setThemepic(String themepic) {
        this.themepic = themepic;
    }

    public Integer getFiletype() {
        return filetype;
    }

    public void setFiletype(Integer filetype) {
        this.filetype = filetype;
    }

    public Integer getReadcount() {
        return readcount;
    }

    public void setReadcount(Integer readcount) {
        this.readcount = readcount;
    }

    public String getPushtime() {
        return pushtime;
    }

    public void setPushtime(String pushtime) {
        this.pushtime = pushtime;
    }

    public Date getPushDate() {
        return pushDate;
    }

    public void setPushDate(Date pushDate) {
        this.pushDate = pushDate;
    }

    public String getOrg() {
//        if (StringUtils.isNotBlank(this.deptName))
//            return this.deptName;
        return org;
    }

    public void setOrg(String org) {
        this.org = org;
    }

    public String getOrgfName() {
        return orgfName;
    }

    public void setOrgfName(String orgfName) {
        this.orgfName = orgfName;
    }

    public String getLayoutStyle() {
        return layoutStyle;
    }

    public void setLayoutStyle(String layoutStyle) {
        this.layoutStyle = layoutStyle;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getContentPicList() {
        return contentPicList;
    }

    public void setContentPicList(List<String> contentPicList) {
        this.contentPicList = contentPicList;
    }

    public String getNewsFrom() {
        return newsFrom;
    }

    public void setNewsFrom(String newsFrom) {
        this.newsFrom = newsFrom;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getMakeFrom() {
        return makeFrom;
    }

    public void setMakeFrom(String makeFrom) {
        this.makeFrom = makeFrom;
    }

    public String getTurnFrom() {
        return turnFrom;
    }

    public void setTurnFrom(String turnFrom) {
        this.turnFrom = turnFrom;
    }

    public String getSubmitUser() {
        return submitUser;
    }

    public void setSubmitUser(String submitUser) {
        this.submitUser = submitUser;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public String getRefuseReason() {
        return refuseReason;
    }

    public void setRefuseReason(String refuseReason) {
        this.refuseReason = refuseReason;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Integer getPublicScope() {
        return publicScope;
    }

    public void setPublicScope(Integer publicScope) {
        this.publicScope = publicScope;
    }

    public String getTheType() {
        return theType;
    }

    public void setTheType(String theType) {
        this.theType = theType;
    }

    public Integer getIstop() {
        return istop;
    }

    public void setIstop(Integer istop) {
        this.istop = istop;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Date getCreateddate() {
        return createddate;
    }

    public void setCreateddate(Date createddate) {
        this.createddate = createddate;
    }

    public Date getAuditdate() {
        return auditdate;
    }

    public void setAuditdate(Date auditdate) {
        this.auditdate = auditdate;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Integer getIsRequiredReading() {
        return isRequiredReading;
    }

    public String getUserCollectionId() {
        return userCollectionId;
    }

    public void setUserCollectionId(String userCollectionId) {
        this.userCollectionId = userCollectionId;
    }

    public void setIsRequiredReading(Integer isRequiredReading) {
        this.isRequiredReading = isRequiredReading;
    }

    public Integer getSuggestDuration() {
        return suggestDuration;
    }

    public void setSuggestDuration(Integer suggestDuration) {
        this.suggestDuration = suggestDuration;
    }

    public Integer getSuggestRate() {
        return suggestRate;
    }

    public void setSuggestRate(Integer suggestRate) {
        this.suggestRate = suggestRate;
    }
}
