package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;
import com.cmos.pbms.web.ccsp.annotation.Decryption;

import java.util.Date;

public class OnlineSuggestDTO extends GenericBean {

    private static final long serialVersionUID = -908210915681404658L;

    private String osFlg;

    private String title;

    private Date startTime;

    private Date endTime;

    private String contentUrl;

    private String content;

    private Integer isLogin;

    private String userId;

    private String userName;
    @Decryption
    private String telephones;

    private String orgInfo;

    public String getOsFlg() {
        return osFlg;
    }

    public void setOsFlg(String osFlg) {
        this.osFlg = osFlg;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getContentUrl() {
        return contentUrl;
    }

    public void setContentUrl(String contentUrl) {
        this.contentUrl = contentUrl;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getIsLogin() {
        return isLogin;
    }

    public void setIsLogin(Integer isLogin) {
        this.isLogin = isLogin;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getTelephones() {
        return telephones;
    }

    public void setTelephones(String telephones) {
        this.telephones = telephones;
    }

    public String getOrgInfo() {
        return orgInfo;
    }

    public void setOrgInfo(String orgInfo) {
        this.orgInfo = orgInfo;
    }
}
