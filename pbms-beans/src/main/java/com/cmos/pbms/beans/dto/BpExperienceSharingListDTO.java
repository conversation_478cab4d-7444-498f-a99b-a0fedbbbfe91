package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

public class BpExperienceSharingListDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    private String orgId;

    private String experienceTitle;

    private String experienceContent;

    private String userId;

    private String userName;

    private String profilephoto;

    private Integer publicScope;

    private Integer thuCount;

    private Integer revCount;

    private Integer dataStatus;

    private Date createddate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getExperienceTitle() {
        return experienceTitle;
    }

    public void setExperienceTitle(String experienceTitle) {
        this.experienceTitle = experienceTitle;
    }

    public String getExperienceContent() {
        return experienceContent;
    }

    public void setExperienceContent(String experienceContent) {
        this.experienceContent = experienceContent;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getProfilephoto() {
        return profilephoto;
    }

    public void setProfilephoto(String profilephoto) {
        this.profilephoto = profilephoto;
    }

    public Integer getPublicScope() {
        return publicScope;
    }

    public void setPublicScope(Integer publicScope) {
        this.publicScope = publicScope;
    }

    public Integer getThuCount() {
        return thuCount;
    }

    public void setThuCount(Integer thuCount) {
        this.thuCount = thuCount;
    }

    public Integer getRevCount() {
        return revCount;
    }

    public void setRevCount(Integer revCount) {
        this.revCount = revCount;
    }

    public Integer getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Date getCreateddate() {
        return createddate;
    }

    public void setCreateddate(Date createddate) {
        this.createddate = createddate;
    }
}
