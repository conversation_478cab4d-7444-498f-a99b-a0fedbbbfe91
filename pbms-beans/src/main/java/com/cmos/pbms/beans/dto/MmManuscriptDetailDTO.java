package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;
import com.cmos.pbms.beans.news.News;

import java.util.Date;

public class MmManuscriptDetailDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    private String mTitle;

    private String submitUser;

    private Integer readCount;

    private Integer thumbsCount;

    private Integer reviewCount;

    private Date submitTime;

    private String mContent;

    private String mType;

    private String mTypeDesc;

    private Integer isReprint;

    private String reprintId;

    private Date auditTime;

    private String auditRemark;

    private Integer mStatus;

    private News news;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getmTitle() {
        return mTitle;
    }

    public void setmTitle(String mTitle) {
        this.mTitle = mTitle;
    }

    public String getSubmitUser() {
        return submitUser;
    }

    public void setSubmitUser(String submitUser) {
        this.submitUser = submitUser;
    }

    public Integer getReadCount() {
        return readCount;
    }

    public void setReadCount(Integer readCount) {
        this.readCount = readCount;
    }

    public Integer getThumbsCount() {
        return thumbsCount;
    }

    public void setThumbsCount(Integer thumbsCount) {
        this.thumbsCount = thumbsCount;
    }

    public Integer getReviewCount() {
        return reviewCount;
    }

    public void setReviewCount(Integer reviewCount) {
        this.reviewCount = reviewCount;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public String getmContent() {
        return mContent;
    }

    public void setmContent(String mContent) {
        this.mContent = mContent;
    }

    public String getmType() {
        return mType;
    }

    public void setmType(String mType) {
        this.mType = mType;
    }

    public String getmTypeDesc() {
        return mTypeDesc;
    }

    public void setmTypeDesc(String mTypeDesc) {
        this.mTypeDesc = mTypeDesc;
    }

    public Integer getIsReprint() {
        return isReprint;
    }

    public void setIsReprint(Integer isReprint) {
        this.isReprint = isReprint;
    }

    public String getReprintId() {
        return reprintId;
    }

    public void setReprintId(String reprintId) {
        this.reprintId = reprintId;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public Integer getmStatus() {
        return mStatus;
    }

    public void setmStatus(Integer mStatus) {
        this.mStatus = mStatus;
    }

    public News getNews() {
        return news;
    }

    public void setNews(News news) {
        this.news = news;
    }
}
