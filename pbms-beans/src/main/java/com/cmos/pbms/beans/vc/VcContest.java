package com.cmos.pbms.beans.vc;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

public class Vc<PERSON>ontest extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    private String topic;

    private String vcCode;

    private Date startTime;

    private Date endTime;

    private String deptId;

    private String userId;

    private String description;

    private Integer playerMutexCycle;

    private Integer playerAllowedTimes;

    private Integer judgeCycle;

    private Integer judgeAllowedTimes;

    private Integer vcStatus;

    private String videoId;

    private String videoUrl;

    private String videoImageUrl;

    private Integer isdeleted;

    private String createdby;

    private Date createddate;

    private String modifiedby;

    private Date modifieddate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getVcCode() {
        return vcCode;
    }

    public void setVcCode(String vcCode) {
        this.vcCode = vcCode;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getPlayerMutexCycle() {
        return playerMutexCycle;
    }

    public void setPlayerMutexCycle(Integer playerMutexCycle) {
        this.playerMutexCycle = playerMutexCycle;
    }

    public Integer getPlayerAllowedTimes() {
        return playerAllowedTimes;
    }

    public void setPlayerAllowedTimes(Integer playerAllowedTimes) {
        this.playerAllowedTimes = playerAllowedTimes;
    }

    public Integer getJudgeCycle() {
        return judgeCycle;
    }

    public void setJudgeCycle(Integer judgeCycle) {
        this.judgeCycle = judgeCycle;
    }

    public Integer getJudgeAllowedTimes() {
        return judgeAllowedTimes;
    }

    public void setJudgeAllowedTimes(Integer judgeAllowedTimes) {
        this.judgeAllowedTimes = judgeAllowedTimes;
    }

    public Integer getVcStatus() {
        return vcStatus;
    }

    public void setVcStatus(Integer vcStatus) {
        this.vcStatus = vcStatus;
    }

    public String getVideoId() {
        return videoId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getVideoImageUrl() {
        return videoImageUrl;
    }

    public void setVideoImageUrl(String videoImageUrl) {
        this.videoImageUrl = videoImageUrl;
    }

    public Integer getIsdeleted() {
        return isdeleted;
    }

    public void setIsdeleted(Integer isdeleted) {
        this.isdeleted = isdeleted;
    }

    public String getCreatedby() {
        return createdby;
    }

    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    public Date getCreateddate() {
        return createddate;
    }

    public void setCreateddate(Date createddate) {
        this.createddate = createddate;
    }

    public String getModifiedby() {
        return modifiedby;
    }

    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }
}