package com.cmos.pbms.beans.ps;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

public class PsUserDailyScore extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    private String pusId;

    private Integer scoreDate;

    private Integer poineerScore;

    private Integer vRankingNum;

    private Integer cRankingNum;

    private Integer vComparison;

    private Integer cComparison;

    private Integer isdeleted;

    private String createdby;

    private Date createddate;

    private String modifiedby;

    private Date modifieddate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPusId() {
        return pusId;
    }

    public void setPusId(String pusId) {
        this.pusId = pusId;
    }

    public Integer getScoreDate() {
        return scoreDate;
    }

    public void setScoreDate(Integer scoreDate) {
        this.scoreDate = scoreDate;
    }

    public Integer getPoineerScore() {
        return poineerScore;
    }

    public void setPoineerScore(Integer poineerScore) {
        this.poineerScore = poineerScore;
    }

    public Integer getvRankingNum() {
        return vRankingNum;
    }

    public void setvRankingNum(Integer vRankingNum) {
        this.vRankingNum = vRankingNum;
    }

    public Integer getcRankingNum() {
        return cRankingNum;
    }

    public void setcRankingNum(Integer cRankingNum) {
        this.cRankingNum = cRankingNum;
    }

    public Integer getvComparison() {
        return vComparison;
    }

    public void setvComparison(Integer vComparison) {
        this.vComparison = vComparison;
    }

    public Integer getcComparison() {
        return cComparison;
    }

    public void setcComparison(Integer cComparison) {
        this.cComparison = cComparison;
    }

    public Integer getIsdeleted() {
        return isdeleted;
    }

    public void setIsdeleted(Integer isdeleted) {
        this.isdeleted = isdeleted;
    }

    public String getCreatedby() {
        return createdby;
    }

    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    public Date getCreateddate() {
        return createddate;
    }

    public void setCreateddate(Date createddate) {
        this.createddate = createddate;
    }

    public String getModifiedby() {
        return modifiedby;
    }

    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }
}