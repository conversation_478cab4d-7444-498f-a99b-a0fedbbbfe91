package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

public class DdCommentSummaryListDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    private String commentYear;

    private String topic;

    private Integer publishStatus;

    private String publisher;

    private Date publishTime;

    private Integer isDistribute;

    private String distributeId;

    private Integer allOrgNum;

    private Integer doneOrgNum;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCommentYear() {
        return commentYear;
    }

    public void setCommentYear(String commentYear) {
        this.commentYear = commentYear;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public Integer getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(Integer publishStatus) {
        this.publishStatus = publishStatus;
    }

    public String getPublisher() {
        return publisher;
    }

    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public Integer getIsDistribute() {
        return isDistribute;
    }

    public void setIsDistribute(Integer isDistribute) {
        this.isDistribute = isDistribute;
    }

    public String getDistributeId() {
        return distributeId;
    }

    public void setDistributeId(String distributeId) {
        this.distributeId = distributeId;
    }

    public Integer getAllOrgNum() {
        return allOrgNum;
    }

    public void setAllOrgNum(Integer allOrgNum) {
        this.allOrgNum = allOrgNum;
    }

    public Integer getDoneOrgNum() {
        return doneOrgNum;
    }

    public void setDoneOrgNum(Integer doneOrgNum) {
        this.doneOrgNum = doneOrgNum;
    }
}
