package com.cmos.pbms.beans.enums;

/**
 * 消息发送类型枚举类
 */
public enum MessageTypeEnum {
    MEETING_PLAN(1, "会议计划", "com.cmos.pbms.web.utils.Yzy.notice.MeetingPlanNotice"),
    MEETING_NOTIFICATION(2, "会议通知", "com.cmos.pbms.web.utils.Yzy.notice.ConferenceNotificationUtils"),
    MEETING_ARCHIVE(3, "会议归档", "com.cmos.pbms.web.utils.Yzy.notice.MeetingArchiveNotice"),
    TASK_SUPERVISION(4, "任务督办", "com.cmos.pbms.web.utils.Yzy.notice.TaskSupervisionNotice"),
    DD_SUPERVISION(5,"民主评议催办","com.cmos.pbms.web.utils.Yzy.notice.TaskSupervisionNotice");
    /**
     * 类型值
     */
    private Integer code;
    /**
     * 类型描述
     */
    private String desc;
    /**
     * 处理类
     */
    private String handlerClass;

    /**
     * 私有构造,防止被外部调用
     *
     * @param code 类型值
     * @param desc 类型描述
     * @param handlerClass 处理类
     */
    MessageTypeEnum(Integer code, String desc, String handlerClass) {
        this.code = code;
        this.desc = desc;
        this.handlerClass = handlerClass;
    }

    /**
     * 根据code获取枚举实例
     *
     * @param code 类型值
     * @return 枚举实例
     */
    public static MessageTypeEnum getByCode(Integer code) {
        for (MessageTypeEnum type : MessageTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 取类型描述
     *
     * @return 类型描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 取类型值
     *
     * @return 类型值
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 取处理类
     *
     * @return 处理类
     */
    public String getHandlerClass() {
        return handlerClass;
    }
}
