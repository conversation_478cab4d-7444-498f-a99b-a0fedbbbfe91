package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;
import com.cmos.pbms.beans.common.Attachments;
import com.cmos.pbms.beans.sys.Users;

import java.util.List;

/**
 * 读书活动业务实体类
 *
 * <AUTHOR>
 * created on 2018-03-28 上午11:03
 */
public class ReadingDTO extends GenericBean {

    private static final long serialVersionUID = 3824583485625827883L;

    private String id;

    private String theme;

    private String actTime;

    private String starttime;

    private String endtime;

    private String sponsorName;

    private String sponsorCode;

    private String themepic;

    private Integer rnstatus;

    private String summary;

    private Users leader;

    private String currentReaderId;

    private List<Users> participations;

    private String bookId;

    private String bookName;

    private String ebookUrl;

    private Boolean hasHandOverNote;

    private List<Attachments> attachmentList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTheme() {
        return theme;
    }

    public void setTheme(String theme) {
        this.theme = theme;
    }

    public String getActTime() {
        actTime = starttime.substring(0, 16) + " ~ " + endtime.substring(0, 16);
        return actTime;
    }

    public void setActTime(String actTime) {
        this.actTime = actTime;
    }

    public String getStarttime() {
        return starttime;
    }

    public void setStarttime(String starttime) {
        this.starttime = starttime;
    }

    public String getEndtime() {
        return endtime;
    }

    public void setEndtime(String endtime) {
        this.endtime = endtime;
    }

    public String getSponsorName() {
        return sponsorName;
    }

    public void setSponsorName(String sponsorName) {
        this.sponsorName = sponsorName;
    }

    public String getSponsorCode() {
        return sponsorCode;
    }

    public void setSponsorCode(String sponsorCode) {
        this.sponsorCode = sponsorCode;
    }

    public String getThemepic() {
        return themepic;
    }

    public void setThemepic(String themepic) {
        this.themepic = themepic;
    }

    public Integer getRnstatus() {
        return rnstatus;
    }

    public void setRnstatus(Integer rnstatus) {
        this.rnstatus = rnstatus;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public Users getLeader() {
        return leader;
    }

    public void setLeader(Users leader) {
        this.leader = leader;
    }

    public String getCurrentReaderId() {
        return currentReaderId;
    }

    public void setCurrentReaderId(String currentReaderId) {
        this.currentReaderId = currentReaderId;
    }

    public List<Users> getParticipations() {
        return participations;
    }

    public void setParticipations(List<Users> participations) {
        this.participations = participations;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    public String getEbookUrl() {
        return ebookUrl;
    }

    public void setEbookUrl(String ebookUrl) {
        this.ebookUrl = ebookUrl;
    }

    public Boolean getHasHandOverNote() {
        return hasHandOverNote;
    }

    public void setHasHandOverNote(Boolean hasHandOverNote) {
        this.hasHandOverNote = hasHandOverNote;
    }

    public List<Attachments> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<Attachments> attachmentList) {
        this.attachmentList = attachmentList;
    }
}
