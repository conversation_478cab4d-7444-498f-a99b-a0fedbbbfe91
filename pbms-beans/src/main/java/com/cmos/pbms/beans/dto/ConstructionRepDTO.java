package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

/**
 * 运营内容统计报表DTO
 */
public class ConstructionRepDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    /**
     * 数据ID，对应各业务表的ID
     */
    private String objId;

    /**
     * 类型
     */
    private String objType;

    /**
     * 标题
     */
    private String title;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 组织架构ID
     */
    private String orgId;

    /**
     * 组织架构简称
     */
    private String orgsName;

    /**
     * 组织架构全称
     */
    private String orgfName;

    /**
     * 组织架构显示名称
     */
    private String orgName;

    /**
     * 创建用户ID
     */
    private String createUserId;

    /**
     * 创建者
     */
    private String createUser;

    /**
     * 出处
     */
    private String makeFrom;

    /**
     * 来源
     */
    private String turnFrom;

    private Integer publicScope;

    private Integer reviewStatus;

    private String submitUser;

    private Date submitTime;

    private String reviewUser;

    private Date reviewTime;

    private String refuseReason;

    private Date releaseTime;

    public String getObjId() {
        return objId;
    }

    public void setObjId(String objId) {
        this.objId = objId;
    }

    public String getObjType() {
        return objType;
    }

    public void setObjType(String objType) {
        this.objType = objType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgsName() {
        return orgsName;
    }

    public void setOrgsName(String orgsName) {
        this.orgsName = orgsName;
    }

    public String getOrgfName() {
        return orgfName;
    }

    public void setOrgfName(String orgfName) {
        this.orgfName = orgfName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getMakeFrom() {
        return makeFrom;
    }

    public void setMakeFrom(String makeFrom) {
        this.makeFrom = makeFrom;
    }

    public String getTurnFrom() {
        return turnFrom;
    }

    public void setTurnFrom(String turnFrom) {
        this.turnFrom = turnFrom;
    }

    public Integer getPublicScope() {
        return publicScope;
    }

    public void setPublicScope(Integer publicScope) {
        this.publicScope = publicScope;
    }

    public Integer getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getSubmitUser() {
        return submitUser;
    }

    public void setSubmitUser(String submitUser) {
        this.submitUser = submitUser;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public String getReviewUser() {
        return reviewUser;
    }

    public void setReviewUser(String reviewUser) {
        this.reviewUser = reviewUser;
    }

    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public String getRefuseReason() {
        return refuseReason;
    }

    public void setRefuseReason(String refuseReason) {
        this.refuseReason = refuseReason;
    }

    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }
}
