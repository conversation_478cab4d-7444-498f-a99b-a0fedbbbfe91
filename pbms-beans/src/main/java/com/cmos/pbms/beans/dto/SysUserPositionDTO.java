package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;
import com.cmos.pbms.web.ccsp.annotation.Decryption;

public class SysUserPositionDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 职务ID
     */
    private String postId;

    /**
     * 职务名称
     */
    private String postName;

    /**
     * 组织ID
     */
    private String orgId;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 组织全称
     */
    private String orgfName;

    /**
     * 电话号码
     */
    @Decryption
    private String telephone;

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPostId() {
        return postId;
    }

    public void setPostId(String postId) {
        this.postId = postId;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgfName() {
        return orgfName;
    }

    public void setOrgfName(String orgfName) {
        this.orgfName = orgfName;
    }
}
