package com.cmos.pbms.beans.enums;

/**
 * @类名：CommentSummaryLevelEnum
 * @类的作用：
 * @作者：牛文钻
 * @创建时间：2019/3/22
 */
public enum CommentSummaryLevelEnum {

    DEFAULT(0, "无"),
    EXCELLENT(1, "优秀"),
    GOOD(2, "合格"),
    QUALIFIED(3, "基本合格"),
    UNQUALIFIED(4, "不合格");

    private Integer code;

    private String desc;

    CommentSummaryLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
