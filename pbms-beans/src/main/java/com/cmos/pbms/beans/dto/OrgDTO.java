package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;
import com.cmos.pbms.web.ccsp.annotation.Decryption;
import org.hibernate.validator.constraints.Email;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Date;

/**
 * 党组业务实体类
 *
 * <AUTHOR>
 */
public class OrgDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    @NotEmpty
    private String id;

    private String province;

    private String provinceName;

    private String orgsname;

    private String orgfname;

    private String codestr;

    private String parentid;

    private String parentname;

    private Integer levelnum;

    private Integer ordernum;

    private Integer orgtype;

    private String orgtypeStr;

    private String address;
    @Decryption
    private String telephone;

    private String fax;

    @Email(message = "email必须为正确的邮箱地址")
    private String email;

    private Byte orgstatus;

    private String deptid;

    private String deptname;

    private int isgroup;

    private int isBranch;

    /**
     * 组织头像
     */
    private String picture;

    /**
     * 成立日期
     */
    private Date foundDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String extfld1;

    /**
     * 预留字段2
     */
    private String extfld2;

    /**
     * 预留字段3
     */
    private String extfld3;

    private String orgName;

    private Date electionChangeDate;

    private String meetingIcon;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getOrgsname() {
        return orgsname;
    }

    public void setOrgsname(String orgsname) {
        this.orgsname = orgsname;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getOrgfname() {
        return orgfname;
    }

    public void setOrgfname(String orgfname) {
        this.orgfname = orgfname;
    }

    public String getCodestr() {
        return codestr;
    }

    public void setCodestr(String codestr) {
        this.codestr = codestr;
    }

    public String getParentid() {
        return parentid;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }

    public String getParentname() {
        return parentname;
    }

    public void setParentname(String parentname) {
        this.parentname = parentname;
    }

    public Integer getLevelnum() {
        return levelnum;
    }

    public void setLevelnum(Integer levelnum) {
        this.levelnum = levelnum;
    }

    public Integer getOrdernum() {
        return ordernum;
    }

    public void setOrdernum(Integer ordernum) {
        this.ordernum = ordernum;
    }

    public Integer getOrgtype() {
        return orgtype;
    }

    public void setOrgtype(Integer orgtype) {
        this.orgtype = orgtype;
    }

    public String getOrgtypeStr() {
        return orgtypeStr;
    }

    public void setOrgtypeStr(String orgtypeStr) {
        this.orgtypeStr = orgtypeStr;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Byte getOrgstatus() {
        return orgstatus;
    }

    public void setOrgstatus(Byte orgstatus) {
        this.orgstatus = orgstatus;
    }

    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }

    public String getDeptname() {
        return deptname;
    }

    public void setDeptname(String deptname) {
        this.deptname = deptname;
    }

    public int getIsgroup() {
        return isgroup;
    }

    public void setIsgroup(int isgroup) {
        this.isgroup = isgroup;
    }

    public int getIsBranch() {
        return isBranch;
    }

    public void setIsBranch(int isBranch) {
        this.isBranch = isBranch;
    }

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    public Date getFoundDate() {
        return foundDate;
    }

    public void setFoundDate(Date foundDate) {
        this.foundDate = foundDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getExtfld1() {
        return extfld1;
    }

    public void setExtfld1(String extfld1) {
        this.extfld1 = extfld1;
    }

    public String getExtfld2() {
        return extfld2;
    }

    public void setExtfld2(String extfld2) {
        this.extfld2 = extfld2;
    }

    public String getExtfld3() {
        return extfld3;
    }

    public void setExtfld3(String extfld3) {
        this.extfld3 = extfld3;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Date getElectionChangeDate() {
        return electionChangeDate;
    }

    public void setElectionChangeDate(Date electionChangeDate) {
        this.electionChangeDate = electionChangeDate;
    }

    public String getMeetingIcon() {
        return meetingIcon;
    }

    public void setMeetingIcon(String meetingIcon) {
        this.meetingIcon = meetingIcon;
    }
}