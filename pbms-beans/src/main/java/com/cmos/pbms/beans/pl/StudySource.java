package com.cmos.pbms.beans.pl;

import com.cmos.common.bean.GenericBean;
import com.cmos.pbms.beans.common.Attachments;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

public class StudySource extends GenericBean {
    private static final long serialVersionUID = 1L;
    @NotNull(message = "id不可为空")
    private String id;

    private String orgid;

    private Integer publicscope;

    private String strpublicscope;

    private String orgname;

    private Integer filetype;

    private String strfiletype;

    private String province;

    private String filename;

    private String duration;

    private String storepath;

    private String title;

    private String themepic;

    private String contenttype;

    private String strcontenttype;

    private Integer ssstatus;

    private String strssstatus;

    private Integer isexcellent;

    private Integer readcount;

    private String summaryWithHtml;

    private String summary;

    private Integer isdeleted;

    private String createdby;

    private String createUser;

    private Date createddate;

    private String modifiedby;

    private Date modifieddate;

    private Integer enablereview;

    private Integer enablethumbs;

    private Integer reviewscount;

    private Integer thumbscount;

    /**
     * 出处
     */
    private String makeFrom;

    /**
     * 来源
     */
    private String turnFrom;

    private Integer reviewStatus;

    private String submitUser;

    private Date submitTime;

    private String reviewUser;

    private Date reviewTime;

    private String refuseReason;

    private String orderNo;

    private Integer effectiveReadCount;

    private Integer currentProgress;

    private Integer maxProgress;

    private Integer playTime;

    private boolean submitFlag;

    private Attachments attachments;

    private List<Attachments> attachmentsList;

    private List<String> ids;

    // 是否必读（0-否，1-是）
    private Integer isRequiredReading;
    // 推荐阅读时长
    private Integer suggestRate;

    //必读范围（1全员必学，2书记必学，3党员必学）
    private Integer mustType;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrgid() {
        return orgid;
    }

    public void setOrgid(String orgid) {
        this.orgid = orgid;
    }

    public Integer getPublicscope() {
        return publicscope;
    }

    public void setPublicscope(Integer publicscope) {
        this.publicscope = publicscope;
    }

    public String getStrpublicscope() {
        return strpublicscope;
    }

    public void setStrpublicscope(String strpublicscope) {
        this.strpublicscope = strpublicscope;
    }

    public String getOrgname() {
        return orgname;
    }

    public void setOrgname(String orgname) {
        this.orgname = orgname;
    }

    public Integer getFiletype() {
        return filetype;
    }

    public void setFiletype(Integer filetype) {
        this.filetype = filetype;
    }

    public String getStrfiletype() {
        return strfiletype;
    }

    public void setStrfiletype(String strfiletype) {
        this.strfiletype = strfiletype;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getStorepath() {
        return storepath;
    }

    public void setStorepath(String storepath) {
        this.storepath = storepath;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getThemepic() {
        return themepic;
    }

    public void setThemepic(String themepic) {
        this.themepic = themepic;
    }

    public String getContenttype() {
        return contenttype;
    }

    public void setContenttype(String contenttype) {
        this.contenttype = contenttype;
    }

    public String getStrcontenttype() {
        return strcontenttype;
    }

    public void setStrcontenttype(String strcontenttype) {
        this.strcontenttype = strcontenttype;
    }

    public Integer getSsstatus() {
        return ssstatus;
    }

    public void setSsstatus(Integer ssstatus) {
        this.ssstatus = ssstatus;
    }

    public String getStrssstatus() {
        return strssstatus;
    }

    public void setStrssstatus(String strssstatus) {
        this.strssstatus = strssstatus;
    }

    public Integer getIsexcellent() {
        return isexcellent;
    }

    public void setIsexcellent(Integer isexcellent) {
        this.isexcellent = isexcellent;
    }

    public Integer getReadcount() {
        return readcount;
    }

    public void setReadcount(Integer readcount) {
        this.readcount = readcount;
    }

    public String getSummaryWithHtml() {
        return summaryWithHtml;
    }

    public void setSummaryWithHtml(String summaryWithHtml) {
        this.summaryWithHtml = summaryWithHtml;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public Integer getIsdeleted() {
        return isdeleted;
    }

    public void setIsdeleted(Integer isdeleted) {
        this.isdeleted = isdeleted;
    }

    public String getCreatedby() {
        return createdby;
    }

    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateddate() {
        return createddate;
    }

    public void setCreateddate(Date createddate) {
        this.createddate = createddate;
    }

    public String getModifiedby() {
        return modifiedby;
    }

    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }

    public Integer getEnablereview() {
        return enablereview;
    }

    public void setEnablereview(Integer enablereview) {
        this.enablereview = enablereview;
    }

    public Integer getEnablethumbs() {
        return enablethumbs;
    }

    public void setEnablethumbs(Integer enablethumbs) {
        this.enablethumbs = enablethumbs;
    }

    public Integer getReviewscount() {
        return reviewscount;
    }

    public void setReviewscount(Integer reviewscount) {
        this.reviewscount = reviewscount;
    }

    public Integer getThumbscount() {
        return thumbscount;
    }

    public void setThumbscount(Integer thumbscount) {
        this.thumbscount = thumbscount;
    }

    public String getMakeFrom() {
        return makeFrom;
    }

    public void setMakeFrom(String makeFrom) {
        this.makeFrom = makeFrom;
    }

    public String getTurnFrom() {
        return turnFrom;
    }

    public void setTurnFrom(String turnFrom) {
        this.turnFrom = turnFrom;
    }

    public Integer getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getSubmitUser() {
        return submitUser;
    }

    public void setSubmitUser(String submitUser) {
        this.submitUser = submitUser;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public String getReviewUser() {
        return reviewUser;
    }

    public void setReviewUser(String reviewUser) {
        this.reviewUser = reviewUser;
    }

    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public String getRefuseReason() {
        return refuseReason;
    }

    public void setRefuseReason(String refuseReason) {
        this.refuseReason = refuseReason;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getEffectiveReadCount() {
        return effectiveReadCount;
    }

    public void setEffectiveReadCount(Integer effectiveReadCount) {
        this.effectiveReadCount = effectiveReadCount;
    }

    public Integer getCurrentProgress() {
        return currentProgress;
    }

    public void setCurrentProgress(Integer currentProgress) {
        this.currentProgress = currentProgress;
    }

    public Integer getMaxProgress() {
        return maxProgress;
    }

    public void setMaxProgress(Integer maxProgress) {
        this.maxProgress = maxProgress;
    }

    public Integer getPlayTime() {
        return playTime;
    }

    public void setPlayTime(Integer playTime) {
        this.playTime = playTime;
    }

    public boolean isSubmitFlag() {
        return submitFlag;
    }

    public void setSubmitFlag(boolean submitFlag) {
        this.submitFlag = submitFlag;
    }

    public Attachments getAttachments() {
        return attachments;
    }

    public void setAttachments(Attachments attachments) {
        this.attachments = attachments;
    }

    public List<Attachments> getAttachmentsList() {
        return attachmentsList;
    }

    public void setAttachmentsList(List<Attachments> attachmentsList) {
        this.attachmentsList = attachmentsList;
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    public Integer getIsRequiredReading() {
        return isRequiredReading;
    }

    public void setIsRequiredReading(Integer isRequiredReading) {
        this.isRequiredReading = isRequiredReading;
    }

    public Integer getSuggestRate() {
        return suggestRate;
    }

    public void setSuggestRate(Integer suggestRate) {
        this.suggestRate = suggestRate;
    }

    public Integer getMustType() {
        return mustType;
    }

    public void setMustType(Integer mustType) {
        this.mustType = mustType;
    }
}
