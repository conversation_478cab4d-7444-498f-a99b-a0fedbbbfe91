package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

/**
 * 组织贡献
 */
public class OrganizationContributionDTO extends GenericBean {
    private static final long serialVersionUID = -6328254405643600628L;
    /**
     * 新闻发布量
     */
    private int newsCount;

    /**
     * 视频发布量
     */
    private int videoCount;

    /**
     * 音频发布量
     */
    private int audioCount;

    /**
     * 图书发布量
     */
    private int bookCount;

    public int getNewsCount() {
        return newsCount;
    }

    public void setNewsCount(int newsCount) {
        this.newsCount = newsCount;
    }

    public int getVideoCount() {
        return videoCount;
    }

    public void setVideoCount(int videoCount) {
        this.videoCount = videoCount;
    }

    public int getAudioCount() {
        return audioCount;
    }

    public void setAudioCount(int audioCount) {
        this.audioCount = audioCount;
    }

    public int getBookCount() {
        return bookCount;
    }

    public void setBookCount(int bookCount) {
        this.bookCount = bookCount;
    }
}
