package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

public class WorkTaskDTO extends GenericBean {
    private static final long serialVersionUID = 1L;

    private String id;

    private String taskname;

    private String summary;

    private String theme;

    private String userid;

    private Integer tasktype;

    private Integer type;

    private String objid;

    private String subobjid;

    private Integer taskstatus;

    private Integer isdeleted;

    private String createdby;

    private Date createddate;

    private String modifiedby;

    private Date modifieddate;

    private String strcreateddate;

    private Integer status;

    private String starttime;

    private String topic;

    private String orgname;

    private String endtime;

    private String moderator;

    private String username;

    private String lastModefyTime;

    private Integer isjoin;

    private String userRole;

    private String extfld1;

    private String extfld2;

    private String extfld3;

    /**
     * 会议是否异常 0：正常，1：异常
     */
    private Integer iserror;

    /**
     * 开展渠道：01线下，02线上
     */
    private String channelType;

    /**
     * 流程类型：01主流程，02分发流程
     **/
    private String distributionType;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTaskname() {
        return taskname;
    }

    public void setTaskname(String taskname) {
        this.taskname = taskname;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getTheme() {
        return theme;
    }

    public void setTheme(String theme) {
        this.theme = theme;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public Integer getTasktype() {
        return tasktype;
    }

    public void setTasktype(Integer tasktype) {
        this.tasktype = tasktype;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getObjid() {
        return objid;
    }

    public void setObjid(String objid) {
        this.objid = objid;
    }

    public String getSubobjid() {
        return subobjid;
    }

    public void setSubobjid(String subobjid) {
        this.subobjid = subobjid;
    }

    public Integer getTaskstatus() {
        return taskstatus;
    }

    public void setTaskstatus(Integer taskstatus) {
        this.taskstatus = taskstatus;
    }

    public Integer getIsdeleted() {
        return isdeleted;
    }

    public void setIsdeleted(Integer isdeleted) {
        this.isdeleted = isdeleted;
    }

    public String getCreatedby() {
        return createdby;
    }

    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    public Date getCreateddate() {
        return createddate;
    }

    public void setCreateddate(Date createddate) {
        this.createddate = createddate;
    }

    public String getModifiedby() {
        return modifiedby;
    }

    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }

    public String getStrcreateddate() {
        return strcreateddate;
    }

    public void setStrcreateddate(String strcreateddate) {
        this.strcreateddate = strcreateddate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStarttime() {
        return starttime;
    }

    public void setStarttime(String starttime) {
        this.starttime = starttime;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getOrgname() {
        return orgname;
    }

    public void setOrgname(String orgname) {
        this.orgname = orgname;
    }

    public String getEndtime() {
        return endtime;
    }

    public void setEndtime(String endtime) {
        this.endtime = endtime;
    }

    public String getModerator() {
        return moderator;
    }

    public void setModerator(String moderator) {
        this.moderator = moderator;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getLastModefyTime() {
        return lastModefyTime;
    }

    public void setLastModefyTime(String lastModefyTime) {
        this.lastModefyTime = lastModefyTime;
    }

    public Integer getIsjoin() {
        return isjoin;
    }

    public void setIsjoin(Integer isjoin) {
        this.isjoin = isjoin;
    }

    public String getUserRole() {
        return userRole;
    }

    public void setUserRole(String userRole) {
        this.userRole = userRole;
    }

    public String getExtfld1() {
        return extfld1;
    }

    public void setExtfld1(String extfld1) {
        this.extfld1 = extfld1;
    }

    public String getExtfld2() {
        return extfld2;
    }

    public void setExtfld2(String extfld2) {
        this.extfld2 = extfld2;
    }

    public String getExtfld3() {
        return extfld3;
    }

    public void setExtfld3(String extfld3) {
        this.extfld3 = extfld3;
    }

    public Integer getIserror() {
        return iserror;
    }

    public void setIserror(Integer iserror) {
        this.iserror = iserror;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getDistributionType() {
        return distributionType;
    }

    public void setDistributionType(String distributionType) {
        this.distributionType = distributionType;
    }
}