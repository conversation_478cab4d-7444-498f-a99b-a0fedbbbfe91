package com.cmos.pbms.beans.enums;

public enum PtPlanTinyTypeEnum {

    ZBBZJQ("zbbzjq", "支部班子健全"),
    ZZAQHJ("zzaqhj", "组织按期换届"),
    SJLZPC("sjlzpc", "书记履职评测"),
    KZSHYK("kzshyk", "如期开展“三会一课”"),
    KZJHXX("kzjhxx", "开展计划学习"),
    ZDZGFA("zdzgfa", "制定整改方案");

    /**
     * 类型值
     */
    private String code;
    /**
     * 类型描述
     */
    private String desc;

    /**
     * 私有构造,防止被外部调用
     *
     * @param code 类型值
     * @param desc 类型描述
     */
    PtPlanTinyTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 取类型描述
     *
     * @return 类型描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 取类型值
     *
     * @return 类型值
     */
    public String getCode() {
        return code;
    }

    public static String getDescByCode(String code) {
        PtPlanTinyTypeEnum[] PtPlanTinyTypeEnums = values();
        for (PtPlanTinyTypeEnum ptEnum : PtPlanTinyTypeEnums) {
            if (ptEnum.getCode().equals(code)) {
                return ptEnum.getDesc();
            }
        }
        return "未知类型";
    }
}
