package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class VwQuestionStoreDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    private String qDatadate;

    private String qaTitle;

    private Map<String, Integer> qCollectData = new HashMap<>();

    private List<VwQuestionDTO> vwQuestionDTOList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getqDatadate() {
        return qDatadate;
    }

    public void setqDatadate(String qDatadate) {
        this.qDatadate = qDatadate;
    }

    public String getQaTitle() {
        return qaTitle;
    }

    public void setQaTitle(String qaTitle) {
        this.qaTitle = qaTitle;
    }

    public List<VwQuestionDTO> getVwQuestionDTOList() {
        return vwQuestionDTOList;
    }

    public void setVwQuestionDTOList(List<VwQuestionDTO> vwQuestionDTOList) {
        this.vwQuestionDTOList = vwQuestionDTOList;
    }

    public Map<String, Integer> getqCollectData() {
        return qCollectData;
    }

    public void setqCollectData(Map<String, Integer> qCollectData) {
        this.qCollectData = qCollectData;
    }
}