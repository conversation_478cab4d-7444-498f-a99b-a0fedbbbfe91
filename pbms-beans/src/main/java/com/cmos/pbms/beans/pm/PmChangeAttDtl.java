package com.cmos.pbms.beans.pm;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

public class PmChangeAttDtl extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    private String orgChangeId;

    private String attType;

    private String attCode;

    private String attName;

    private String attFromValue;

    private String attToValue;

    private Integer isdeleted;

    private String createdby;

    private Date createddate;

    private String modifiedby;

    private Date modifieddate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrgChangeId() {
        return orgChangeId;
    }

    public void setOrgChangeId(String orgChangeId) {
        this.orgChangeId = orgChangeId;
    }

    public String getAttType() {
        return attType;
    }

    public void setAttType(String attType) {
        this.attType = attType;
    }

    public String getAttCode() {
        return attCode;
    }

    public void setAttCode(String attCode) {
        this.attCode = attCode;
    }

    public String getAttName() {
        return attName;
    }

    public void setAttName(String attName) {
        this.attName = attName;
    }

    public String getAttFromValue() {
        return attFromValue;
    }

    public void setAttFromValue(String attFromValue) {
        this.attFromValue = attFromValue;
    }

    public String getAttToValue() {
        return attToValue;
    }

    public void setAttToValue(String attToValue) {
        this.attToValue = attToValue;
    }

    public Integer getIsdeleted() {
        return isdeleted;
    }

    public void setIsdeleted(Integer isdeleted) {
        this.isdeleted = isdeleted;
    }

    public String getCreatedby() {
        return createdby;
    }

    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    public Date getCreateddate() {
        return createddate;
    }

    public void setCreateddate(Date createddate) {
        this.createddate = createddate;
    }

    public String getModifiedby() {
        return modifiedby;
    }

    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }
}