package com.cmos.pbms.beans.enums;

/**
 * @类名：MeetingSchedulerTypeEnum
 * @类的作用：
 * @作者：牛文钻
 * @创建时间：2019/3/15
 */
public enum MeetingSchedulerTypeEnum {

    UNKNOW("Unknow", "Meeting-Check", "未知，用于初始化枚举"),

    QTOBEPlANNED("Q-ToBePlanned", "Meeting-Check", "季度待计划状态异常检查"),
    QTOBENOTIFIED("Q-ToBeNotified", "Meeting-Check", "季度待通知状态异常检查"),
    QTOBEHELD("Q-ToBeHeld", "Meeting-Check", "季度待召开状态异常检查"),
    QTOBECONCLUDED("Q-ToBeConcluded", "Meeting-Check", "季度待归档状态异常检查"),
    QBEOVERDUE("Q-BeOverdue", "Meeting-Check", "季度超期未完成状态异常检查"),

    MTOBEPlANNED("M-ToBePlanned", "Meeting-Check", "月度待计划状态异常检查"),
    MTOBENOTIFIED("M-ToBeNotified", "Meeting-Check", "月度待通知状态异常检查"),
    MTOBEHELD("M-ToBeHeld", "Meeting-Check", "月度待召开状态异常检查"),
    MTOBECONCLUDED("M-ToBeConcluded", "Meeting-Check", "月度待归档状态异常检查"),
    MBEOVERDUE("M-BeOverdue", "Meeting-Check", "月度超期未完成状态异常检查");

    private String code;

    private String dict;

    private String desc;

    MeetingSchedulerTypeEnum(String code, String dict, String desc) {
        this.code = code;
        this.dict = dict;
        this.desc = desc;
    }

    /**
     * 取状态描述
     *
     * @return 状态描述
     */
    public String getDesc() {
        return desc;
    }

    public String getDict() {
        return dict;
    }

    /**
     * 取状态值
     *
     * @return 状态值
     */
    public String getCode() {
        return code;
    }
}
