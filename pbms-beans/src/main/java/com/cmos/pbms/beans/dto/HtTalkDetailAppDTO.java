package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;
import com.cmos.pbms.beans.common.Attachments;

import java.util.Date;

public class HtTalkDetailAppDTO extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String htId;

    private String conventioneerId;

    private String userId;

    private Integer cType;

    private String htTitle;

    private String speakers;

    private String speakersPost;

    private Date htStartTime;

    private Date htEndTime;

    private String htAddress;

    private String audiences;

    private String recorders;

    private String attendants;

    private Integer isGenSign;

    private Integer isSigned;

    Attachments recordFile;

    public String getHtId() {
        return htId;
    }

    public void setHtId(String htId) {
        this.htId = htId;
    }

    public String getConventioneerId() {
        return conventioneerId;
    }

    public void setConventioneerId(String conventioneerId) {
        this.conventioneerId = conventioneerId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getcType() {
        return cType;
    }

    public void setcType(Integer cType) {
        this.cType = cType;
    }

    public String getHtTitle() {
        return htTitle;
    }

    public void setHtTitle(String htTitle) {
        this.htTitle = htTitle;
    }

    public String getSpeakers() {
        return speakers;
    }

    public void setSpeakers(String speakers) {
        this.speakers = speakers;
    }

    public String getSpeakersPost() {
        return speakersPost;
    }

    public void setSpeakersPost(String speakersPost) {
        this.speakersPost = speakersPost;
    }

    public Date getHtStartTime() {
        return htStartTime;
    }

    public void setHtStartTime(Date htStartTime) {
        this.htStartTime = htStartTime;
    }

    public Date getHtEndTime() {
        return htEndTime;
    }

    public void setHtEndTime(Date htEndTime) {
        this.htEndTime = htEndTime;
    }

    public String getHtAddress() {
        return htAddress;
    }

    public void setHtAddress(String htAddress) {
        this.htAddress = htAddress;
    }

    public String getAudiences() {
        return audiences;
    }

    public void setAudiences(String audiences) {
        this.audiences = audiences;
    }

    public String getRecorders() {
        return recorders;
    }

    public void setRecorders(String recorders) {
        this.recorders = recorders;
    }

    public String getAttendants() {
        return attendants;
    }

    public void setAttendants(String attendants) {
        this.attendants = attendants;
    }

    public Integer getIsGenSign() {
        return isGenSign;
    }

    public void setIsGenSign(Integer isGenSign) {
        this.isGenSign = isGenSign;
    }

    public Integer getIsSigned() {
        return isSigned;
    }

    public void setIsSigned(Integer isSigned) {
        this.isSigned = isSigned;
    }

    public Attachments getRecordFile() {
        return recordFile;
    }

    public void setRecordFile(Attachments recordFile) {
        this.recordFile = recordFile;
    }
}
