package com.cmos.pbms.beans.enums;

/**
 * @类名：ScoreRuleEnum
 * @类的作用：积分规则代码枚举类
 * @作者：牛文钻
 * @创建时间：2019/5/10
 */
public enum ScoreRuleEnum {

    SKANSWEREXELLENT(70, "90001", "每日一练满分规则"),
    SKANSWERCOMPLETE(70, "90002", "每日一练非满分规则"),
    SKEVENTNOTICE(70, "90003", "事件提醒答题积分");

    private Integer objType;

    private String ruleNo;

    private String desc;

    ScoreRuleEnum(Integer objType, String ruleNo, String desc) {
        this.objType = objType;
        this.ruleNo = ruleNo;
        this.desc = desc;
    }

    public Integer getObjType() {
        return objType;
    }

    public void setObjType(Integer objType) {
        this.objType = objType;
    }

    public String getRuleNo() {
        return ruleNo;
    }

    public void setRuleNo(String ruleNo) {
        this.ruleNo = ruleNo;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
