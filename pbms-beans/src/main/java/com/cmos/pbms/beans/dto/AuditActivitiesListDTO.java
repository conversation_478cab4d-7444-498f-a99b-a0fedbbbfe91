package com.cmos.pbms.beans.dto;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

public class AuditActivitiesListDTO extends GenericBean {
    private static final long serialVersionUID = 1L;

    private String id;

    private String actname;

    private Integer actstatus;

    private String orgfname;

    private Integer acttype;

    private String leader;

    private Integer isdeleted;

    private String createdby;

    private Date createddate;

    private String modifiedby;

    private Date modifieddate;

    private Date subAuditTime;

    private String subAuditUser;

    private Date auditTime;

    private String auditUser;

    private String auditReason;

    private Integer auditState;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getActname() {
        return actname;
    }

    public void setActname(String actname) {
        this.actname = actname;
    }

    public Integer getActstatus() {
        return actstatus;
    }

    public void setActstatus(Integer actstatus) {
        this.actstatus = actstatus;
    }

    public String getOrgfname() {
        return orgfname;
    }

    public void setOrgfname(String orgfname) {
        this.orgfname = orgfname;
    }

    public Integer getActtype() {
        return acttype;
    }

    public void setActtype(Integer acttype) {
        this.acttype = acttype;
    }

    public String getLeader() {
        return leader;
    }

    public void setLeader(String leader) {
        this.leader = leader;
    }

    public Integer getIsdeleted() {
        return isdeleted;
    }

    public void setIsdeleted(Integer isdeleted) {
        this.isdeleted = isdeleted;
    }

    public String getCreatedby() {
        return createdby;
    }

    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    public Date getCreateddate() {
        return createddate;
    }

    public void setCreateddate(Date createddate) {
        this.createddate = createddate;
    }

    public String getModifiedby() {
        return modifiedby;
    }

    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }

    public Date getSubAuditTime() {
        return subAuditTime;
    }

    public void setSubAuditTime(Date subAuditTime) {
        this.subAuditTime = subAuditTime;
    }

    public String getSubAuditUser() {
        return subAuditUser;
    }

    public void setSubAuditUser(String subAuditUser) {
        this.subAuditUser = subAuditUser;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getAuditUser() {
        return auditUser;
    }

    public void setAuditUser(String auditUser) {
        this.auditUser = auditUser;
    }

    public String getAuditReason() {
        return auditReason;
    }

    public void setAuditReason(String auditReason) {
        this.auditReason = auditReason;
    }

    public Integer getAuditState() {
        return auditState;
    }

    public void setAuditState(Integer auditState) {
        this.auditState = auditState;
    }
}
