package com.cmos.pbms.beans.bn;

import com.cmos.common.bean.GenericBean;

import java.util.Date;

public class BnBranchNews extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    private String orgId;

    private String userId;

    private String pubOrgId;

    private Date pubDate;

    private Integer bnType;

    private String bnLabel;

    private Integer conType;

    private String content;

    private String videoName;

    private String videoId;

    private String videoImgUrl;

    private String forwardImageUrl;

    private String forwardTitle;

    private String forwardUrl;

    private String bnAddress;

    private String bnLongitude;

    private String bnLatitude;

    private Integer thuCount;

    private Integer revCount;

    private String objType;

    private String objId;

    private Integer isdeleted;

    private String createdby;

    private Date createddate;

    private String modifiedby;

    private Date modifieddate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Date getPubDate() {
        return pubDate;
    }

    public void setPubDate(Date pubDate) {
        this.pubDate = pubDate;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPubOrgId() {
        return pubOrgId;
    }

    public void setPubOrgId(String pubOrgId) {
        this.pubOrgId = pubOrgId;
    }

    public Integer getBnType() {
        return bnType;
    }

    public void setBnType(Integer bnType) {
        this.bnType = bnType;
    }

    public String getBnLabel() {
        return bnLabel;
    }

    public void setBnLabel(String bnLabel) {
        this.bnLabel = bnLabel;
    }

    public Integer getConType() {
        return conType;
    }

    public void setConType(Integer conType) {
        this.conType = conType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getVideoName() {
        return videoName;
    }

    public void setVideoName(String videoName) {
        this.videoName = videoName;
    }

    public String getVideoId() {
        return videoId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    public String getVideoImgUrl() {
        return videoImgUrl;
    }

    public void setVideoImgUrl(String videoImgUrl) {
        this.videoImgUrl = videoImgUrl;
    }

    public String getForwardImageUrl() {
        return forwardImageUrl;
    }

    public void setForwardImageUrl(String forwardImageUrl) {
        this.forwardImageUrl = forwardImageUrl;
    }

    public String getForwardTitle() {
        return forwardTitle;
    }

    public void setForwardTitle(String forwardTitle) {
        this.forwardTitle = forwardTitle;
    }

    public String getForwardUrl() {
        return forwardUrl;
    }

    public void setForwardUrl(String forwardUrl) {
        this.forwardUrl = forwardUrl;
    }

    public String getBnAddress() {
        return bnAddress;
    }

    public void setBnAddress(String bnAddress) {
        this.bnAddress = bnAddress;
    }

    public String getBnLongitude() {
        return bnLongitude;
    }

    public void setBnLongitude(String bnLongitude) {
        this.bnLongitude = bnLongitude;
    }

    public String getBnLatitude() {
        return bnLatitude;
    }

    public void setBnLatitude(String bnLatitude) {
        this.bnLatitude = bnLatitude;
    }

    public Integer getThuCount() {
        return thuCount;
    }

    public void setThuCount(Integer thuCount) {
        this.thuCount = thuCount;
    }

    public Integer getRevCount() {
        return revCount;
    }

    public void setRevCount(Integer revCount) {
        this.revCount = revCount;
    }

    public String getObjType() {
        return objType;
    }

    public void setObjType(String objType) {
        this.objType = objType;
    }

    public String getObjId() {
        return objId;
    }

    public void setObjId(String objId) {
        this.objId = objId;
    }

    public Integer getIsdeleted() {
        return isdeleted;
    }

    public void setIsdeleted(Integer isdeleted) {
        this.isdeleted = isdeleted;
    }

    public String getCreatedby() {
        return createdby;
    }

    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    public Date getCreateddate() {
        return createddate;
    }

    public void setCreateddate(Date createddate) {
        this.createddate = createddate;
    }

    public String getModifiedby() {
        return modifiedby;
    }

    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
    }
}