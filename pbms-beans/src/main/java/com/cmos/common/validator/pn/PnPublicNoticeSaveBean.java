package com.cmos.common.validator.pn;

import com.cmos.common.bean.GenericBean;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.Date;

public class PnPublicNoticeSaveBean extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    @NotBlank(message = "标题不能为空")
    private String noticeTitle;

    @NotNull(message = "有效开始时间不能为空")
    private Date startTime;

    @NotNull(message = "有效结束时间不能为空")
    private Date endTime;

    @NotBlank(message = "公告通知组织范围不能为空")
    private String orgScopeList;

    //    @NotBlank(message = "用户标签范围不能为空")
    private String userLabelScope;

    @NotBlank(message = "所属组织不能为空")
    private String orgId;

    private String noticeSource;

    @NotNull(message = "公告类型不能为空")
    private Integer noticeType;

    @NotBlank(message = "公告正文不能为空")
    private String noticeContentWithHtml;

    private Integer isSubmit;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNoticeTitle() {
        return noticeTitle;
    }

    public void setNoticeTitle(String noticeTitle) {
        this.noticeTitle = noticeTitle;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getOrgScopeList() {
        return orgScopeList;
    }

    public void setOrgScopeList(String orgScopeList) {
        this.orgScopeList = orgScopeList;
    }

    public String getUserLabelScope() {
        return userLabelScope;
    }

    public void setUserLabelScope(String userLabelScope) {
        this.userLabelScope = userLabelScope;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getNoticeSource() {
        return noticeSource;
    }

    public void setNoticeSource(String noticeSource) {
        this.noticeSource = noticeSource;
    }

    public Integer getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(Integer noticeType) {
        this.noticeType = noticeType;
    }

    public String getNoticeContentWithHtml() {
        return noticeContentWithHtml;
    }

    public void setNoticeContentWithHtml(String noticeContentWithHtml) {
        this.noticeContentWithHtml = noticeContentWithHtml;
    }

    public Integer getIsSubmit() {
        return isSubmit;
    }

    public void setIsSubmit(Integer isSubmit) {
        this.isSubmit = isSubmit;
    }
}
