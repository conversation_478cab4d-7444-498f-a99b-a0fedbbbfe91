package com.cmos.common.validator.pm;

import com.cmos.common.bean.GenericBean;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 查询下级党组参数校验实体类
 *
 * <AUTHOR>
 */
public class VQuerySubOrgBean extends GenericBean {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private Integer page;

    @NotNull(message = "页大小不能为1")
    @Min(value = 1, message = "页大小不能小于1")
    private Integer limit;

    private String parentid;

    private String parentname;

    private String orgsname;

    private String telephone;

    private String fax;

    private String orgCode;

    private String orgName;

    @Min(0)
    private Integer orgstatus;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public String getParentid() {
        return "".equals(parentid) ? null : parentid;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }

    public String getParentname() {
        return "".equals(parentname) ? null : parentname;
    }

    public void setParentname(String parentname) {
        this.parentname = parentname;
    }

    public String getOrgsname() {
        return "".equals(orgsname) ? null : orgsname;
    }

    public void setOrgsname(String orgsname) {
        this.orgsname = orgsname;
    }

    public String getTelephone() {
        return "".equals(telephone) ? null : telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getFax() {
        return "".equals(fax) ? null : fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getOrgstatus() {
        return orgstatus;
    }

    public void setOrgstatus(Integer orgstatus) {
        this.orgstatus = orgstatus;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}
