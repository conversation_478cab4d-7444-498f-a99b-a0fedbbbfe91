package com.cmos.common.validator.an;

import com.cmos.common.bean.GenericBean;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class VAnTasksSearchBean extends GenericBean {
    private static final long serialVersionUID = 1L;

    // 页码
    @NotNull(message = "page不能为null")
    @Min(value = 1, message = "page不能小于1")
    private Integer page;

    // 页大小
    @NotNull(message = "limit不能为null")
    @Min(value = 1, message = "limit不能小于1")
    private Integer limit;

    private String id;

    private String taskType;

    private String taskYear;

    private Integer isdeleted;

    private String createdby;

    @JsonIgnore
    private String orgIdLever1;

    private Date createddate;

    private String modifiedby;

    private Date modifieddate;

    private Date startTime;

    private Date endTime;

    private Integer status;

    private String organizationIds;
    //年度任务模板
    private Boolean isTemplate = false;
    // 模板适用组织类型（1为党委，3为党总支，4为党支部，5为党小组，6为党委），多个以半角符合","分隔
    private String templateOrgType;

}
