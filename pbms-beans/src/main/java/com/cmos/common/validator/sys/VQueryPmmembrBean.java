package com.cmos.common.validator.sys;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 查询党组成员参数校验实体类
 *
 * <AUTHOR>
 */
public class VQueryPmmembrBean {

    @NotNull(message = "页码参数不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private Integer page;

    @NotNull(message = "页大小参数不能为1")
    @Min(value = 1, message = "页大小不能小于1")
    private Integer limit;

    private String orgid;

    private String username;

    private String mobilephone;

    private String staffstatus;

    // 判断访问接口的是移动设备还是PC端
    private String client;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public String getOrgid() {
        return "".equals(orgid) ? null : orgid;
    }

    public void setOrgid(String orgid) {
        this.orgid = orgid;
    }

    public String getUsername() {
        return "".equals(username) ? null : username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getMobilephone() {
        return "".equals(mobilephone) ? null : mobilephone;
    }

    public void setMobilephone(String mobilephone) {
        this.mobilephone = mobilephone;
    }

    public String getStaffstatus() {
        return staffstatus;
    }

    public void setStaffstatus(String staffstatus) {
        this.staffstatus = staffstatus;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }
}
