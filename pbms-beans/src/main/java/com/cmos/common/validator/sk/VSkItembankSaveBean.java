package com.cmos.common.validator.sk;

import com.cmos.common.bean.GenericBean;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @类名：VSkItembankSaveBean
 * @类的作用：
 * @作者：牛文钻
 * @创建时间：2019/5/13
 */
public class VSkItembankSaveBean extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    @NotBlank(message = "题库名称不能为空。")
    private String title;

    private String bankDesc;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBankDesc() {
        return bankDesc;
    }

    public void setBankDesc(String bankDesc) {
        this.bankDesc = bankDesc;
    }
}
