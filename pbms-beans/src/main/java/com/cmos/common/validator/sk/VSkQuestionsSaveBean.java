package com.cmos.common.validator.sk;

import com.cmos.common.bean.GenericBean;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * @类名：VSkQuestionsSaveBean
 * @类的作用：
 * @作者：牛文钻
 * @创建时间：2019/5/14
 */
public class VSkQuestionsSaveBean extends GenericBean {

    private static final long serialVersionUID = 1L;

    private String id;

    @NotBlank(message = "所属题库不能为空。")
    private String bankId;

    private String chapter;

    @NotNull(message = "题型不能为空。")
    private Integer questionType;

    @NotNull(message = "试题状态不能为空。")
    private Integer status;

    @NotBlank(message = "题干不能为空。")
    private String stemWithJSON;

    @NotBlank(message = "答题解析不能为空。")
    private String analysisWithJSON;

    @NotBlank(message = "答案选项不能为空。")
    private String optionsJson;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public String getChapter() {
        return chapter;
    }

    public void setChapter(String chapter) {
        this.chapter = chapter;
    }

    public Integer getQuestionType() {
        return questionType;
    }

    public void setQuestionType(Integer questionType) {
        this.questionType = questionType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStemWithJSON() {
        return stemWithJSON;
    }

    public void setStemWithJSON(String stemWithJSON) {
        this.stemWithJSON = stemWithJSON;
    }

    public String getAnalysisWithJSON() {
        return analysisWithJSON;
    }

    public void setAnalysisWithJSON(String analysisWithJSON) {
        this.analysisWithJSON = analysisWithJSON;
    }

    public String getOptionsJson() {
        return optionsJson;
    }

    public void setOptionsJson(String optionsJson) {
        this.optionsJson = optionsJson;
    }
}
