package com.cmos.common.validator.me;

import com.cmos.common.bean.GenericBean;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @类名：VMeetingsSummaryListSearchBean
 * @类的作用：
 * @作者：牛文钻
 * @创建时间：2018/7/28
 */
public class VMeetingsSummaryListSearchBean extends GenericBean {

    // 页码
    @NotNull(message = "page不能为null")
    @Min(value = 1, message = "page不能小于1")
    private Integer page;

    // 页大小
    @NotNull(message = "limit不能为null")
    @Min(value = 1, message = "limit不能小于1")
    private Integer limit;

    // 会议议题
    private String topic;

    // 开始时间
    private String starttime;

    // 新结束时间
    private String endtime;

    // 党组织名称
    private String orgname;

    //党组织code参数
    private String orgCodeTerm;

    // 状态
    private Integer status;

    private Integer auditState;

    // 是否删除
    private Integer isDeleted;

    //参会人
    private String joiner;

    private String moderator;

    //主持人
    private String meHost;

    //授课人
    private String teacher;

    /**
     * 权限角色
     */
    private String currentOrgCode;

    /**
     * 是否异常（1-是；0-否）
     */
    private Integer iserror;

    /**
     * 会议类型 （10支部党员大会；20支部委员会；30党小组会；40党课；50集中学习）
     **/
    private Integer type;

    /**
     * 是否计划内 （1-是，0-否）
     **/
    private Integer isplan;

    private String sortRule;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getStarttime() {
        return starttime;
    }

    public void setStarttime(String starttime) {
        this.starttime = starttime;
    }

    public String getEndtime() {
        return endtime;
    }

    public void setEndtime(String endtime) {
        this.endtime = endtime;
    }

    public String getOrgname() {
        return orgname;
    }

    public void setOrgname(String orgname) {
        this.orgname = orgname;
    }

    public String getOrgCodeTerm() {
        return orgCodeTerm;
    }

    public void setOrgCodeTerm(String orgCodeTerm) {
        this.orgCodeTerm = orgCodeTerm;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getAuditState() {
        return auditState;
    }

    public void setAuditState(Integer auditState) {
        this.auditState = auditState;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getJoiner() {
        return joiner;
    }

    public void setJoiner(String joiner) {
        this.joiner = joiner;
    }

    public String getModerator() {
        return moderator;
    }

    public void setModerator(String moderator) {
        this.moderator = moderator;
    }

    public String getMeHost() {
        return meHost;
    }

    public void setMeHost(String meHost) {
        this.meHost = meHost;
    }

    public String getTeacher() {
        return teacher;
    }

    public void setTeacher(String teacher) {
        this.teacher = teacher;
    }

    public Integer getIserror() {
        return iserror;
    }

    public void setIserror(Integer iserror) {
        this.iserror = iserror;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCurrentOrgCode() {
        return currentOrgCode;
    }

    public void setCurrentOrgCode(String currentOrgCode) {
        this.currentOrgCode = currentOrgCode;
    }

    public Integer getIsplan() {
        return isplan;
    }

    public void setIsplan(Integer isplan) {
        this.isplan = isplan;
    }

    public String getSortRule() {
        return sortRule;
    }

    public void setSortRule(String sortRule) {
        this.sortRule = sortRule;
    }
}