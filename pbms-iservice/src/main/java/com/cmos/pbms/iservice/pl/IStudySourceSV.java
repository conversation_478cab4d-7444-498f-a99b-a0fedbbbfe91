package com.cmos.pbms.iservice.pl;

import com.cmos.common.exception.GeneralException;
import com.cmos.common.validator.pl.StudySourceReviewListSearchBean;
import com.cmos.common.validator.pl.VStudySourceSpecialBean;
import com.cmos.pbms.beans.pl.StudySource;
import com.cmos.pbms.beans.pl.StudySourceForAPP;
import com.github.pagehelper.PageInfo;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

public interface IStudySourceSV {
    /**
     * 根据id删除一条学习材料信息
     *
     * @param id 学习材料id
     * @return 受影响的数据记录数（int）
     */
    int deleteByPrimaryKey(String id);

    /**
     * 新增一条学习材料信息，只插入的record中不为空的字段
     *
     * @param record 学习材料对象
     * @return 受影响的数据记录数（int）
     */
    int insertSelective(StudySource record);

    /**
     * 根据id更新学习材料信息
     *
     * @param record 学习材料对象
     * @return 受影响的数据记录数（int）
     */
    int updateByPrimaryKeySelective(StudySource record);

    int updateByPrimaryKey(StudySource record);

    /**
     * 查询指定党支部指定年度视频发布量
     *
     * @param orgid 党支部id
     * @param year  年份
     * @return 视频发布量
     */
    int getVideoCountByOrgid(String orgid, String year);

    /**
     * 查询指定党支部指定年度音频发布量
     *
     * @param orgid 党支部id
     * @param year  年份
     * @return 音频发布量
     */
    int getAudioCountByOrgid(String orgid, String year);

    /**
     * 根据学习材料d获取学习材料信息
     *
     * @param id 学习材料对象id
     * @return 学习材料对象（StudySource）
     */
    StudySource getByPrimaryKey(String id);

    /**
     * 根据学习材料d获取学习材料信息
     *
     * @param id 学习材料对象id
     * @return 学习材料对象（StudySource）
     */
    StudySource getById(String id);

    /**
     * 根据查询条件分页获取学习材料信息
     *
     * @param pageNum  页码
     * @param pageSize 页大小
     * @return 学习材料对象List集合（List<StudySource>）
     */
    PageInfo<StudySource> getByPageWithParam(Integer pageNum, Integer pageSize, Map<String, Object> params);

    /**
     * 根据学习材料d获取学习材料信息--APP
     *
     * @param id 学习材料对象id-APP
     * @return 学习材料对象（StudySourceForAPP）
     */
    StudySourceForAPP getByIdForAPP(String id);

    StudySourceForAPP selectDetailByIdForAPP(String id);

    String selectUpIdForAPP(String title, String orgcode, Integer filetype, String id, String specialId);

    String selectDownIdForAPP(String title, String orgcode, Integer filetype, String id, String specialId);

    /**
     * 获取多媒体列表APP
     *
     * @param title 标题
     * @param orgid 当前用户组织id
     * @return 多媒体列表
     */
    PageInfo<StudySourceForAPP> getMedioListForAPP(Integer pageNum, Integer pageSize, String title, String orgid, Integer filetype);

    /**
     * 获取网上学习列表APP
     *
     * @param title 标题
     * @param orgid 当前用户组织id
     * @return 网上学习列表
     */
    PageInfo<StudySourceForAPP> getOnlineListForAPP(Integer pageNum, Integer pageSize, String title, String orgid, Integer isexcellent, String contenttype) throws ParseException;

    /**
     * 阅读数增加APP
     *
     * @param id 标题
     * @return 影响条数
     */
    int readCountAdd(String id);

    /**
     * 获取组织（包含下级）发布的资源数
     *
     * @param orgcode  组织code
     * @param filetype 资源类型（1：音频，2：视频，3：文章）
     * @return 资源数
     */
    int getPublishResourceCount(String orgcode, Integer filetype);

    /**
     * 获取组织（包含下级）发布资源阅读人次
     *
     * @param orgcode 组织code
     * @return
     */
    int getResourceReadTotal(String orgcode, Integer filetype);

    /**
     * 评论数增加
     *
     * @param id
     * @return
     */
    Integer reviewsCountAdd(String id);

    /**
     * 点赞数增加
     *
     * @param id
     * @return
     */
    Integer thumbsCountAdd(String id);

    /**
     * 有效阅读数增加
     *
     * @param id
     * @return
     */
    Integer effectiveReadCountAdd(String id, Integer thisTimePlayTime, String thisTimePlayFlg) throws GeneralException;

    /**
     * 评论数减1
     *
     * @param id
     * @return
     */
    Integer reviewsCountReduce(String id);

    /**
     * 点赞数减1
     *
     * @param id
     * @return
     */
    Integer thumbsCountReduce(String id);

    /**
     * 根据查询条件分页获取学习材料审核列表
     *
     * @return 学习材料对象List集合（List<StudySource>）
     */
    PageInfo<StudySource> getReviewPageInfo(StudySourceReviewListSearchBean searchBean);

    /**
     * 批量审核
     *
     * @param record
     * @return
     */
    int reviewSsInBatch(StudySource record);

    /**
     * 根据条件，查询专题内音视频列表
     *
     * @param paramBean 检索条件
     * @return
     */
    PageInfo<StudySource> getSpecMediaListByParams(VStudySourceSpecialBean paramBean);

    /**
     * 根据条件，查询非本专题内音视频列表
     *
     * @param paramBean 检索条件
     * @return
     */
    PageInfo<StudySource> getNoSpecMediaListByParams(VStudySourceSpecialBean paramBean);

    Map<String, List> getAPPFirstPage(Integer bookNum, Integer audioNum, Integer videoNum, Integer onlineNum, String codeStr, String userId);

    Map<String, List> searchAPPFirstPageByTitle(String title, String codeStr, String userId);
}
