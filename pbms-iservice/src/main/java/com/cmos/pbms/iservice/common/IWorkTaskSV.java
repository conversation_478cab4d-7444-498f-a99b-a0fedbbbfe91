package com.cmos.pbms.iservice.common;

import com.cmos.common.exception.GeneralException;
import com.cmos.pbms.beans.common.WorkTask;
import com.cmos.pbms.beans.dto.MeetingsListDTO;
import com.cmos.pbms.beans.dto.WorkTaskDTO;
import com.cmos.pbms.beans.dto.WorkTaskForAllDTO;
import com.cmos.pbms.beans.dto.WorkTaskForStudyDTO;
import com.cmos.pbms.beans.me.Meetings;
import com.cmos.pbms.beans.sys.Users;
import com.github.pagehelper.PageInfo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IWorkTaskSV {

    WorkTask getWorkTaskById(String id);

    /**
     * 新增一条任务信息，只插入的record中不为空的字段
     *
     * @param record 任务对象
     * @return 受影响的数据记录数（int）
     */
    int insertSelective(WorkTask record);

    /**
     * 批量存入任务
     *
     * @param workTaskList 任务列表
     * @return 影响行数
     */
    int insertInBatch(List<WorkTask> workTaskList);

    int insertCollection(List<WorkTask> taskList);

    /**
     * 根据id删除一条任务信息
     *
     * @param id 任务id
     * @return 受影响的数据记录数（int）
     */
    int deleteByPrimaryKey(String id);

    int deleteByIdLogic(String id);

    int deleteByObjUser(String objid, String userid);

    PageInfo<WorkTask> getByPageWithParam(Integer pageNum, Integer pageSize, Map<String, Object> params);

    PageInfo<WorkTask> getStudyByPage(Integer pageNum, Integer pageSize, String userid);

    /**
     * 根据当前用户及代办类型，查询代办列表
     *
     * @param pageNum  页码
     * @param pageSize 每页记录数
     * @param params   检索条件对象
     * @return 代办列表
     */
    PageInfo<WorkTaskDTO> getAgencyByType(Integer pageNum, Integer pageSize, Map<String, Object> params);

    /**
     * 根据当前用户及代办类型，查询代办列表
     *
     * @param params 检索条件对象
     * @return 代办列表
     */
    List<WorkTaskDTO> getAgencyListByType(Map<String, Object> params);

    /**
     * 查询某对象相关的所有待办
     *
     * @param objId    对象ID
     * @param tasktype 待办类型
     * @return 待办列表
     */
    List<WorkTask> getAgencyListByObjId(String objId, Integer tasktype);

    /**
     * 查询任务待办
     *
     * @param objId    事件ID
     * @param tasktype 待办任务类型
     * @param type     待办任务子类型
     * @param userid
     * @return
     */
    WorkTask getWorkTaskByObjIdAndTaskTypeAndType(String objId, Integer tasktype, Integer type, String userid, String roleId);

    /**
     * 更新待办任务状态
     *
     * @param objid
     * @param tasktype
     * @param taskstatus
     * @param userId
     * @param sysDate
     * @return
     */
    int doneByObjidAndType(String objid, Integer tasktype, Integer taskstatus, String userId, Date sysDate, Integer type);

    int doneByObjidAndTypeAndUserId(String objid, Integer tasktype, Integer taskstatus, String userId, Date sysDate, Integer type);

    int doneByObjidAndTypeAndUserId(String objid, Integer tasktype, Integer taskstatus, String userId, Integer type, String modifiedby, Date modifieddate);

    /**
     * 批量生成待办(FOR会议通知，排除操作者生成任务)
     *
     * @param meetings 任务关联的具体业务对象
     * @param userList 接收待办任务的人员列表
     * @param userId   当前操作人
     * @param sysDate  系统时间
     * @param taskname 任务名称
     * @param summary  任务概要
     * @param type     任务类别
     * @return 影响行数
     */
    void insertTaskInBatch(Meetings meetings, List<Users> userList, String userId, Date sysDate, String taskname, String summary, Integer type) throws GeneralException;

    int updateByPrimaryKey(WorkTask record);

    /**
     * 查询代办列表（含一期活动和二期三会一课）
     *
     * @param pageNum
     * @param pageSize
     * @param taskstatus
     * @return
     */
    PageInfo<WorkTaskForAllDTO> getAgencyForAllType(Integer pageNum, Integer pageSize, Integer taskstatus, String userid);

    List<String> selectAgencyIdsByObjId(Map<String, Object> param);

    int doneByIdsAndType(Map<String, Object> params);

    /**
     * 按组织删除相关会议信息
     *
     * @param orgId      组织ID
     * @param currUserId 当前用户
     * @param currTime   当前时间
     * @return
     */
    int deleteMeTaskByOrgId(String orgId, String currUserId, Date currTime);

    /**
     * 删除指定数据的指定类别的待办
     *
     * @param objid   数据ID
     * @param userId  当前用户
     * @param sysDate 当前时间
     * @param type    待办类别
     * @return
     */
    int deleteByObjidAndType(String objid, String userId, Date sysDate, Integer type);

    int deleteByObjidAndTaskType(String objid, String userId, String currUserId, Date sysDate, Integer taskType);

    PageInfo<MeetingsListDTO> getTaskMeetingsWithRole(Integer page, Integer limit, Map<String, Object> params);

    PageInfo<WorkTaskForAllDTO> getAgencyForAllTypeWithVw(Integer pageNum, Integer pageSize, Integer code, String userId);

    List<WorkTaskForAllDTO> searchListWithVwByTitle(String title, String userId);

    PageInfo<WorkTaskForStudyDTO> getAgencyForStudy(Integer pageNum, Integer pageSize, Integer code, String userId, String title);

    int deleteByObjidLogic(String objid, String userId, Date sysDate);

    int endPtWorkTaskByMonth(String month);

    int deleteHtTaskByHtId(String htId, String userId, Date sysDate);
}
