package com.cmos.pbms.iservice.bp;

import com.cmos.pbms.beans.bp.BpPartyCommando;
import com.cmos.pbms.beans.dto.BpPartyCommandoDetailDTO;
import com.cmos.pbms.beans.dto.BpPartyCommandoListDTO;
import com.github.pagehelper.PageInfo;

import java.util.Date;

public interface IBpPartyCommandoSV {

    PageInfo<BpPartyCommandoListDTO> getBpPartyCommandoListForWeb(Integer page, Integer limit, String bpcTitle, String orgId, String bpcDuty, String bpcVision, String queryStart, String queryEnd, String orgCode);

    PageInfo<BpPartyCommandoListDTO> getBpPartyCommandoListForApp(Integer page, Integer limit, String orgId, Date queryDate);

    int insertSelective(BpPartyCommando bpPartyCommando);

    BpPartyCommando getByPrimaryKey(String id);

    int updateByPrimaryKeySelective(BpPartyCommando bpPartyCommando);

    int updateByPrimaryKey(BpPartyCommando bpPartyCommando);

    int updateIsTop(String id, String userId, Date currTime);

    BpPartyCommandoDetailDTO getBpPartyCommandoDetail(String id);
}
