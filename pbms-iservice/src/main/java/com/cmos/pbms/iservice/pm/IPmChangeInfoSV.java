package com.cmos.pbms.iservice.pm;

import com.cmos.common.exception.GeneralException;
import com.cmos.common.exception.SystemFailureException;
import com.cmos.pbms.beans.dto.*;
import com.cmos.pbms.beans.sys.Users;
import com.github.pagehelper.PageInfo;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface IPmChangeInfoSV {
    List<PmChangeAttDtlListDTO> getPmChangeAttDtlList(String orgChangeId, String attType);

    PmChangeInfoSimpleDTO getNewChangeId(Integer changeType, String orgId, String parentOrgId, String currentUserId, Date currTime) throws GeneralException;

    Integer saveOrCommitChangeOrdInfo(String orgChangeId, String subDesc, String subAttDesc, String revokeDesc, Date revokeDate, Integer isCommit, PmOrganizationChangeDtlDTO pmOrganizationChangeDtlDTO, Users currentUser, Date currTime) throws GeneralException;

    Integer changeOrganizationJobInformation(String orgChangeId, String position, Integer changeType, String[] userIds, String id, Date date) throws SystemFailureException;

    List<SelectBean> changeOrganizationPersonnelInformation(String orgChangeId, Integer changeType, String[] userIds, String transferToOrgId, String currentUserId, Date date) throws SystemFailureException;

    Integer changeOrganizationRolePersonnelInformation(String orgChangeId, String roleId, String[] userIds, String id, Date date) throws SystemFailureException;

    PageInfo<PmChangeInfoListDTO> getChangeOrdInfoList(Integer page, Integer limit, String orgId);

    PmChangeInfoDetailDTO getChangeOrdInfoDetail(String orgChangeId) throws SystemFailureException;

    PageInfo<PmChangeInfoAuditListDTO> getChangeOrdInfoAuditList(Integer page, Integer limit, String orgName, Integer changeType, String orgId, Integer changeState, Date subStartDate, Date subEndDate, Date auditStartDate, Date auditEndDate, String hrId);

    PageInfo<OrgMember> getOrganizationPersonnelInformation(Integer page, Integer limit, String orgChangeId, String userName, String telephones, Integer acStatus, Integer changeType, Integer isChange);

    List<SelectBean> getOrganizationPersonnelInformationCountInfo(String orgChangeId);

    Map<String, List<OrgMember>> getOrgUserInfoByActiveScope(String codeStr, String province, Integer orgStatus, String orgCode, Date queryStart, Date queryEnd, Date foundStart, Date foundEnd);

    List<OrgRoleUserDTO> getOrganizationRolePersonnelInformation(String orgChangeId);

    List<PostUserDTO> getOrganizationJobInformation(String orgChangeId);

    List<PostUserDTO> getOrganizationJobInformationGroupByPost(String orgChangeId);

    Map<String, List<PostUserDTO>> getOrgPostInfoByActiveScope(String codeStr, String province, Integer orgStatus, String orgCode, Date queryStart, Date queryEnd, Date foundStart, Date foundEnd);

    Integer auditChangeOrdInfo(String orgChangeId, Integer changeState, String auditDesc, Users currentUser, Date date) throws GeneralException;

    List<UserListBean> getOrganizationRolePersonnelInformationChosen(String orgChangeId, String roleId);

    List<UserListBean> getOrganizationJobInformationChosen(String orgChangeId, String postId);

    Integer changeOrganizationType(String orgChangeId, Integer orgType, String userId, Date date) throws SystemFailureException;

    List<SelectBean> revertOrganizationPersonnelInformation(String sysUserChangeId, String userId, Date date) throws SystemFailureException;

    Integer cancelChangeOrdInfo(String orgChangeId, String userId, Date date) throws GeneralException;
}
