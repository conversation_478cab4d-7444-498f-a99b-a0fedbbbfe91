package com.cmos.pbms.iservice.ps;

import com.cmos.pbms.beans.ps.PsPoineerScoreInfo;

import java.util.Date;

public interface IPsPoineerScoreInfoSV {

    int insert(PsPoineerScoreInfo psPoineerScoreInfo);

    int insertSelective(PsPoineerScoreInfo psPoineerScoreInfo);

    PsPoineerScoreInfo getByPrimaryKey(String id);

    int updateByPrimaryKeySelective(PsPoineerScoreInfo psPoineerScoreInfo);

    int updateByPrimaryKey(PsPoineerScoreInfo psPoineerScoreInfo);

    PsPoineerScoreInfo getByMonth(String dataMonth);

    /**
     * 计算当天先锋指数
     *
     * @param sn
     * @param date
     */
    void calculationPoineerScore(String sn, Date date);

    /**
     * 结算本月先锋指数
     *
     * @param sn
     * @param date
     */
    void settlementPoineerScore(String sn, Date date);

    /**
     * 计算当天先锋指数（个人）
     *
     * @param userId
     * @param date
     */
    int calUserPoineerScore(String userId, Date date);
}
