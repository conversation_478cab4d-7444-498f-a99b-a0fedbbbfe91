package com.cmos.pbms.iservice.dd;

import com.cmos.pbms.beans.dd.Question;

import java.util.List;
import java.util.Map;

public interface IQuestionSV {

    /**
     * 根据民主评议id获取问题
     *
     * @param subjectid 民主评议对象id
     * @return 问题对象（Question）
     */
    List<Question> getQuestion(String subjectid);

    /**
     * 根据民主评议id获取答卷
     *
     * @param subjectid 民主评议对象id
     * @param userid    用户id
     * @return 问题对象（Question）
     */
    Map<String, Object> getAnswers(String subjectid, String userid);

    /**
     * 插入答案
     *
     * @param record 答案记录
     * @return 受影响的数据记录数（int）
     */
    Map<String, Object> insertAnswerBatch(String answersjson, String subjectid, String userid, Integer isanonymous);

}
