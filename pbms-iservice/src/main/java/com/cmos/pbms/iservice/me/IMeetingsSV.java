package com.cmos.pbms.iservice.me;

import com.alibaba.fastjson.JSONArray;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.exception.SystemFailureException;
import com.cmos.common.validator.me.VMeetingsSummaryListSearchBean;
import com.cmos.common.web.upload.exception.StorageException;
import com.cmos.pbms.beans.an.AnActivityTaskRelationship;
import com.cmos.pbms.beans.common.Attachments;
import com.cmos.pbms.beans.dto.*;
import com.cmos.pbms.beans.me.Meetings;
import com.cmos.pbms.beans.pageHome.OrgLifeStatistics;
import com.cmos.pbms.beans.sys.Users;
import com.github.pagehelper.PageInfo;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public interface IMeetingsSV {

    int insertSelective(Meetings meetings);

    /**
     * 根据主键，查询会议详情
     *
     * @param id 会议ID
     * @return 会议详情
     */
    Meetings getByPrimaryKey(String id);

    Meetings getByPrimaryKeyAndDeleteStatus(String id, Integer isDelete);

    /**
     * 根据主键，查询会议详情(app端)
     *
     * @param id     会议ID
     * @param userId 当前用户ID
     * @return 会议详情
     */
    MeetingsDetailDTO getDetailById(String id, String userId);

    /**
     * 根据主键，更新会议信息
     *
     * @param meetings 会议详情
     * @return 影响行数
     */
    int updateMeetingsByPrimaryKey(Meetings meetings);

    /**
     * 更新会议基本信息（不涉及状态变更）
     *
     * @param meetingId 会议ID
     * @param basicInfo 基本信息
     * @param userId 操作用户ID
     * @param modifiedDate 修改时间
     * @return 影响行数
     */
    int updateMeetingBasicInfo(String meetingId, VMeetingsBasicUpdateBean basicInfo, String userId, Date modifiedDate);

    int updateByPrimaryKeySelective(Meetings meetings);

    /**
     * 会议取消
     *
     * @param id           会议ID
     * @param reason       取消原因
     * @param userId       操作人ID
     * @param modifieddate 操作时间
     * @return 影响行数
     */
    int cancelMeetingsByPrimaryKey(String id, String reason, String userId, Date modifieddate);

    String turnMeetingToConvening(String sn);

    /**
     * 是否能解除异常
     *
     * @param meeting 会议信息
     * @return 是否能解除
     */
    boolean isCanRemoveError(Meetings meeting);

    MeetingSumInfoDTO getMeetingSumInfo(String statusSet, String typeSet);

    MeetingsDetailDTO getFullDetailById(String id, Users user);

    /**
     * 会议统计（查询倒序，导出正序）
     *
     * @param vMeetingsSummaryListSearchBean
     * @param sortRule
     * @return
     */
    PageInfo<MeetingsDTO> selectListByParams(VMeetingsSummaryListSearchBean vMeetingsSummaryListSearchBean, String sortRule);

    List<MeStistDTO> selectListByParams(VMeetingsSummaryListSearchBean vMeetingsSummaryListSearchBean);

    MeMinutesDetailDTO getMinutesDetail(String meetingId);

    PageInfo<Meetings> getMinutesLis(int pageNum, int pagesize, Map<String, Object> paramMap);

    /**
     * @方法名：deleteCustomMeeting
     * @方法作用：逻辑删除计划外会议
     * @方法参数：要删除会议的id
     * @返回结果：
     * @作者：牛文钻
     * @日期：2018/9/26
     */
    void deleteCustomMeeting(String meetingId);

    /**
     * 恢复删除的会议
     *
     * @param meetingId  会议ID
     * @param currUserId 当前用户
     * @param currTime   当前时间
     */
    int recoveryMeetingForDelete(String meetingId, String currUserId, Date currTime) throws SystemFailureException;

    /**
     * 保存直播视频的回放
     *
     * @param meeting 会议对象（需要id, modifiedby, modifieddate）
     * @return
     */
    int addReplayTimes(Meetings meeting);

    List<Meetings> selectMeetingsByMainProcessId(String mainProcessId);

    /**
     * @方法名：selectMeetingIdsByMainProcessId
     * @方法作用：根据主流程ID获取所有分发会议ID
     * @方法参数：主流程会议ID
     * @返回结果：
     * @作者：牛文钻
     * @日期：2018/9/26
     */
    List<String> selectMeetingIdsByMainProcessId(String mainProcessId);

    /**
     * @方法名：selectNotCreatedSigns
     * @方法作用：查询所有未创建签到表且是线下待归档状态的会议
     * @方法参数：
     * @返回结果：
     * @作者：牛文钻
     * @日期：2019/2/20
     */
    List<Meetings> selectNotCreatedSigns();

    /**
     * @meetingIdInOfPlan：计划内会议
     * @meetingIdOutOfPlan：计划外会议id
     */
    void exchangeMeeting(String meetingIdInOfPlanId, Meetings meetingIdOutOfPlan);

    /**
     * 按组织删除相关会议信息
     *
     * @param orgId      组织ID
     * @param currUserId 当前用户
     * @param currTime   当前时间
     * @return
     */
    int deleteMeetingByOrgId(String orgId, String currUserId, Date currTime);

    /**
     * 查询会议应与会的人员列表
     *
     * @param meeting
     * @return
     */
    List<Users> getConventioneerListForMe(Meetings meeting) throws GeneralException;

    /**
     * 手动结束会议
     *
     * @param id           会议ID
     * @param userId       操作人ID
     * @param modifieddate 操作时间
     * @return 影响行数
     */
    int endMeetingsByPrimaryKey(String id, String userId, Date modifieddate) throws GeneralException;

    List<MeStistDTO> getMeetingStatistics(String meetingIds, String sortRule);

    List<MeetingsRelatedDTO> queryAllRelatedMeetings(Integer type, String topic, Date startDate, Date endDate, String codeStr);

    List<MeetingsRelatedDTO> queryNotRelatedMeetings(Integer type, String topic, Date startDate, Date endDate, String codeStr);

    List<MeetingsRelatedDTO> queryNotRelatedMeetingsForVd(Integer type, String topic, Date startDate, Date endDate, String currUserId);

    PageInfo<AuditMeetingListDTO> getAuditMeetingList(String currentUserId, String currentUserRoleId, Integer type, String topic, Integer auditState, String orgId, Date auditTodoTimeStart, Date auditTodoTimeEnd, Date auditExampleTimeStart, Date auditExampleTimeEnd, Integer page, Integer limit);

    AuditMeetingDetailDTO getAuditMeetingDetail(String id, String userId);

    Integer generAuditTask(Meetings meetings, Users currUser, Date date) throws GeneralException;

    int sendBackAuditMsg(Users currUser, String objId, String objTopic, Integer objType, Integer processType, String subUserId);

    Integer generSignatureToDo(Meetings meetings, Integer signState, JSONArray meMeetingSignDetailListJson, String orgId, String username, String currentUserId, Date date) throws SystemFailureException, StorageException;

    Integer signRefuse(String meetingId, String refuseReason, String orgId, String username, String userId, Date date) throws SystemFailureException;

    Meetings sign(String meetingId, String signImageUrl, String userId, String userName, Date date) throws SystemFailureException;

    Map<String, Object> createMinutesByMeetingId(String meetingId) throws GeneralException;

    void updateWorkTask(Meetings meeting, List<UserRoleForMeDTO> userList, Date sysDate, Integer type, Integer workTaskType, String currUserId);

    LinkedHashMap<String, Map<String, Map<Integer, MeetingsCountDTO>>> countMeByBranchAndType(VMeetingsSummaryListSearchBean vMeetingsSummaryListSearchBean);

    void relatedTasksSaveAndUpdate(List<String> taskIds, String meetingId);

    List<AnActivityTaskRelationship> getRelatedTasks(String id);

    /**
     *
     * 更新活动状态到 材料审核
     * @param meetings
     * @param currentUser
     * @param date
     * @return
     */
    int commitToExamine(Meetings meetings, Users currentUser, Date date);

    int passToExamine(String meetingId, Users user) throws GeneralException;

    int complete(String meetingId, Users user) throws GeneralException;

    /**
     * 审批流程结果
     * @param meetingId 流程ID
     * @param businessType 流程类型
     *
     * @return
     * @throws GeneralException
     */
    int approvaProcessResult(String meetingId, Integer businessType, List<Attachments> attachmentsList) throws GeneralException;

    /**
     * 获取组织生活统计
     * @param codestr
     * @return
     */
    List<OrgLifeStatistics> getOrgLifeStatistics(String codestr);

    /**
     * 计算 党支部的活动期数
     *
     * @param orgId       党支部id
     * @param meetingType 活动类型
     * @param year        年度
     * @param studyType
     * @return 期数
     */
    Integer getActivityPeriod(String orgId, String meetingType, String year, String studyType);
}

