package com.cmos.pbms.iservice.me;

import com.cmos.common.exception.SystemFailureException;
import com.cmos.pbms.beans.dto.MeVoiceTranslationInfoListDTO;
import com.cmos.pbms.beans.dto.MeVoiceTranslationInfoSyncDTO;
import com.cmos.pbms.beans.me.MeVoiceTranslationInfo;

import java.util.Date;
import java.util.List;

public interface IMeVoiceTranslationInfoSV {

    Integer insertSelective(MeVoiceTranslationInfo meVoiceTranslationInfo);

    MeVoiceTranslationInfo selectByPrimaryKey(String id);

    Integer updateByPrimaryKeySelective(MeVoiceTranslationInfo meVoiceTranslationInfo);

    Integer updateToDeleteById(String id, String modifiedBy, Date modifiedDate);

    Integer updateToDeleteByOrderId(String orderId, String modifiedBy, Date modifiedDate);

    List<MeVoiceTranslationInfo> getByUserId(String userId);

    Integer updateOrderIdById(String conferenceId, String orderId, String conferenceUniqueId, String phone, Date current, String userId) throws SystemFailureException;

    MeVoiceTranslationInfo selectByOrderId(String orderId);

    List<MeVoiceTranslationInfoSyncDTO> selectBySourceStatus(Integer size);

    int updateBySourceStatus(String id, String syncMsg, Date date, Integer sourceStatus, String mp3Path, String audioId, String textId, String textSrcId);

    List<MeVoiceTranslationInfoListDTO> selectByObjId(String objId);

    List<MeVoiceTranslationInfoListDTO> selectByVdId(String vdId);

    int updateToDeleteByObjId(String objId, String modifiedBy, Date modifiedDate);
}
