package com.cmos.pbms.iservice.am;

import com.cmos.common.exception.GeneralException;
import com.cmos.common.exception.SystemFailureException;
import com.cmos.pbms.beans.am.AmAssessmentInfo;
import com.cmos.pbms.beans.dto.AmAssessmentInfoDetailDTO;
import com.cmos.pbms.beans.dto.AmAssessmentInfoListDTO;
import com.cmos.pbms.beans.dto.AmAssessmentInfoPreOrFinListDTO;
import com.github.pagehelper.PageInfo;

import java.util.Date;
import java.util.List;

public interface IAmAssessmentInfoSV {
    AmAssessmentInfo getAmAssessmentInfoById(String id);

    List<AmAssessmentInfoListDTO> getAllAmAssessmentInfoList(String topic, Integer amState);

    Integer saveAmAssessmentInfo(String amId, String topic, Date startDate, Date endDate, Integer amState, String[] orgIds, String userId, Date date, String taskType, String taskTag, String taskYearId, String taskQuarter);

    Integer deleteAmAssessmentInfoById(String amId, String userId, Date date) throws GeneralException;

    List<AmAssessmentInfoListDTO> getAmAssessmentInfoEnableList();

    AmAssessmentInfoDetailDTO getAmAssessmentInfoDetailByAmId(String amId) throws SystemFailureException;

    AmAssessmentInfoDetailDTO getAmAssessmentInfoQuestionDetailByAmId(String amId) throws SystemFailureException;

    Integer releaseAmAssessmentInfoById(String amId, String userId, Date date) throws GeneralException;

    Integer enableAmAssessmentInfoById(String amId, String userId, Date date) throws GeneralException;

    PageInfo<AmAssessmentInfoPreOrFinListDTO> getPreOrFinAmAssessmentInfoList(String amTopic, String userId, Integer amqlType, Integer page, Integer limit);

    void deleteAmAssessmentInfoByTaskIdAndNoExistId(List<String> amIds, String id);

    List<AmAssessmentInfoListDTO> selectAmAssessmentInfoByTaskId(String id);

    boolean deleteByTaskId(String taskId);
}
