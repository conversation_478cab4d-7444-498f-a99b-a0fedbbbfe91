package com.cmos.pbms.iservice.rn;

import com.cmos.pbms.beans.rn.Likedbook;

/**
 * 用户收藏书籍接口
 *
 * <AUTHOR>
 * created on 2018-04-10 上午10:42
 */
public interface ILikedbookSV {
    int deleteByPrimaryKey(String id);

    int deleteByBooidAndUserid(String bookid, String userid);

    int insert(Likedbook record);

    int insertSelective(Likedbook record);

    Likedbook getByPrimaryKey(String id);

    int updateByPrimaryKeySelective(Likedbook record);

    int updateByPrimaryKey(Likedbook record);
}
