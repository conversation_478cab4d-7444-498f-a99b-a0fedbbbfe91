package com.cmos.pbms.iservice.bh;

import com.cmos.common.exception.GeneralException;
import com.cmos.pbms.beans.dto.*;
import com.cmos.pbms.beans.sys.Users;
import com.github.pagehelper.PageInfo;

import java.util.Date;

public interface IBhOrgHonorSV {
    Integer add(BhOrgHonorSaveBean bhOrgHonorSaveBean, Users currUser, Date date) throws GeneralException;

    PageInfo<AuditBhOrgHonorListDTO> getAuditBhOrgHonorList(String currentUserId, String currentUserRoleId, String honorName, String issuerType, Integer bhState, String orgId, Integer page, Integer limit);

    AuditBhOrgHonorDetailDTO getAuditBhOrgHonorDetail(String id);

    Integer auditBhOrgHonor(String bhId, Integer auditState, String auditReason, String userId, String userName, Date date);

    PageInfo<BhOrgHonorListDTO> getBhOrgHonorList(String honorName, String issuerType, Integer bhState, String orgId, Integer page, Integer limit);

    BhOrgHonorDetailDTO getBhOrgHonorDetail(String id);

    Integer delete(String id, String userId, Date date);
}
