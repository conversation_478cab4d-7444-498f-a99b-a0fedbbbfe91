package com.cmos.pbms.iservice.bn;

import com.cmos.pbms.beans.dto.*;
import com.github.pagehelper.PageInfo;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface IBnBranchNewsSV {
    Integer add(BnBranchNewsSaveBean bnBranchNewsSaveBean, String userId, String orgId, Date date);

    PageInfo<BnBranchNewsListDTO> getBnBranchNewsDtoList(String id, String orgId, Date date, Integer page, Integer limit);

    PageInfo<BnBranchNewsListDTO> getMyBnBranchNewsDtoList(String id, Integer page, Integer limit);

    List<BnOrgListDTO> getMyBnBranchNewsOrgIds(String orgId);

    Integer reviewsCountAdd(String id);

    Integer reviewsCountReduce(String id);

    Integer thumbsCountAdd(String id);

    Integer thumbsCountReduce(String id);

    Integer delete(String id, String userId, Date date);

    BnBranchNewsNewInfo getBnBranchNewsNewInfo(String userId, String orgId, Date date);

    PageInfo<BnBranchNewsListDTO> getOrgBnBranchNewsDtoList(String currentUserOrgId, String orgId, Integer page, Integer limit);

    PageInfo<BnBranchNewsDTO> getByParams(Integer page, Integer limit, String userName, Date pubDateStart, Date pubDateEnd, String orgId, String orderby, String orgCode);

    List<BnBranchNewsDTO> getExeportList(String userName, Date pubDateStart, Date pubDateEnd, String orgId, String orderby, String orgCode);

    List<Map<String, Object>> getExpSta(String userName, Date pubDateStart, Date pubDateEnd, String orgId, String orderby, String orgCode);

    BnBranchNewsDTO getById(String id);
}
