#缓存客户端异常前缀
CacheExceptionsPrefix=CMOSCache
#异常,类型,优先级,处理模式
CacheExceptions=InvalidURIException,0,1,0;JedisAskDataException,0,2,0;JedisBusyException,0,1,0;JedisClusterException,0,1,0;JedisClusterMaxRedirectionsException,0,1,0;JedisConnectionException,0,1,0;JedisDataException,0,2,0;JedisExhaustedPoolException,0,2,0;JedisMovedDataException,0,2,0;JedisNoReachableClusterNodeException,0,1,0;JedisNoScriptException,0,2,0;CacheException,0,2,0;CachePropertyException,0,2,0;ClusterDownException,0,1,0;ClusterNotFondException,0,1,0;KeyEmptyException,0,3,0;RouteException,0,1,0;ValueEmptyException,0,3,0;ValueSizeBigException,0,3,0;KeyTypeExpection,0,3,0;DetectRedisException,0,2,0

#是否判断value的size
CheckValueIsBig=true
#是否判断value是否为空
CheckValueisEmpty=true

#二级缓存(秒分组)过期时间单位秒
expire.write.in.seconds=30
#二级缓存(分钟分组)过期时间单位分钟
expire.write.in.minutes=2
#二级缓存(小时分组)过期时间单位小时
expire.write.in.hours=1
#二级缓存容量
initial.capctity=100
#二级缓存最大容量
maximum.size=1000
#设置并发级别为8，并发级别是指可以同时写缓存的线程数
concurrency.level=16

