package com.cmos.pbms.web.controller.bn;

import com.alibaba.dubbo.config.annotation.Reference;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.exception.ValidationException;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.dto.*;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.iservice.bn.IBnBranchNewsSV;
import com.cmos.pbms.utils.DateUtil;
import com.cmos.pbms.utils.ValidateUtil;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.*;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

@RestController
@RequestMapping(value = "/bnBranchNews")
@Validated
@Api(description = "支部新鲜事控制器")
public class BnBranchNewsController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(BnBranchNewsController.class);
    //设置全局校验对象
    private static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    private static final String PARAM_INVALID = "PBMS_SYS_0033";

    @Reference(group = "pbms")
    private IBnBranchNewsSV bnBranchNewsSV;

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ApiOperation(value = "新增新鲜事", notes = "新增新鲜事",
            consumes = "application/x-www-form-urlencoded",
            produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "新增成功", response = Integer.class),
            @ApiResponse(code = 999, message = "新增失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "新鲜事id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pubOrgId", value = "公开范围组织id orgId：表示具体的组织", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bnType", value = "类型 1：个人，2：组织", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "bnLabel", value = "新鲜事标签分类,来源于字典 bn_label_config", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "conType", value = "类型 1：文字，2：图片，4：视频，8：转发类型", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "content", value = "文本内容", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "videoName", value = "视频名字", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "videoId", value = "视频id", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "videoImgUrl", value = "视频封面图", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "forwardImageUrl", value = "转发图片链接", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "forwardTitle", value = "转发标题", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "forwardUrl", value = "跳转链接", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "objType", value = "业务类型 me:三会一课 news:新闻 co:协同办公...", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "objId", value = "业务主键", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bnAddress", value = "地址", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bnLongitude", value = "经度", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bnLatitude", value = "纬度", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Integer add(BnBranchNewsSaveBean bnBranchNewsSaveBean) throws GeneralException {
        //校验页面提交的参数是否合法，结果集会保存每一条检验失败的信息
        Set<ConstraintViolation<BnBranchNewsSaveBean>> constraintViolationSet = validator.validate(bnBranchNewsSaveBean);
        // 如果校验不通过，则结果集中会保存每一条校验失败的信息，判断结果集长度即可知道是否通过校验
        if (!constraintViolationSet.isEmpty())
            throw new ValidationException("PBMS_SYS_0033", constraintViolationSet.iterator().next().getMessage());

        Users user = getUser();
        return bnBranchNewsSV.add(bnBranchNewsSaveBean, user.getId(), user.getOrgid(), new Date());
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ApiOperation(value = "新增新鲜事", notes = "新增新鲜事",
            consumes = "application/x-www-form-urlencoded",
            produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "新增成功", response = Integer.class),
            @ApiResponse(code = 999, message = "新增失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "新鲜事id", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Integer delete(String id) throws GeneralException {
        ValidateUtil.isNotEmpty(id);

        Users user = getUser();
        return bnBranchNewsSV.delete(id, user.getId(), new Date());
    }

    @RequestMapping(value = "/getBnBranchNewsDtoList", method = RequestMethod.POST)
    @ApiOperation(value = "获取当前用户能看到的新鲜事列表", notes = "获取当前用户能看到的新鲜事列表",
            consumes = "application/x-www-form-urlencoded",
            produces = "application/json", httpMethod = "POST", response = PageInfo.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "获取成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "获取失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页数", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<BnBranchNewsListDTO> getBnBranchNewsDtoList(Integer page, Integer limit) throws GeneralException {
        ValidateUtil.isNotNull(page, limit);

        Users user = getUser();
        return bnBranchNewsSV.getBnBranchNewsDtoList(user.getId(), user.getOrgid(), new Date(), page, limit);
    }

    @RequestMapping(value = "/getMyBnBranchNewsDtoList", method = RequestMethod.POST)
    @ApiOperation(value = "获取我发布的新鲜事列表", notes = "获取我发布的新鲜事列表",
            consumes = "application/x-www-form-urlencoded",
            produces = "application/json", httpMethod = "POST", response = PageInfo.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "获取成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "获取失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页数", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<BnBranchNewsListDTO> getMyBnBranchNewsDtoList(Integer page, Integer limit) throws GeneralException {
        ValidateUtil.isNotNull(page, limit);

        Users user = getUser();
        return bnBranchNewsSV.getMyBnBranchNewsDtoList(user.getId(), page, limit);
    }

    @RequestMapping(value = "/getOrgBnBranchNewsDtoList", method = RequestMethod.POST)
    @ApiOperation(value = "获取组织的新鲜事列表", notes = "获取组织的新鲜事列表",
            consumes = "application/x-www-form-urlencoded",
            produces = "application/json", httpMethod = "POST", response = PageInfo.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "获取成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "获取失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页数", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<BnBranchNewsListDTO> getOrgBnBranchNewsDtoList(String orgId, Integer page, Integer limit) throws GeneralException {
        ValidateUtil.isNotEmpty(orgId);
        ValidateUtil.isNotNull(page, limit);

        Users user = getUser();
        return bnBranchNewsSV.getOrgBnBranchNewsDtoList(user.getOrgid(), orgId, page, limit);
    }

    @RequestMapping(value = "/getBnBranchNewsNewInfo", method = RequestMethod.POST)
    @ApiOperation(value = "获取新鲜事最新汇总信息", notes = "获取新鲜事最新汇总信息（新点赞、新评论、新新鲜事）",
            consumes = "application/x-www-form-urlencoded",
            produces = "application/json", httpMethod = "POST", response = PageInfo.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "获取成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "获取失败", response = GeneralException.class)
    })
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public BnBranchNewsNewInfo getBnBranchNewsNewInfo() throws GeneralException {
        Users user = getUser();
        return bnBranchNewsSV.getBnBranchNewsNewInfo(user.getId(), user.getOrgid(), new Date());
    }

    @RequestMapping(value = "/getMyBnBranchNewsOrgIds", method = RequestMethod.POST)
    @ApiOperation(value = "获取我的新鲜事组织范围", notes = "获取我的新鲜事组织范围",
            consumes = "application/x-www-form-urlencoded",
            produces = "application/json", httpMethod = "POST", response = PageInfo.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "获取成功", response = List.class),
            @ApiResponse(code = 999, message = "获取失败", response = GeneralException.class)
    })
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public List<BnOrgListDTO> getMyBnBranchNewsOrgIds() throws GeneralException {
        Users user = getUser();
        return bnBranchNewsSV.getMyBnBranchNewsOrgIds(user.getOrgid());
    }

    @RequestMapping(value = "/getBranchNewsById", method = RequestMethod.POST)
    @ApiOperation(value = "根据ID获取新鲜事详情", notes = "根据ID获取新鲜事详情",
            consumes = "application/x-www-form-urlencoded",
            produces = "application/json", httpMethod = "POST", response = PageInfo.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "获取成功", response = BnBranchNewsDTO.class),
            @ApiResponse(code = 999, message = "获取失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public BnBranchNewsDTO getBranchNewsById(String id) throws GeneralException {
        ValidateUtil.isNotNull(id);
        return bnBranchNewsSV.getById(id);
    }

    @RequestMapping(value = "/staBranchNews", method = RequestMethod.POST)
    @ApiOperation(value = "支部新鲜事统计", notes = "支部新鲜事统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "userName", value = "姓名", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "pubDateStart", value = "查询时间范围开始时间", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "pubDateEnd", value = "查询时间范围结束时间", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "组织架构id", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orderby", value = "排序字段", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<BnBranchNewsDTO> staBranchNews(Integer page, Integer limit, String userName, Date pubDateStart, Date pubDateEnd, String orgId, String orderby, String orgCode) throws GeneralException {
        if (null == page || null == limit) {
            logger.error("请求参数错误");
            throw new GeneralException(PARAM_INVALID);
        }
        return bnBranchNewsSV.getByParams(page, limit, userName, pubDateStart, pubDateEnd, orgId, orderby, orgCode);
    }

    @RequestMapping(value = "/expStaList", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "支部新鲜事统计结果导出", notes = "支部新鲜事统计结果导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "姓名", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "pubDateStart", value = "查询时间范围开始时间", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "pubDateEnd", value = "查询时间范围结束时间", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "组织架构id", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orderby", value = "排序字段", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void expStaList(String userName, Date pubDateStart, Date pubDateEnd, String orgId, String orderby, String orgCode, HttpServletResponse response) throws GeneralException {
        List<Map<String,Object>> listSheet1 = bnBranchNewsSV.getExpSta(userName, pubDateStart, pubDateEnd, orgId, orderby, orgCode);
        List<BnBranchNewsDTO> listSheet2 = bnBranchNewsSV.getExeportList(userName, pubDateStart, pubDateEnd, orgId, orderby, orgCode);

        HSSFWorkbook workbook = new HSSFWorkbook();

        HSSFFont titleFont = workbook.createFont();
        titleFont.setFontName("宋体");
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 18);

        HSSFCellStyle titleCellStyle = workbook.createCellStyle();
        titleCellStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.YELLOW.getIndex());
        titleCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        titleCellStyle.setAlignment(HorizontalAlignment.CENTER);
        titleCellStyle.setBorderBottom(BorderStyle.THIN);
        titleCellStyle.setBorderLeft(BorderStyle.THIN);
        titleCellStyle.setBorderRight(BorderStyle.THIN);
        titleCellStyle.setBorderTop(BorderStyle.THIN);
        titleCellStyle.setFont(titleFont);

        HSSFFont columFont = workbook.createFont();
        columFont.setFontName("宋体");
        columFont.setBold(true);
        columFont.setFontHeightInPoints((short) 11);

        HSSFCellStyle columsCellStyle = workbook.createCellStyle();
        columsCellStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.YELLOW.getIndex());
        columsCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        columsCellStyle.setBorderBottom(BorderStyle.THIN);
        columsCellStyle.setBorderLeft(BorderStyle.THIN);
        columsCellStyle.setBorderRight(BorderStyle.THIN);
        columsCellStyle.setBorderTop(BorderStyle.THIN);
        columsCellStyle.setFont(columFont);
        columsCellStyle.setWrapText(true);

        //创建Sheet1用于存放统计汇总信息
        HSSFSheet sheet1 = workbook.createSheet("新鲜事发布量统计");
        //设置sheet1标题
        HSSFRow sheet1Title = sheet1.createRow(0);
        HSSFCell titleCell = sheet1Title.createCell(0);
        titleCell.setCellValue("新鲜事发布量统计");
        //sheet1合并区域
        CellRangeAddress regionSheet1 = new CellRangeAddress(0, 0, 0, 2);
        sheet1.addMergedRegion(regionSheet1);

        titleCell.setCellStyle(titleCellStyle);
        //设置sheet1列头

        HSSFRow sheet1Colums = sheet1.createRow(1);
        HSSFCell sheet1ColumsCell0 = sheet1Colums.createCell(0);
        sheet1ColumsCell0.setCellValue("组织名称");
        sheet1ColumsCell0.setCellStyle(columsCellStyle);
        HSSFCell sheet1ColumsCell1 = sheet1Colums.createCell(1);
        sheet1ColumsCell1.setCellValue("姓名");
        sheet1ColumsCell1.setCellStyle(columsCellStyle);
        HSSFCell sheet1ColumsCell2 = sheet1Colums.createCell(2);
        sheet1ColumsCell2.setCellValue("发布次数");
        sheet1ColumsCell2.setCellStyle(columsCellStyle);
        //循环插入统计数据
        for (int i = 0; i < listSheet1.size(); i++) {
            Map<String, Object> map = listSheet1.get(i);
            HSSFRow row = sheet1.createRow(i+2);
            row.createCell(0).setCellValue(map.get("orgFullName").toString());
            row.createCell(1).setCellValue(map.get("userName").toString());
            row.createCell(2).setCellValue(map.get("countNum").toString());
        }

        sheet1.setColumnWidth(0, 30 * 256);
        sheet1.setColumnWidth(2, 15 * 256);

        //创建Sheet2用于存放支部新鲜事列表
        HSSFSheet sheet2 = workbook.createSheet("新鲜事发布详情统计");
        //设置sheet2标题
        HSSFRow sheet2Title = sheet2.createRow(0);
        HSSFCell titleCell2 = sheet2Title.createCell(0);
        titleCell2.setCellValue("新鲜事发布详情统计");
        //sheet2合并区域
        CellRangeAddress regionSheet2 = new CellRangeAddress(0, 0, 0, 4);
        sheet2.addMergedRegion(regionSheet2);

        titleCell2.setCellStyle(titleCellStyle);
        //设置sheet2列头
        HSSFRow sheet2Colums = sheet2.createRow(1);
        HSSFCell sheet2ColumsCell0 = sheet2Colums.createCell(0);
        sheet2ColumsCell0.setCellValue("姓名");
        sheet2ColumsCell0.setCellStyle(columsCellStyle);
        HSSFCell sheet2ColumsCell1 = sheet2Colums.createCell(1);
        sheet2ColumsCell1.setCellValue("组织名称");
        sheet2ColumsCell1.setCellStyle(columsCellStyle);
        HSSFCell sheet2ColumsCell2 = sheet2Colums.createCell(2);
        sheet2ColumsCell2.setCellValue("发表日期");
        sheet2ColumsCell2.setCellStyle(columsCellStyle);
        HSSFCell sheet2ColumsCell3 = sheet2Colums.createCell(3);
        sheet2ColumsCell3.setCellValue("点赞数");
        sheet2ColumsCell3.setCellStyle(columsCellStyle);
        HSSFCell sheet2ColumsCell4 = sheet2Colums.createCell(4);
        sheet2ColumsCell4.setCellValue("新鲜事正文");
        sheet2ColumsCell4.setCellStyle(columsCellStyle);
        //循环插入新鲜事数据
        for (int i = 0; i < listSheet2.size(); i++) {
            BnBranchNewsDTO branchNewsDTO = listSheet2.get(i);
            HSSFRow row = sheet2.createRow(i + 2);
            row.createCell(0).setCellValue(branchNewsDTO.getUserName());
            row.createCell(1).setCellValue(branchNewsDTO.getOrgFullName());
            row.createCell(2).setCellValue(DateUtil.format(branchNewsDTO.getPubDate(), "yyyy-MM-dd"));
            row.createCell(3).setCellValue(branchNewsDTO.getThuCount());
            row.createCell(4).setCellValue(branchNewsDTO.getContent());
        }

        //设置列宽
        sheet2.setColumnWidth(1, 30 * 256);
        sheet2.setColumnWidth(2, 20 * 256);
        sheet2.setColumnWidth(4, 100 * 256);

        try {
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + System.currentTimeMillis() + ".xls");
            workbook.write(out);
            workbook.close();
            out.flush();
            out.close();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("PBMS_UC_0001");
        }

    }
}
