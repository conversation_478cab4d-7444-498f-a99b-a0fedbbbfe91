package com.cmos.pbms.web.controller.common;

import com.alibaba.dubbo.config.annotation.Reference;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.iservice.common.IOnestTransferSV;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * onest数据迁移控制器
 *
 * <AUTHOR>
 * @date 2019-3-29
 */
@RestController
@RequestMapping(value = "/dataTransfer")
@Api("数据校正操作控制器")
@Validated
public class OnestTransferController extends BaseController {

    @Reference(group = "pbms")
    private IOnestTransferSV onestTransferSV;

    private static final Logger logger = LoggerFactory.getLogger(OnestTransferController.class);

    @RequestMapping(value = "/backupDate", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "URL备份", notes = "URL备份",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = String.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = String.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "tableName", value = "业务表名", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "columnName", value = "业务表字段名", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public String backupDate(String tableName, String columnName) throws GeneralException {

        logger.info("onest URL备份开始执行。。。");

        onestTransferSV.backupUrl(tableName, columnName);
        return "onest URL备份开始执行。。。";
    }

    @RequestMapping(value = "/deleteDate", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "数据删除", notes = "数据删除",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = String.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = String.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "tableName", value = "业务表名", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "columnName", value = "业务表字段名", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public String deleteDate(String tableName, String columnName) {

        logger.info("onest数据删除开始执行。。。");

        onestTransferSV.deleteUrl(tableName, columnName);
        return "onest数据删除开始执行。。。";
    }

    @RequestMapping(value = "/transferData", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "数据迁移", notes = "数据迁移",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = String.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = String.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "tableName", value = "业务表名", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "columnName", value = "业务表字段名", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public String transferData(String tableName, String columnName) throws GeneralException {

        logger.info("onest数据迁移开始执行。。。");

        onestTransferSV.transferUrl(tableName, columnName);
        return "onest数据迁移开始执行。。。";
    }

    @RequestMapping(value = "/rollbackData", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "数据回滚", notes = "数据回滚",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = String.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = String.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "tableName", value = "业务表名", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "columnName", value = "业务表字段名", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public String rollbackData(String tableName, String columnName) throws GeneralException {

        logger.info("onest数据回滚开始执行。。。");

        onestTransferSV.rollbackUrl(tableName, columnName);
        return "onest数据回滚开始执行。。。";
    }

    @RequestMapping(value = "/updatePicInContentOnest", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "新闻类正文中图片更新", notes = "新闻类正文中图片更新",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = String.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = String.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "tableName", value = "业务表名", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "columnName", value = "业务表字段名", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public String updatePicInContentOnest(String tableName, String columnName) {

        logger.info("onest新闻类正文中图片更新开始执行。。。");

        onestTransferSV.updatePicInContentOnest(tableName, columnName);
        return "onest新闻类正文中图片更新开始执行。。。";
    }

    @RequestMapping(value = "/updatePicInContentLocal", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "足迹类正文中图片更新", notes = "足迹类正文中图片更新",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = String.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = String.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "tableName", value = "业务表名", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "columnName", value = "业务表字段名", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public String updatePicInContent(String tableName, String columnName) {

        logger.info("onest足迹类正文中图片更新开始执行。。。");

        onestTransferSV.updatePicInContentLocal(tableName, columnName);
        return "onest足迹类正文中图片更新开始执行。。。";
    }
}
