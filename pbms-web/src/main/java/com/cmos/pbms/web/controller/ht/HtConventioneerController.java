package com.cmos.pbms.web.controller.ht;

import com.alibaba.dubbo.config.annotation.Reference;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.dto.HtConventioneerListDTO;
import com.cmos.pbms.iservice.ht.IHtConventioneerSV;
import com.cmos.pbms.utils.ValidateUtil;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/htConventioneer")
@Validated
@Api("谈心谈话-谈话参与者信息 控制器")
public class HtConventioneerController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(HtConventioneerController.class);

    @Reference(group = "pbms")
    private IHtConventioneerSV htConventioneerSV;

    @RequestMapping(value = "/getSpeakerList", method = RequestMethod.POST)
    @ApiOperation(value = "查询谈话主谈人列表", notes = "查询谈话主谈人列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "htId", value = "主谈人", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "userName", value = "谈话分类", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "userPhone", value = "谈话方式", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<HtConventioneerListDTO> getSpeakerList(Integer page, Integer limit, String htId, String userName, String userPhone) throws GeneralException {

        ValidateUtil.isNotNull(page, limit);
        return htConventioneerSV.getConventioneerListByHtId(page, limit, htId, userName, userPhone, 1);
    }

    @RequestMapping(value = "/getAudienceList", method = RequestMethod.POST)
    @ApiOperation(value = "查询谈话被约谈人列表", notes = "查询谈话被约谈人列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "htId", value = "主谈人", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "userName", value = "谈话分类", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "userPhone", value = "谈话方式", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<HtConventioneerListDTO> getAudienceList(Integer page, Integer limit, String htId, String userName, String userPhone) throws GeneralException {

        ValidateUtil.isNotNull(page, limit);
        return htConventioneerSV.getConventioneerListByHtId(page, limit, htId, userName, userPhone, 2);
    }
}
