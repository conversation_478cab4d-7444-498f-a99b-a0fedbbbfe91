package com.cmos.pbms.web.controller.pt;

import com.alibaba.dubbo.config.annotation.Reference;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.dto.*;
import com.cmos.pbms.beans.enums.ProcessTypeEnum;
import com.cmos.pbms.beans.pm.Organization;
import com.cmos.pbms.beans.pt.PtPlanInfo;
import com.cmos.pbms.beans.pt.PtTaskInfo;
import com.cmos.pbms.beans.sys.DictionaryItems;
import com.cmos.pbms.beans.sys.ReviewLog;
import com.cmos.pbms.beans.sys.Role;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.iservice.common.IWorkTaskSV;
import com.cmos.pbms.iservice.me.IMeetingsSV;
import com.cmos.pbms.iservice.pm.IOrganizationSV;
import com.cmos.pbms.iservice.pt.IPtPlanInfoSV;
import com.cmos.pbms.iservice.pt.IPtTaskInfoSV;
import com.cmos.pbms.iservice.sys.IDictionaryItemsSV;
import com.cmos.pbms.iservice.sys.IReviewLogSV;
import com.cmos.pbms.utils.CacheServiceUtil;
import com.cmos.pbms.utils.DateUtil;
import com.cmos.pbms.utils.UIDUtil;
import com.cmos.pbms.utils.ValidateUtil;
import com.cmos.pbms.utils.constants.PbmsConstants;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@RestController
@RequestMapping("/ptTask")
@Validated
@Api(description = "工作台任务控制器")
public class PtTaskController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(PtTaskController.class);

    @Reference(group = "pbms")
    private IPtTaskInfoSV ptTaskInfoSV;
    @Reference(group = "pbms")
    private IPtPlanInfoSV ptPlanInfoSV;
    @Reference(group = "pbms")
    private IWorkTaskSV workTaskSV;
    @Reference(group = "pbms")
    private IDictionaryItemsSV dictionaryItemsSV;
    @Reference(group = "pbms")
    private IReviewLogSV reviewLogSV;
    @Reference(group = "pbms")
    private IMeetingsSV meetingsSV;
    @Reference(group = "pbms")
    private IOrganizationSV organizationSV;

    @RequestMapping(value = "/getOrgInfo", method = RequestMethod.POST)
    @ApiOperation(value = "工作台-组织相关信息查询", notes = "工作台-组织相关信息查询")
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PtOrgInfoDTO getOrgInfo() throws GeneralException {
        return ptTaskInfoSV.getOrgInfo(getUser().getOrgid());
    }

    @RequestMapping(value = "/getMyTaskList", method = RequestMethod.POST)
    @ApiOperation(value = "我的工作计划清单", notes = "我的工作计划清单")
    @ApiImplicitParams({@ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "查询开始时间", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "查询结束时间", required = false, dataType = "date", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<PtPlanTaskListDTO> getMyTaskList(Integer page, Integer limit, Date startDate, Date endDate) {
        Users currUser = getUser();
        List<String> roleIds = new ArrayList<>();
        List<Role> allUserRoles = currUser.getAllUserRoles();
        for (Role role : allUserRoles) {
            roleIds.add(role.getId());
        }
        Map<String, Organization> orgMap = currUser.getOrganizationMap();
        List<String> orgCodeList = new ArrayList<>(10);
        for (Map.Entry<String, Organization> org : orgMap.entrySet()) {
            orgCodeList.add(org.getValue().getCodestr());
        }

        return ptTaskInfoSV.getPlanTaskList(page, limit, orgCodeList, roleIds, startDate, endDate, currUser.getId());
    }

    @RequestMapping(value = "/getAppTaskDetail", method = RequestMethod.POST)
    @ApiOperation(value = "APP-工作计划详情", notes = "APP-工作计划详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务ID", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PtTaskDetailDTO getAppTaskDetail(String taskId) {

        return ptTaskInfoSV.getPtAppTaskDetail(taskId);
    }

    @RequestMapping(value = "/getWebTaskDetail", method = RequestMethod.POST)
    @ApiOperation(value = "WEB-工作计划详情", notes = "WEB-工作计划详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务ID", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public WpPlanTaskDetailDTO getWebTaskDetail(String taskId) {

        return ptTaskInfoSV.getPtWebTaskDetail(taskId);
    }

    @RequestMapping(value = "/savePtTaskAnswer", method = RequestMethod.POST)
    @ApiOperation(value = "WEB-计划任务保存|提交", notes = "WEB-计划任务保存|提交")
    @ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "isSubmit", value = "是否提交稽核（true-提交，false-保存）", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "textDesc", value = "任务文字说明", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "recreateTime", value = "预计完成时间", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int savePtTaskAnswer(String taskId, Boolean isSubmit, String textDesc, Date recreateTime) throws GeneralException {
        ValidateUtil.isNotNull(taskId, isSubmit);
        Users currUser = getUser();
        Date currTime = new Date();
        PtTaskInfo ptTaskInfo = ptTaskInfoSV.getByPrimaryKey(taskId);
        if (null == ptTaskInfo) {
            logger.error("未查询到对应的任务信息，请刷新后重试");
            throw new GeneralException("PBMS_PT_0001");
        }

        List<Users> userList = ptTaskInfoSV.getUserByOrgId(ptTaskInfo.getOrgId());
        if (CollectionUtils.isEmpty(userList)) {
            logger.error("稽核员未配置，请联系管理员");
            throw new GeneralException("PBMS_ME_1066");
        }

        PtPlanInfo ptPlanInfo = ptPlanInfoSV.getByPrimaryKey(ptTaskInfo.getPlanId());

        // 更新任务
        ptTaskInfo.setModifiedby(currUser.getId());
        ptTaskInfo.setModifieddate(currTime);
        ptTaskInfo.setRecreateTime(recreateTime);
        ptTaskInfo.setTextDesc(textDesc);
        if (isSubmit) {
            ptTaskInfo.setSubmitTime(currTime);
            ptTaskInfo.setSubmitUser(currUser.getId());
            // 结束待办
            workTaskSV.doneByObjidAndType(taskId, null, 1, currUser.getId(), currTime, null);
            ptTaskInfo.setAuditState(1);
            DictionaryItems dictionaryItems = dictionaryItemsSV.getByCodeAndDictcode(ptPlanInfo.getPlanTinyType() + "_jh", "PT_TASK_TYPE_CONFIG");
            // 为稽核员生成待办
            ptTaskInfoSV.generAuditTask(taskId, ptTaskInfo.getOrgId(), "计划任务稽核【" + ptPlanInfo.getPlanTitle() + "】", currUser, currTime, Integer.valueOf(dictionaryItems.getItemtext()), ProcessTypeEnum.PT_AUDIT.getCode(), userList);
        }
        return ptTaskInfoSV.updateByPrimaryKey(ptTaskInfo);
    }

    @RequestMapping(value = "/setRemindTime", method = RequestMethod.POST)
    @ApiOperation(value = "设置任务生成的备忘时间", notes = "设置任务生成的备忘时间")
    @ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "remindTime", value = "务生成的备忘时间", required = true, dataType = "date", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int setRemindTime(String taskId, Date remindTime) throws GeneralException {
        ValidateUtil.isNotNull(taskId, remindTime);
        PtTaskInfo ptTaskInfo = ptTaskInfoSV.getByPrimaryKey(taskId);
        ptTaskInfo.setRemindTime(remindTime);
        ptTaskInfo.setModifiedby(getUser().getId());
        ptTaskInfo.setModifieddate(new Date());
        return ptTaskInfoSV.updateByPrimaryKeySelective(ptTaskInfo);
    }

    @RequestMapping(value = "/auditPlanTask", method = RequestMethod.POST)
    @ApiOperation(value = "计划任务-中台稽核", notes = "计划任务-中台稽核")
    @ApiResponses({
            @ApiResponse(code = 0, message = "稽核成功", response = Integer.class),
            @ApiResponse(code = 999, message = "稽核失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId", value = "任务ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "auditState", value = "1：保存 2：不通过 3：通过", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "auditReason", value = "不通过的原因", required = false, dataType = "String", paramType = "query")})
    public Integer auditPlanTask(String taskId, Integer auditState, String auditReason) throws GeneralException {
        Users user = getUser();
        String userId = user.getId();
        ValidateUtil.isNotNull(taskId);
        if (Integer.valueOf(2).equals(auditState) && StringUtils.isBlank(auditReason)) {
            logger.error("稽核退回时，原因不能为空");
            throw new GeneralException("PBMS_PT_0002");
        }

        // 查询支部名片任务答案
        PtTaskInfo ptTaskInfo = ptTaskInfoSV.getByPrimaryKey(taskId);
        if (null == ptTaskInfo) {
            logger.error("参数信息有误：未查询到计划任务（ID=‘" + taskId + "’");
            throw new GeneralException("PBMS_COM_1001");
        }
        // 查询计划
        PtPlanInfo ptPlanInfo = ptPlanInfoSV.getByPrimaryKey(ptTaskInfo.getPlanId());

        Date sysDate = new Date();
        ptTaskInfo.setModifiedby(userId);
        ptTaskInfo.setModifieddate(sysDate);
        ptTaskInfo.setAuditReason(auditReason);

        ReviewLog reviewLog = new ReviewLog();
        reviewLog.setId(UIDUtil.getUID());
        reviewLog.setProcessType(ProcessTypeEnum.PT_AUDIT.getCode());
        reviewLog.setObjectId(taskId);
        // 8001( 1：提交稽核 2：稽核不通过 3：稽核通过)
        reviewLog.setOperationType(auditState);
        reviewLog.setOperationDesc(2 == auditState ? "稽核不通过" : "稽核通过");
        reviewLog.setOperationUserId(userId);
        reviewLog.setOperationUserName("智慧中台");
        reviewLog.setOperationTime(sysDate);
        reviewLog.setResuseReason(auditReason);
        reviewLog.setRemark("智慧中台已" + reviewLog.getOperationDesc());
        reviewLogSV.insertSelective(reviewLog);

        // 稽核待办类型
        DictionaryItems dictionaryItems = dictionaryItemsSV.getByCodeAndDictcode(ptPlanInfo.getPlanTinyType() + "_jh", "PT_TASK_TYPE_CONFIG");
        Integer taskType = Integer.valueOf(dictionaryItems.getItemtext());

        if (Integer.valueOf(1) == auditState) {
            return ptTaskInfoSV.updateByPrimaryKeySelective(ptTaskInfo);
        } else {
            // 2：稽核未通过 3：稽核通过
            ptTaskInfo.setAuditState(auditState);
            // 更新稽核人和时间
            ptTaskInfo.setAuditUserId(userId);
            ptTaskInfo.setAuditTime(new Date());
            if (Integer.valueOf(3) == auditState) {
                // 更新待办
                workTaskSV.doneByObjidAndTypeAndUserId(taskId, taskType, Integer.valueOf(1), null, taskType, userId, sysDate);
                // 稽核通过
                ptTaskInfo.setTaskStatus(2);
                if ("zzaqhj".equals(ptPlanInfo.getPlanTinyType())) {
                    ptTaskInfo.setFinishStatus(4);
                    // 如果任务类型为按期换届，则同步更新预计完成时间为组织的换届时间
                    organizationSV.updateElectionChangeDate(ptTaskInfo.getOrgId(), ptTaskInfo.getRecreateTime(), userId, sysDate);
                } else {
                    ptTaskInfo.setFinishStatus(2);
                }
                return ptTaskInfoSV.updateByPrimaryKey(ptTaskInfo);
            } else if (Integer.valueOf(2) == auditState) {
                // 稽核不通过
                ptTaskInfoSV.updateByPrimaryKey(ptTaskInfo);
                if (!CacheServiceUtil.checkNotAllow(PbmsConstants.sendPtTaskInfo_notify + DateUtil.format(sysDate, "yyyyMMdd") + ptTaskInfo.getId() + "submit")) {
                    // 短信提醒
                    meetingsSV.sendBackAuditMsg(getUser(), taskId, ptPlanInfo.getPlanTitle(), taskType, ProcessTypeEnum.PT_AUDIT.getCode(), ptTaskInfo.getSubmitUser());
                }
                // 稽核待办类型
                DictionaryItems dictionaryItems1 = dictionaryItemsSV.getByCodeAndDictcode(ptPlanInfo.getPlanTinyType(), "PT_TASK_TYPE_CONFIG");
                Integer WortTaskType = Integer.valueOf(dictionaryItems1.getItemtext());
                Integer type = WortTaskType * 10 + ptTaskInfo.getFinishStatus();
                // 更新提交人待办
                workTaskSV.doneByObjidAndTypeAndUserId(taskId, WortTaskType, Integer.valueOf(0), null, type, userId, sysDate);
                // 更新稽核员待办
                return workTaskSV.doneByObjidAndTypeAndUserId(taskId, taskType, Integer.valueOf(1), null, taskType, userId, sysDate);
            } else {
                logger.error("稽核状态异常，请联系管理员");
                throw new GeneralException("PBMS_ME_1065");
            }
        }
    }

    @RequestMapping(value = "/getAuditList", method = RequestMethod.POST)
    @ApiOperation(value = "查询计划任务稽核列表", notes = "查询计划任务稽核列表")
    @ApiResponses({
            @ApiResponse(code = 0, message = "查询成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "查询失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataType", value = "数据类型", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "topic", value = "待办主题", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "auditState", value = "稽核状态(1：待稽核 2：稽核未通过 3：稽核通过 不传默认为全部)", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "所属组织id", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数量", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "submitStart", value = "提交稽核时间开始", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "submitEnd", value = "提交稽核时间结束", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "auditStart", value = "稽核审核时间开始", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "auditEnd", value = "稽核审核时间结束", required = false, dataType = "date", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<AuditMeetingListDTO> getAuditList(String dataType, String topic, Integer auditState, String orgId, Integer page, Integer limit, Date submitStart, Date submitEnd, Date auditStart, Date auditEnd) throws GeneralException {

        ValidateUtil.isNotNull(page, limit);
        Users currentUser = getUser();

        return ptTaskInfoSV.getAuditList(page, limit, currentUser.getId(), currentUser.getCurrentRoleId(), auditState, topic, dataType, orgId, submitStart, submitEnd, auditStart, auditEnd);
    }

    @RequestMapping(value = "/judgeHavaPtTask", method = RequestMethod.POST)
    @ApiOperation(value = "判断当前角色关联组织是否在计划任务试点组织范围内（0-不是，>0是）", notes = "判断当前角色关联组织是否在计划任务试点组织范围内")
    @ApiResponses({
            @ApiResponse(code = 0, message = "查询成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "查询失败", response = GeneralException.class)
    })
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int judgeHavaPtTask() {
        Users currUser = getUser();
        List<String> roleIds = new ArrayList<>();
        List<Role> allUserRoles = currUser.getAllUserRoles();
        for (Role role : allUserRoles)
            roleIds.add(role.getId());
        Map<String, Organization> orgMap = currUser.getOrganizationMap();
        List<String> orgCodeList = new ArrayList<>(10);
        for (Map.Entry<String, Organization> org : orgMap.entrySet()) {
            orgCodeList.add(org.getValue().getCodestr());
        }

        return ptTaskInfoSV.getPlanTaskList(orgCodeList, roleIds, currUser.getId());
    }

    @RequestMapping(value = "/getPlanTityTypeList", method = RequestMethod.POST)
    @ApiOperation(value = "获取计划任务类型列表", notes = "获取计划任务类型列表")
    @ApiResponses({
            @ApiResponse(code = 0, message = "查询成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "查询失败", response = GeneralException.class)
    })
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public List<Map<String, String>> getPlanTityTypeList() {
        Map<String, String> typeMap = ptPlanInfoSV.getPlanTinyTypeMap();
        List<Map<String, String>> result = new ArrayList<>(10);
        for (Map.Entry<String, String> temp : typeMap.entrySet()) {
            Map<String, String> data = new HashMap<>(2);
            data.put("planTinyType", temp.getKey());
            data.put("planTinyTypeDesc", temp.getValue());
            result.add(data);
        }
        return result;
    }

    @RequestMapping(value = "/endCustomized", method = RequestMethod.POST)
    @ApiOperation(value = "结束用户自定义计划任务", notes = "结束用户自定义计划任务")
    @ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "finishDesc", value = "结束任务原因", required = true, dataType = "date", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int endCustomized(String taskId, String finishDesc) throws GeneralException {
        ValidateUtil.isNotNull(taskId, finishDesc);
        Date currTime = new Date();
        PtTaskInfo ptTaskInfo = ptTaskInfoSV.getByPrimaryKey(taskId);
        ptTaskInfo.setFinishDesc(finishDesc);
        ptTaskInfo.setTaskStatus(2);
        ptTaskInfo.setFinishStatus(2);
        ptTaskInfo.setExcuteTime(currTime);
        ptTaskInfo.setModifiedby(getUser().getId());
        ptTaskInfo.setModifieddate(currTime);
        return ptTaskInfoSV.updateByPrimaryKeySelective(ptTaskInfo);
    }

    @RequestMapping(value = "/getPtMonthlySummaryInfo", method = RequestMethod.POST)
    @ApiOperation(value = "获取月度总结信息", notes = "获取月度总结信息")
    @ApiResponses({
            @ApiResponse(code = 0, message = "查询成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "查询失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "planMonth", value = "数据类型", required = false, dataType = "int", paramType = "query")
    })
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PtMonthlySummaryInfoDTO getPtMonthlySummaryInfo(String planMonth) {
        ValidateUtil.checkIsNotEmpty(planMonth);
        Users user = getUser();
        Map<String, Organization> orgMap = user.getOrganizationMap();
        List<String> orgCodeList = new ArrayList<>(10);
        for (Map.Entry<String, Organization> org : orgMap.entrySet()) {
            orgCodeList.add(org.getValue().getCodestr());
        }
        return ptTaskInfoSV.getPtMonthlySummaryInfo(planMonth, orgCodeList, user.getId());
    }
}
