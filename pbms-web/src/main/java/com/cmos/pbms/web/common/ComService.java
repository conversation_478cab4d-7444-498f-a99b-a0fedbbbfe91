package com.cmos.pbms.web.common;

import com.alibaba.dubbo.config.annotation.Reference;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.sys.DictionaryItems;
import com.cmos.pbms.iservice.sys.IDictionaryItemsSV;
import com.cmos.pbms.utils.MsgUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @ Author     ：liqs
 * @ Date       ：Created in 14:20 2019/2/12
 * @ Description：${description}
 * @ Modified By：
 * @Version: $version$
 */
@Component
public class ComService {

    private static final Logger logger = LoggerFactory.getLogger(ComService.class);

    @Reference(group = "pbms")
    private IDictionaryItemsSV iDictionaryItemsSV;

    public boolean checkMsgNoNotice(String rspId) {
        if (StringUtils.isBlank(rspId))
            return false;

        DictionaryItems dictionaryItems = iDictionaryItemsSV.getByCodeAndDictcode(rspId, "SMS_NoNotice_CFG");

        return MsgUtil.isMsgNoNotice(dictionaryItems);
    }

    public boolean checkMsgNoNotice(String rspId, Date date) {
        if (StringUtils.isBlank(rspId))
            return false;

        DictionaryItems dictionaryItems = iDictionaryItemsSV.getByCodeAndDictcode(rspId, "SMS_NoNotice_CFG");

        return MsgUtil.isMsgNoNotice(dictionaryItems, date);
    }

    public Date getNoticDate(String rspId) {

        if (StringUtils.isBlank(rspId))
            return new Date();
        DictionaryItems dictionaryItems = iDictionaryItemsSV.getByCodeAndDictcode(rspId, "SMS_NoNotice_CFG");

        return MsgUtil.getNextNoticDate(dictionaryItems);
    }

    public Date getNoticDate(String rspId, Date date) {

        if (StringUtils.isBlank(rspId))
            return date;
        DictionaryItems dictionaryItems = iDictionaryItemsSV.getByCodeAndDictcode(rspId, "SMS_NoNotice_CFG");

        return MsgUtil.getNextNoticDate(dictionaryItems, date);
    }
}