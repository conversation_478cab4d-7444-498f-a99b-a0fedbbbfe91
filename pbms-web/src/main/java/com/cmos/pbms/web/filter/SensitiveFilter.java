package com.cmos.pbms.web.filter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.utils.HttpContentUtil;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;

@Component
@Order(Ordered.LOWEST_PRECEDENCE - 2)
@WebFilter(urlPatterns = {"/*"}, filterName = "responseWrapperFilter")
public class SensitiveFilter implements Filter {
    private static final Logger logger = LoggerFactory.getLogger(SensitiveFilter.class);

    private static final Map<String, Set<String>> url_paramKey = new HashMap<>();
    private static final String charSet = "UTF-8";

    @Override
    public void init(FilterConfig filterConfig) {
        url_paramKey.put("/pbms/user/getByParams", asSet("username", "telephones", "email", "modifedby"));
        url_paramKey.put("/pbms/statistics/queryUserNotLogin", asSet("userName", "phoneNum"));
        url_paramKey.put("/pbms/user/getByOrgIdWithParams", asSet("username", "telephones"));
        url_paramKey.put("/pbms/user/queryById", asSet("username", "telephones", "email"));
        url_paramKey.put("/pbms/user/getCurrentUserInfo", asSet("username", "telephone", "email"));
        url_paramKey.put("/pbms/meeting/getMeetingsDetailByIdForApp", asSet("personName", "personPhone", "email", "username", "telephones", "meHost"));
        url_paramKey.put("/pbms/attendance/getAttendanceByMeeting", asSet("personName", "personPhone"));
        url_paramKey.put("/pbms/conventioneer/getConventioneerList", asSet("username", "phoneNumber"));
        url_paramKey.put("/pbms/conventioneer/getConventioneerListByParams", asSet("username", "phoneNumber"));
        url_paramKey.put("/pbms/pmChangeInfo/getOrganizationPersonnelInformation", asSet("username", "telephones"));
        url_paramKey.put("/pbms/login/login", asSet("username", "telephones"));
        /*url_paramKey.put("/pbms/cipherMachine/testEncryptSm4Cbc", asSet("username", "text"));
        url_paramKey.put("/pbms/cipherMachine/testDecryptSm4Cbc", asSet("username", "text"));
        url_paramKey.put("/pbms/cipherMachine/getUserScore", asSet("username", "yyyyMMmm"));
        url_paramKey.put("/pbms/cipherMachine/initCcsp", asSet("username"));*/
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        ResponseWrapper wrapperResponse = new ResponseWrapper((HttpServletResponse) response);//转换成代理类
        String path = httpServletRequest.getRequestURI();

        // 检查是否是需要排除的路径
        if (isExcludedPath(path)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 这里只拦截返回，直接让请求过去，如果在请求前有处理，可以在这里处理
        filterChain.doFilter(request, wrapperResponse);
        boolean isCipher = HttpContentUtil.isCipher(httpServletRequest);
        byte[] content = wrapperResponse.getContent();//获取返回值
        ServletOutputStream out = response.getOutputStream();
        //判断是否有值
        try {
            if (null != content && content.length > 0 && "application/json;charset=UTF-8".equals(response.getContentType())) {
                //转码
                String contentStr = new String(content, charSet);
                JSONValidator.Type type = JSONValidator.from(contentStr).getType();
                if (type == JSONValidator.Type.Array) {
                    JSONArray jsonArray = JSONObject.parseArray(new String(content, charSet));
                    encode2(jsonArray, isCipher);
                    content = jsonArray.toJSONString().getBytes(charSet);
                } else if (type == JSONValidator.Type.Object) {
                    JSONObject jsonObject = JSONObject.parseObject(new String(content, charSet));

                    encode2(jsonObject, isCipher);
                    content = jsonObject.toJSONString().getBytes(charSet);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        //把返回值输出到客户端
        response.setContentLength(content.length);
        out.write(content);
        out.flush();

    }

    @Override
    public void destroy() {

    }

    private void encode2(JSONArray jsonArray, boolean isCipher) throws UnsupportedEncodingException {

        if (null != jsonArray || !jsonArray.isEmpty())
            for (int i = jsonArray.size() - 1; i >= 0; i--) {
                Object value = jsonArray.get(i);

                if (value instanceof JSONObject)
                    encode2((JSONObject) value, isCipher);
                if (value instanceof JSONArray) {
                    JSONArray array = (JSONArray) value;
                    encode2(array, isCipher);
                }
            }
    }

    private void encode2(JSONObject jsonObject, boolean isCipher) throws UnsupportedEncodingException {

        if (isCipher)
            for (Map.Entry<String, Object> json : jsonObject.entrySet()) {
                String key = json.getKey();
                Object value = json.getValue();

                if (value instanceof String)
                    value = HttpContentUtil.encode(key, (String) value);
//                if (value instanceof Integer)
//                    value = HttpContentUtil.encode(key, String.valueOf(value));
                if (value instanceof JSONObject)
                    encode2((JSONObject) value, isCipher);
                if (value instanceof JSONArray) {
                    JSONArray jsonArray = (JSONArray) value;
                    encode2(jsonArray, isCipher);
                }
                json.setValue(value);
            }
    }

    private Set<String> asSet(String... keys) {
        Set<String> set = new HashSet<>();
        if (null == keys || 0 == keys.length)
            return set;
        set.addAll(Arrays.asList(keys));
        return set;
    }

    private boolean isExcludedPath(String path) {
        List<String> excludePaths = Arrays.asList(
                "/pbms/open/oa/approval/submit"
        );

        return excludePaths.stream()
                .anyMatch(path::startsWith);
}
}
