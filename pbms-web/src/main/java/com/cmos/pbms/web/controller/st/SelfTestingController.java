package com.cmos.pbms.web.controller.st;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.cmos.common.exception.ValidationException;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.common.validator.news.VNewsObj;
import com.cmos.pbms.beans.resumption.VwQuestionTmp;
import com.cmos.pbms.beans.resumption.VwQuestionTmpDTO;
import com.cmos.pbms.beans.vw.VwQuestionItem;
import com.cmos.pbms.iservice.st.ISelfTesting;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/selfTesting")
@Validated
@Api("履职自测表单维护")
public class SelfTestingController extends BaseController {

    private static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    @Reference(group = "pbms")
    private ISelfTesting selfTesting;

    /**
     * 编辑数据
     * @return
     */
    @RequestMapping(value = "/updateQuestionTmp/{id}",method = {RequestMethod.POST})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Integer updateQuestionTmp(@PathVariable String id, VwQuestionTmpDTO questionTmp) throws ValidationException {
        Set<ConstraintViolation<VwQuestionTmpDTO>> constraintViolations = validator.validate(questionTmp);

        // 如果校验不通过，则结果集中会保存每一条校验失败的信息，判断结果集长度即可知道是否通过校验
        if (!constraintViolations.isEmpty()) {
            throw new ValidationException("PBMS_SYS_0033", constraintViolations.iterator().next().getMessage());
        }

        if(!StringUtils.isEmpty(questionTmp.getVwQuestionItemArry())){
            //
            List<VwQuestionItem> list= JSON.parseArray(
                    questionTmp.getVwQuestionItemArry(),
                    VwQuestionItem.class
            );
            //
            questionTmp.setVwQuestionItems(list);
        }
        questionTmp.setId(id);
        //
        questionTmp.setCreatedby(getUser().getId());
        //
        questionTmp.setModifiedby(getUser().getId());
        //
        return selfTesting.updateQuestionTmp(questionTmp);
    }

    /**
     * 查看，包含选项
     * @param id
     * @return
     */
    @RequestMapping(value = "/getVwQuestionTmpBYId/{id}",method = {RequestMethod.POST,RequestMethod.GET})
    public VwQuestionTmp getVwQuestionTmpBYId(@PathVariable String id){
        return selfTesting.getVwQuestionTmpBYId(id);
    }
    /**
     *
     * @return
     */
    @RequestMapping(value = "/getSelfTesting",method = {RequestMethod.POST,RequestMethod.GET})
    public PageInfo<VwQuestionTmp> getSelfTesting(Integer page, Integer limit, VwQuestionTmp vwQuestionTm){
        //
        return selfTesting.selectVwQuestionTmp(page,limit,vwQuestionTm);
    }
}
