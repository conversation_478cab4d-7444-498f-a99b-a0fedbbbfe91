package com.cmos.pbms.web.controller.rep;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.exception.SystemFailureException;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.common.validator.pm.VQueryElectionChangeBean;
import com.cmos.common.validator.pm.VQueryLeaderDefectBean;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.dto.*;
import com.cmos.pbms.beans.pm.Organization;
import com.cmos.pbms.beans.rep.ActivePartyMemberStatisticsVo;
import com.cmos.pbms.beans.rep.ApplyForPartyStatisticsVo;
import com.cmos.pbms.beans.rep.PartyMemberDevelopmentTargets;
import com.cmos.pbms.beans.rep.PartyStatisticsVo;
import com.cmos.pbms.beans.sys.UserRole;
import com.cmos.pbms.iservice.bn.IBnBranchNewsSV;
import com.cmos.pbms.iservice.bs.IBigScreenSV;
import com.cmos.pbms.iservice.pm.IOrganizationSV;
import com.cmos.pbms.iservice.rep.IStatisticsSV;
import com.cmos.pbms.iservice.sys.IDictionaryItemsSV;
import com.cmos.pbms.iservice.sys.ISysUserScoreDtlSV;
import com.cmos.pbms.iservice.sys.IUserSV;
import com.cmos.pbms.utils.ConvertUtil;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.util.ResourceUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/statistics")
@Validated
@Api(description = "统计指标")
public class StatisticsController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsController.class);

    private static final String FILE_READ_EXCEPTION = "PBMS_UC_0003";

    private static final String PARAM_INVALID = "PBMS_SYS_0033";

    private static final int DEFAULT_PAGE_NUMBER = 1;

    private static final int DEFAULT_PAGE_SIZE = 10;

    private static final String STATISTICS_TYPE_TOTAL = "01"; // 导出报表类型 01-总表

    private static final String STATISTICS_TYPE_SUB = "02"; // 导出报表类型 02-分表

    private static final String STATISTICS_TYPE_PERSON = "03"; // 导出报表类型 03-个人积分/已登录明细

    private static final String STATISTICS_TYPE_NOLOGIN = "04"; // 导出报表类型 04-未登录明细

    private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

    private static final String DICT_CODE_USER_STAGE = "USER_STAGE"; // 字典码编号-用户属性

    @Reference(group = "pbms")
    private IStatisticsSV statisticsSV;

    @Reference(group = "pbms")
    private IOrganizationSV organizationSV;

    @Reference(group = "pbms")
    private IUserSV userSV;

    @Reference(group = "pbms")
    private IDictionaryItemsSV dictionaryItemsSV;

    @Reference(group = "pbms")
    private ISysUserScoreDtlSV scoreDtlmeetingPlanSV;

    @Reference(group = "pbms")
    private IBigScreenSV bigScreenSV;

    @Reference(group = "pbms")
    private IBnBranchNewsSV bnBranchNewsSV;

    @RequestMapping(value = "/getRepDataByYear", method = RequestMethod.POST)
    @ApiOperation(value = "获取报表数据", notes = "依据报表指标代码、年份获取相应的报表信息；当配置数据规则可以实现过滤")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "comid", value = "分公司id", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "indicator", value = "指标代码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "year", value = "统计年份", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgcode", value = "权限范围", dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public JSONObject getRepDataByYear(String comid, String indicator, int year, String orgcode) {
        return JSONObject.parseObject(statisticsSV.getRepData(comid, indicator, year, orgcode));
    }

    @RequestMapping(value = "/getRepDataByTwoDim", method = RequestMethod.POST)
    @ApiOperation(value = "获取报表数据", notes = "依据报表指标代码、年份获取相应的报表信息；当配置数据规则可以实现过滤")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "comid", value = "分公司id", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "indicator", value = "指标代码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "year", value = "统计年份", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgcode", value = "权限范围", dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public JSONObject getRepDataByTwoDim(String comid, String indicator, int year, String orgcode) {
        JSONObject resultJSON;
        if (null == comid || "".equals(comid)) {
            resultJSON = JSONObject.parseObject(statisticsSV.getRepDataForBar(indicator, year, orgcode));
        } else {
            resultJSON = JSONObject.parseObject(statisticsSV.getRepDataForPie(comid, indicator, year, orgcode));
        }
        return resultJSON;
    }

    @RequestMapping(value = "/exportOrgConf", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出组织架构配置统计", notes = "导出组织架构配置统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isTotal", value = "总表/分表标识-01总表，02分表", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportOrgConf(HttpServletResponse response, String isTotal, String orgName, String orgCode, String orgIds) throws GeneralException {
        InputStream is = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/statistics_report_template.xlsx");

        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);

            Set<Integer> retainSheetIndexSet = new HashSet<>(10);
            if (STATISTICS_TYPE_TOTAL.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<OrgConfRepDTO> dataList = organizationSV.getOrgConfReportTotal(orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("组织架构配置情况-总表");
                exportOrgConfTotal(dataList, sheet);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }
            if (STATISTICS_TYPE_SUB.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<OrgConfRepDTO> dataList = organizationSV.getOrgConfReportSub(orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("组织架构配置情况-分表");
                exportOrgConfSub(dataList, sheet);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }

            // 去除不需导出的sheet页
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (!retainSheetIndexSet.contains(i)) {
                    workbook.removeSheetAt(i);
                }
            }

            String fileName = "组织架构配置情况统计表";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(FILE_READ_EXCEPTION);
        } finally {
            closeStream(is, workbook);
        }
    }

    /**
     * 流关闭
     *
     * @param is
     * @param workbook
     */
    private void closeStream(InputStream is, XSSFWorkbook workbook) {
        try {
            if (null != workbook) {
                workbook.close();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            try {
                if (null != is) {
                    is.close();
                }
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

    @RequestMapping(value = "/queryOrgConfTotal", method = RequestMethod.POST)
    @ApiOperation(value = "查询组织架构配置统计-总表", notes = "查询组织架构配置统计-总表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgConfRepDTO> queryOrgConfTotal(Integer page, Integer limit, String orgName, String orgCode, String orgIds) {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        return organizationSV.getOrgConfReportTotal(pageNum, pageSize, orgName, orgCode, orgIds);
    }

    @RequestMapping(value = "/queryOrgConfSub", method = RequestMethod.POST)
    @ApiOperation(value = "查询组织架构配置统计-分表", notes = "查询组织架构配置统计-分表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgConfRepDTO> queryOrgConfSub(Integer page, Integer limit, String orgName, String orgCode, String orgIds) {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        return organizationSV.getOrgConfReportSub(pageNum, pageSize, orgName, orgCode, orgIds);
    }

    private void exportOrgConfTotal(List<OrgConfRepDTO> dataList, XSSFSheet sheet) {
        int indexRow = 2;
        // 序号
        int indexNo = 1;

        // 遍历结果集
        for (OrgConfRepDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(indexNo);
            // 第二例-对应分公司
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getProvince());
            // 第三列-党组织名称
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getOrgName());
            // 第四列-党组织类别
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getOrgType());
            // 第五列-下设党支部数
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(data.getBranchNum());
            // 第六列-已设党小组数
            XSSFCell cell6 = row.createCell(5);
            cell6.setCellValue(data.getGroupNum());
            // 第七列-书记职务是否分配
            XSSFCell cell7 = row.createCell(6);
            cell7.setCellValue(data.getIsSetSecretary());
            // 第八列-党委委员是否分配
            XSSFCell cell8 = row.createCell(7);
            cell8.setCellValue(data.getIsSetcommitteer());
            // 第九列-领导职务分配完整率
            XSSFCell cell9 = row.createCell(8);
            cell9.setCellValue(data.getPostIntegrityRate());

            indexNo++;
            indexRow++;
        }
    }

    private void exportOrgConfSub(List<OrgConfRepDTO> dataList, XSSFSheet sheet) {
        int indexRow = 2;
        // 序号
        int indexNo = 1;

        // 遍历结果集
        for (OrgConfRepDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(indexNo);
            // 第二列-党组织名称
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getOrgName());
            // 第三列-党支部名称
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getBranchName());
            // 第四列-党组织类别
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getOrgType());
            // 第五列-已设党小组数
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(data.getGroupNum());
            // 第六列-党支部人数
            XSSFCell cell6 = row.createCell(5);
            cell6.setCellValue(data.getUserNum());
            // 第七列-书记职务是否分配
            XSSFCell cell7 = row.createCell(6);
            cell7.setCellValue(data.getIsSetSecretary());
            // 第八列-党委委员是否分配
            XSSFCell cell8 = row.createCell(7);
            cell8.setCellValue(data.getIsSetcommitteer());
            // 第九列-领导职务分配完整率
            XSSFCell cell9 = row.createCell(8);
            cell9.setCellValue(data.getPostIntegrityRate());

            indexNo++;
            indexRow++;
        }
    }

    @RequestMapping(value = "/exportOrgMeeting", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出三会一课完成率统计", notes = "导出三会一课完成率统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isTotal", value = "总表/分表标识-01总表，02分表", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportOrgMeeting(HttpServletResponse response, String isTotal, String startDate, String endDate, String orgName, String orgCode, String orgIds) throws GeneralException, FileNotFoundException {
        InputStream is = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/statistics_report_template.xlsx");
//        InputStream is = new FileInputStream("G:\\work-gd-pbms\\pbms\\pbms-web\\src\\main\\resources\\templates\\statistics_report_template.xlsx");
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);

            // 参数转换
            Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
            Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));

            Set<Integer> retainSheetIndexSet = new HashSet<>(10);
            if (STATISTICS_TYPE_TOTAL.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<OrgMeetingRepDTO> dataList = organizationSV.getOrgMeetingReportTotal(start, end, orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("三会一课完成率统计总表");
                exportOrgMeeting(dataList, sheet, true);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }
            if (STATISTICS_TYPE_SUB.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<OrgMeetingRepDTO> dataList = organizationSV.getOrgMeetingReportSub(start, end, orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("三会一课完成率统计分表");
                exportOrgMeeting(dataList, sheet, false);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }

            // 去除不需导出的sheet页
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (!retainSheetIndexSet.contains(i)) {
                    workbook.removeSheetAt(i);
                }
            }

            String fileName = "三会一课完成率统计表" + "（" + (StringUtils.isBlank(startDate) ? "" : startDate) + "-" + (StringUtils.isBlank(endDate) ? "" : endDate) + "）";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(FILE_READ_EXCEPTION);
        } finally {
            closeStream(is, workbook);
        }
    }

    @RequestMapping(value = "/queryOrgMeetingTotal", method = RequestMethod.POST)
    @ApiOperation(value = "查询三会一课完成率统计-总表", notes = "查询三会一课完成率统计-总表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgMeetingRepDTO> queryOrgMeetingTotal(Integer page, Integer limit, String startDate, String endDate, String orgName, String orgCode, String orgIds) {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        // 参数转换
        Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
        Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));
        return organizationSV.getOrgMeetingReportTotal(pageNum, pageSize, start, end, orgName, orgCode, orgIds);
    }

    @RequestMapping(value = "/queryOrgMeetingSub", method = RequestMethod.POST)
    @ApiOperation(value = "查询三会一课完成率统计-分表", notes = "查询三会一课完成率统计-分表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgMeetingRepDTO> queryOrgMeetingSub(Integer page, Integer limit, String startDate, String endDate, String orgName, String orgCode, String orgIds) {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        // 参数转换
        Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
        Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));
        return organizationSV.getOrgMeetingReportSub(pageNum, pageSize, start, end, orgName, orgCode, orgIds);
    }

    private void exportOrgMeeting(List<OrgMeetingRepDTO> dataList, XSSFSheet sheet, boolean isTotal) {
        int indexRow = 2;
        // 序号
        int indexNo = 1;

        // 遍历结果集
        for (OrgMeetingRepDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(indexNo);
            if (isTotal) {
                // 第二例-对应分公司
                XSSFCell cell2 = row.createCell(1);
                cell2.setCellValue(data.getProvince());
                // 第三列-党组织名称
                XSSFCell cell3 = row.createCell(2);
                cell3.setCellValue(data.getOrgName());
            } else {
                // 第二列-党组织名称
                XSSFCell cell2 = row.createCell(1);
                cell2.setCellValue(data.getOrgName());
                // 第三列-党支部名称
                XSSFCell cell3 = row.createCell(2);
                cell3.setCellValue(data.getBranchName());
            }
            // 第四列-党组织类别
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getOrgType());
            // 数据统计列
            setMeetingInfo(data, row);

            indexNo++;
            indexRow++;
        }
    }

    private void setMeetingInfo(OrgMeetingRepDTO data, XSSFRow row) {
        // -待计划环节（会议总次数）
        row.createCell(4).setCellValue(data.getToBePlanTotal());
        // -待通知环节（会议总次数）
        row.createCell(5).setCellValue(data.getToBeNotifyTotal());
        // -待召开环节（会议总次数）
        row.createCell(6).setCellValue(data.getToBeConveneTotal());
        // -召开中环节（会议总次数）
        row.createCell(7).setCellValue(data.getConveningTotal());
        // -待归档环节（会议总次数）
        row.createCell(8).setCellValue(data.getToBeHoldTotal());
        // -待归档环节（会议总次数）
        row.createCell(9).setCellValue(data.getToBeAuditTotal());
        // -已完成环节（会议总次数）
        row.createCell(10).setCellValue(data.getFinishedTotal());
        // -待计划环节（支部党员大会次数）
        row.createCell(11).setCellValue(data.getToBePlanBranch());
        // -待通知环节（支部党员大会次数）
        row.createCell(12).setCellValue(data.getToBeNotifyBranch());
        // -待召开环节（支部党员大会次数）
        row.createCell(13).setCellValue(data.getToBeConveneBranch());
        // -召开中环节（支部党员大会次数）
        row.createCell(14).setCellValue(data.getConveningBranch());
        // -待归档环节（支部党员大会次数）
        row.createCell(15).setCellValue(data.getToBeHoldBranch());
        // -待归档环节（支部党员大会次数）
        row.createCell(16).setCellValue(data.getToBeAuditBranch());
        // -已完成环节（支部党员大会次数）
        row.createCell(17).setCellValue(data.getFinishedBranch());
        // -待计划环节（支部委员会次数）
        row.createCell(18).setCellValue(data.getToBePlanLeader());
        // -待通知环节（支部委员会次数）
        row.createCell(19).setCellValue(data.getToBeNotifyLeader());
        // -待召开环节（支部委员会次数）
        row.createCell(20).setCellValue(data.getToBeConveneLeader());
        // -召开中环节（支部委员会次数）
        row.createCell(21).setCellValue(data.getConveningLeader());
        // -待归档环节（支部委员会次数）
        row.createCell(22).setCellValue(data.getToBeHoldLeader());
        // -待归档环节（支部委员会次数）
        row.createCell(23).setCellValue(data.getToBeAuditLeader());
        // -已完成环节（支部委员会次数）
        row.createCell(24).setCellValue(data.getFinishedLeader());
        // -待计划环节（党小组会次数）
        row.createCell(25).setCellValue(data.getToBePlanGroup());
        // -待通知环节（党小组会次数）
        row.createCell(26).setCellValue(data.getToBeNotifyGroup());
        // -待召开环节（党小组会次数）
        row.createCell(27).setCellValue(data.getToBeConveneGroup());
        // -召开中环节（党小组会次数）
        row.createCell(28).setCellValue(data.getConveningGroup());
        // -待归档环节（党小组会次数）
        row.createCell(29).setCellValue(data.getToBeHoldGroup());
        // -待归档环节（党小组会次数）
        row.createCell(30).setCellValue(data.getToBeAuditGroup());
        // -已完成环节（党小组会次数）
        row.createCell(31).setCellValue(data.getFinishedGroup());
        // -待计划环节（党课次数）
        row.createCell(32).setCellValue(data.getToBePlanLecture());
        // -待通知环节（党课次数）
        row.createCell(33).setCellValue(data.getToBeNotifyLecture());
        // -待召开环节（党课次数）
        row.createCell(34).setCellValue(data.getToBeConveneLecture());
        // -召开中环节（党课次数）
        row.createCell(35).setCellValue(data.getConveningLecture());
        // -待归档环节（党课次数）
        row.createCell(36).setCellValue(data.getToBeHoldLecture());
        // -待归档环节（党课次数）
        row.createCell(37).setCellValue(data.getToBeAuditLecture());
        // -已完成环节（党课次数）
        row.createCell(38).setCellValue(data.getFinishedLecture());
    }

    @RequestMapping(value = "/exportOrgLogin", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出党建登录统计", notes = "导出党建登录统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isTotal", value = "总表/分表标识-01总表，02分表, 03已登录明细表，04未登录明细表", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportOrgLogin(HttpServletResponse response, String isTotal, String startDate, String endDate, String orgName, String orgCode, String orgIds) throws GeneralException {
        InputStream is = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/statistics_report_template.xlsx");
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);

            // 参数转换
            Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
            Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));

            Set<Integer> retainSheetIndexSet = new HashSet<>(10);
            if (STATISTICS_TYPE_TOTAL.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<OrgLoginRepDTO> dataList = organizationSV.getOrgMemberReportTotal(start, end, orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("党建登录统计总表");
                exportOrgLogin(dataList, sheet, true);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }
            if (STATISTICS_TYPE_SUB.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<OrgLoginRepDTO> dataList = organizationSV.getOrgMemberReportSub(start, end, orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("党建登录统计分表");
                exportOrgLogin(dataList, sheet, false);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }
            if (STATISTICS_TYPE_PERSON.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<OrgLoginDetailRepDTO> dataList = organizationSV.getOrgLoginDetail(start, end, orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("党建登录统计明细表");
                exportOrgLoginDetail(dataList, sheet);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }
            if (STATISTICS_TYPE_NOLOGIN.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<OrgNoLoginUserRepDTO> dataList = organizationSV.getNoLoginUser(orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("党建未登录统计明细表");
                exportNoLoginUser(dataList, sheet);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }

            // 去除不需导出的sheet页
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (!retainSheetIndexSet.contains(i)) {
                    workbook.removeSheetAt(i);
                }
            }

            String fileName = "党建登录统计表" + "（" + (StringUtils.isBlank(startDate) ? "" : startDate) + "-" + (StringUtils.isBlank(endDate) ? "" : endDate) + "）";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(FILE_READ_EXCEPTION);
        } finally {
            closeStream(is, workbook);
        }
    }

    @RequestMapping(value = "/queryOrgLoginTotal", method = RequestMethod.POST)
    @ApiOperation(value = "查询党建登录统计-总表", notes = "查询党建登录统计-总表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgLoginRepDTO> queryOrgLoginTotal(Integer page, Integer limit, String startDate, String endDate, String orgName, String orgCode, String orgIds) throws GeneralException {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        // 参数转换
        Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
        Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));
        return organizationSV.getOrgMemberReportTotal(pageNum, pageSize, start, end, orgName, orgCode, orgIds);
    }

    @RequestMapping(value = "/queryOrgLoginSub", method = RequestMethod.POST)
    @ApiOperation(value = "查询党建登录统计-分表", notes = "查询党建登录统计-分表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgLoginRepDTO> queryOrgLoginSub(Integer page, Integer limit, String startDate, String endDate, String orgName, String orgCode, String orgIds) throws GeneralException {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        // 参数转换
        Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
        Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));
        return organizationSV.getOrgMemberReportSub(pageNum, pageSize, start, end, orgName, orgCode, orgIds);
    }

    @RequestMapping(value = "/queryOrgLoginDetail", method = RequestMethod.POST)
    @ApiOperation(value = "查询党建登录明细统计", notes = "查询党建登录明细统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgLoginDetailRepDTO> queryOrgLoginDetail(Integer page, Integer limit, String startDate, String endDate, String orgName, String orgCode, String orgIds) throws GeneralException {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        // 参数转换
        Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
        Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));
        return organizationSV.getOrgLoginDetail(pageNum, pageSize, start, end, orgName, orgCode, orgIds);

    }

    @RequestMapping(value = "/queryNoLoginDetail", method = RequestMethod.POST)
    @ApiOperation(value = "查询党建未登录明细统计", notes = "查询党建未登录明细统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgNoLoginUserRepDTO> queryNoLoginDetail(Integer page, Integer limit, String orgName, String orgCode, String orgIds) {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        return organizationSV.getNoLoginUser(pageNum, pageSize, orgName, orgCode, orgIds);
    }

    private void exportOrgLogin(List<OrgLoginRepDTO> dataList, XSSFSheet sheet, boolean isTotal) {
        int indexRow = 2;
        // 序号
        int indexNo = 1;

        // 遍历结果集
        for (OrgLoginRepDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(indexNo);
            if (isTotal) {
                // 第二例-对应分公司
                XSSFCell cell2 = row.createCell(1);
                cell2.setCellValue(data.getProvince());
                // 第三列-党组织名称
                XSSFCell cell3 = row.createCell(2);
                cell3.setCellValue(data.getOrgName());
            } else {
                // 第二列-党组织名称
                XSSFCell cell2 = row.createCell(1);
                cell2.setCellValue(data.getOrgName());
                // 第三列-党支部名称
                XSSFCell cell3 = row.createCell(2);
                cell3.setCellValue(data.getBranchName());
            }
            // 第四列-党组织类别
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getOrgType());
            // 第五列-党员数量
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(data.getPartierNum());
            // 第六列-登录系统党员数
            XSSFCell cell6 = row.createCell(5);
            cell6.setCellValue(data.getLoginPartierNum());
            // 第七列-登录比例（登录系统党员数/党员数量）
            XSSFCell cell7 = row.createCell(6);
            cell7.setCellValue(data.getLoginRate());
            // 第八列-活跃账号数（备注：活跃账号，应该是对系统频繁操作的账号，统计算法：指当日登陆系统且有具体操作的账号）
            XSSFCell cell8 = row.createCell(7);
            cell8.setCellValue(data.getActiveAccountNum());
            // 第九列-党员活跃占比（算法1：活跃账号数/党员数量；）
            XSSFCell cell9 = row.createCell(8);
            cell9.setCellValue(data.getActiveRateOne());
            // 第十列-党员活跃占比（算法2：活跃账号数/登录系统党员数；）
            XSSFCell cell10 = row.createCell(9);
            cell10.setCellValue(data.getActiveRateTwo());

            indexNo++;
            indexRow++;
        }
    }

    private void exportOrgLoginDetail(List<OrgLoginDetailRepDTO> dataList, XSSFSheet sheet) {
        int indexRow = 2;
        // 序号
        int indexNo = 1;

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:dd");

        // 遍历结果集
        for (OrgLoginDetailRepDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(indexNo);
            // 第二列-党组织名称
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getProvinceOrgName());
            // 第三列-党支部名称
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getBranchOrgName());
            // 第四列-姓名
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getUserName());
            // 第五列-登录时间
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(df.format(data.getLoginTime()));

            indexNo++;
            indexRow++;
        }
    }

    private void exportNoLoginUser(List<OrgNoLoginUserRepDTO> dataList, XSSFSheet sheet) {
        int indexRow = 2;
        // 序号
        int indexNo = 1;

        // 遍历结果集
        for (OrgNoLoginUserRepDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(indexNo);
            // 第二列-党组织名称
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getProvinceOrgName());
            // 第三列-党支部名称
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getBranchOrgName());
            // 第四列-姓名
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getUserName());

            indexNo++;
            indexRow++;
        }
    }

    @RequestMapping(value = "/exportOrgStudy", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出党建学习量统计", notes = "导出党建登录统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isTotal", value = "总表/分表标识-01总表，02分表", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportOrgStudy(HttpServletResponse response, String isTotal, String startDate, String endDate, String orgName, String orgCode, String orgIds) throws GeneralException, ParseException {
        InputStream is = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/statistics_report_template.xlsx");
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);
            // 参数转换
            Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
            Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));

            Set<Integer> retainSheetIndexSet = new HashSet<>(10);
            if (STATISTICS_TYPE_TOTAL.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<OrgStudyRepDTO> dataList = organizationSV.getOrgStudyReportTotal(start, end, orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("党建学习量统计总表");
                exportOrgStudy(dataList, sheet, true);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }
            if (STATISTICS_TYPE_SUB.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<OrgStudyRepDTO> dataList = organizationSV.getOrgStudyReportSub(start, end, orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("党建学习量统计分表");
                exportOrgStudy(dataList, sheet, false);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }

            // 去除不需导出的sheet页
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (!retainSheetIndexSet.contains(i)) {
                    workbook.removeSheetAt(i);
                }
            }

            String fileName = "党建学习量统计表" + "（" + (StringUtils.isBlank(startDate) ? "" : startDate) + "-" + (StringUtils.isBlank(endDate) ? "" : endDate) + "）";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(FILE_READ_EXCEPTION);
        } finally {
            closeStream(is, workbook);
        }
    }

    @RequestMapping(value = "/queryOrgStudyTotal", method = RequestMethod.POST)
    @ApiOperation(value = "查询党建学习量统计-总表", notes = "查询党建学习量统计-总表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgStudyRepDTO> queryOrgStudyTotal(Integer page, Integer limit, String startDate, String endDate, String orgName, String orgCode, String orgIds) throws GeneralException {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        // 参数转换
        Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
        Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));
        return organizationSV.getOrgStudyReportTotal(pageNum, pageSize, start, end, orgName, orgCode, orgIds);
    }

    @RequestMapping(value = "/queryOrgStudySub", method = RequestMethod.POST)
    @ApiOperation(value = "查询党建学习量统计-分表", notes = "查询党建学习量统计-分表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgStudyRepDTO> queryOrgStudySub(Integer page, Integer limit, String startDate, String endDate, String orgName, String orgCode, String orgIds) throws GeneralException {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        // 参数转换
        Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
        Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));
        return organizationSV.getOrgStudyReportSub(pageNum, pageSize, start, end, orgName, orgCode, orgIds);
    }

    private void exportOrgStudy(List<OrgStudyRepDTO> dataList, XSSFSheet sheet, boolean isTotal) {
        int indexRow = 2;
        // 序号
        int indexNo = 1;

        // 遍历结果集
        for (OrgStudyRepDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(indexNo);
            if (isTotal) {
                // 第二例-对应分公司
                XSSFCell cell2 = row.createCell(1);
                cell2.setCellValue(data.getProvince());
                // 第三列-党组织名称
                XSSFCell cell3 = row.createCell(2);
                cell3.setCellValue(data.getOrgName());
            } else {
                // 第二列-党组织名称
                XSSFCell cell2 = row.createCell(1);
                cell2.setCellValue(data.getOrgName());
                // 第三列-党支部名称
                XSSFCell cell3 = row.createCell(2);
                cell3.setCellValue(data.getBranchName());
            }
            // 第四列-党组织类别
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getOrgType());
            // 第五列-党建资讯阅读量（算法：在规定时间范围内，党组织查看党建资讯总人数）
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(data.getInformationCount());
            // 第六列-在线学习总量 （算法：在规定时间范围内，党组织访问在线学习模块总人数；在线模块：多媒体+网上学习+书籍学习）
            XSSFCell cell6 = row.createCell(5);
            cell6.setCellValue(data.getStudyCount());
            // 第七列-视频学习量（算法：在规定时间范围内，党组织查看党建资讯总人数）
            XSSFCell cell7 = row.createCell(6);
            cell7.setCellValue(data.getVideoCount());
            // 第八列-音频学习量（算法：在规定时间范围内，党组织查看党建资讯总人数）
            XSSFCell cell8 = row.createCell(7);
            cell8.setCellValue(data.getAudioCount());
            // 第九列-网上学习量（算法：在规定时间范围内，党组织查看党建资讯总人数）
            XSSFCell cell9 = row.createCell(8);
            cell9.setCellValue(data.getOnLineCount());
            // 第十列-书籍学习量（算法：在规定时间范围内，党组织查看党建资讯总人数）
            XSSFCell cell10 = row.createCell(9);
            cell10.setCellValue(data.getBookCount());

            indexNo++;
            indexRow++;
        }
    }

    @RequestMapping(value = "/exportOrgScore", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出积分排名表", notes = "导出积分排名表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isTotal", value = "统计范围标识-01分公司，02党支部，03个人", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportOrgScore(HttpServletResponse response, String isTotal, String startDate, String endDate, String orgName, String orgCode, String orgIds) throws GeneralException, ParseException {
        InputStream is = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/statistics_report_template.xlsx");
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);

            // 参数转换
            Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
            Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));

            Set<Integer> retainSheetIndexSet = new HashSet<>(10);
            if (STATISTICS_TYPE_TOTAL.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<OrgScoreRankRepDTO> dataList = organizationSV.getOrgScoreReportTotal(start, end, orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("积分排名表（分公司）");
                exportOrgScore(dataList, sheet, true);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }
            if (STATISTICS_TYPE_SUB.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<OrgScoreRankRepDTO> dataList = organizationSV.getOrgScoreReportSub(start, end, orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("积分排名表（党支部）");
                exportOrgScore(dataList, sheet, false);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }
            if (STATISTICS_TYPE_PERSON.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<PerScoreRankRepDTO> dataList = organizationSV.getPersonScoreReport(start, end, null, orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("积分排名表（个人）");
                exportPersonScore(dataList, sheet);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }

            // 去除不需导出的sheet页
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (!retainSheetIndexSet.contains(i)) {
                    workbook.removeSheetAt(i);
                }
            }

            String fileName = "积分排名统计表" + "（" + (StringUtils.isBlank(startDate) ? "" : startDate) + "-" + (StringUtils.isBlank(endDate) ? "" : endDate) + "）";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(FILE_READ_EXCEPTION);
        } finally {
            closeStream(is, workbook);
        }
    }

    @RequestMapping(value = "/queryOrgScoreTotal", method = RequestMethod.POST)
    @ApiOperation(value = "积分排名表（分公司）", notes = "积分排名表（分公司）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgScoreRankRepDTO> queryOrgScoreTotal(Integer page, Integer limit, String startDate, String endDate, String orgName, String orgCode, String orgIds) throws GeneralException {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        // 参数转换
        Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
        Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));
        return organizationSV.getOrgScoreReportTotal(pageNum, pageSize, start, end, orgName, orgCode, orgIds);
    }

    @RequestMapping(value = "/queryOrgScoreSub", method = RequestMethod.POST)
    @ApiOperation(value = "积分排名表（党支部）", notes = "积分排名表（党支部）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgScoreRankRepDTO> queryOrgScoreSub(Integer page, Integer limit, String startDate, String endDate, String orgName, String orgCode, String orgIds) throws GeneralException {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        // 参数转换
        Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
        Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));
        return organizationSV.getOrgScoreReportSub(pageNum, pageSize, start, end, orgName, orgCode, orgIds);
    }

    @RequestMapping(value = "/queryPersonScore", method = RequestMethod.POST)
    @ApiOperation(value = "积分排名表（个人）", notes = "积分排名表（个人）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "userName", value = "用户姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<PerScoreRankRepDTO> queryPersonScore(Integer page, Integer limit, String startDate, String endDate, String userName, String orgName, String orgCode, String orgIds) throws GeneralException {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        // 参数转换
        Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
        Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));
        return organizationSV.getPersonScoreReport(pageNum, pageSize, start, end, userName, orgName, orgCode, orgIds);
    }

    private void exportOrgScore(List<OrgScoreRankRepDTO> dataList, XSSFSheet sheet, boolean isTotal) {
        int indexRow = 2;

        // 遍历结果集
        for (OrgScoreRankRepDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(data.getIndexNo());
            if (isTotal) {
                // 第二例-对应分公司
                XSSFCell cell2 = row.createCell(1);
                cell2.setCellValue(data.getProvince());
                // 第三列-党组织名称
                XSSFCell cell3 = row.createCell(2);
                cell3.setCellValue(data.getOrgName());
            } else {
                // 第二列-党组织名称
                XSSFCell cell2 = row.createCell(1);
                cell2.setCellValue(data.getOrgName());
                // 第三列-党支部名称
                XSSFCell cell3 = row.createCell(2);
                cell3.setCellValue(data.getBranchName());
            }
            // 第四列-党组织类别
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getOrgType());
            // 第五列-积分总分
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(data.getTotalScore());
            // bug编号：9478 积分分公司和党支部报表增加平均积分列 第六列-人均积分
            XSSFCell cell6 = row.createCell(5);
            cell6.setCellValue(data.getAverageScoreStr());

            indexRow++;
        }
    }

    private void exportPersonScore(List<PerScoreRankRepDTO> dataList, XSSFSheet sheet) {
        int indexRow = 2;

        // 遍历结果集
        for (PerScoreRankRepDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(data.getIndexNo());
            // 第二列-党组织名称
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getOrgName());
            // 第三列-用户姓名
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getUserName());
            // 第四列-人均积分
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getScore());

            indexRow++;
        }
    }

    @RequestMapping(value = "/exportTopRank", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出新闻/视频TOP N排行榜", notes = "导出新闻/视频TOP N排行榜")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isTotal", value = "统计范类别标识-01新闻，02党视频 必须传其一", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "topNum", value = "TOP N", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportTopRank(HttpServletResponse response, String isTotal, String startDate, String endDate, Integer topNum, String orgName, String orgCode, String orgIds) throws GeneralException {
        InputStream is = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/statistics_report_template.xlsx");

        Integer childObjType;
        if (STATISTICS_TYPE_TOTAL.equals(isTotal)) {
            childObjType = 10101;
        } else if (STATISTICS_TYPE_SUB.equals(isTotal)) {
            childObjType = 10201;
        } else {
            logger.error("类型参数错误");
            throw new GeneralException(PARAM_INVALID);
        }

        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);

            // 结果数据集
            List<TopRankRepDTO> dataList = organizationSV.getTopRankByType(startDate, calNextDay(endDate, "yyyy-MM-dd"), childObjType, topNum, orgName, orgCode, orgIds);
            XSSFSheet sheet = workbook.getSheet("TOP排名");
            int retainSheetIndex = workbook.getSheetIndex(sheet);
            exportTop(dataList, sheet);

            // 去除不需导出的sheet页
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (retainSheetIndex != i) {
                    workbook.removeSheetAt(i);
                }
            }

            String fileName = "TOP排名统计表" + "（" + (StringUtils.isBlank(startDate) ? "" : startDate) + "-" + (StringUtils.isBlank(endDate) ? "" : endDate) + "）";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(FILE_READ_EXCEPTION);
        } finally {
            closeStream(is, workbook);
        }
    }

    @RequestMapping(value = "/queryTopRank", method = RequestMethod.POST)
    @ApiOperation(value = "查询新闻/视频TOP N排行榜", notes = "查询新闻/视频TOP N排行榜")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isTotal", value = "统计范类别标识-01新闻，02党视频 必须传其一", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "topNum", value = "TOP N", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public List<TopRankRepDTO> queryTopRank(String isTotal, String startDate, String endDate, Integer topNum, String orgName, String orgCode, String orgIds) throws GeneralException {
        Integer childObjType;
        if (STATISTICS_TYPE_TOTAL.equals(isTotal)) {
            childObjType = 10101;
        } else if (STATISTICS_TYPE_SUB.equals(isTotal)) {
            childObjType = 10201;
        } else {
            logger.error("类型参数错误");
            throw new GeneralException(PARAM_INVALID);
        }
        return organizationSV.getTopRankByType(startDate, calNextDay(endDate, "yyyy-MM-dd"), childObjType, topNum, orgName, orgCode, orgIds);
    }

    private void exportTop(List<TopRankRepDTO> dataList, XSSFSheet sheet) {
        int indexRow = 2;

        // 遍历结果集
        for (TopRankRepDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(data.getIndexNo());
            // 第二列-类型
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getObjType());
            // 第三列-主题
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getObjTitle());
            // 第四列-点击量
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getReadCount());

            indexRow++;
        }
    }

    @RequestMapping(value = "/exportErrorUser", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出异常用户统计", notes = "导出异常用户统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isTotal", value = "总表/分表标识-01总表，02分表", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportErrorUser(HttpServletResponse response, String isTotal, String orgName, String orgCode, String orgIds) throws GeneralException {
        InputStream is = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/statistics_report_template.xlsx");
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);

            Set<Integer> retainSheetIndexSet = new HashSet<>(10);
            if (STATISTICS_TYPE_TOTAL.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<UserDeptErrorRepDTO> dataList = organizationSV.getUserErrorReportTotal(orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("异常用户统计总表");
                exportUserError(dataList, sheet, true);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }
            if (STATISTICS_TYPE_SUB.equals(isTotal) || StringUtils.isBlank(isTotal)) {
                // 结果数据集
                List<UserDeptErrorRepDTO> dataList = organizationSV.getUserErrorReportSub(orgName, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("异常用户统计分表");
                exportUserError(dataList, sheet, false);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }

            // 去除不需导出的sheet页
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (!retainSheetIndexSet.contains(i)) {
                    workbook.removeSheetAt(i);
                }
            }

            String fileName = "异常用户统计表";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(FILE_READ_EXCEPTION);
        } finally {
            closeStream(is, workbook);
        }
    }

    @RequestMapping(value = "/queryErrorUserTotal", method = RequestMethod.POST)
    @ApiOperation(value = "查询异常用户统计-总表", notes = "查询异常用户统计-总表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<UserDeptErrorRepDTO> queryErrorUserTotal(Integer page, Integer limit, String orgName, String orgCode, String orgIds) {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        return organizationSV.getUserErrorReportTotal(pageNum, pageSize, orgName, orgCode, orgIds);
    }

    @RequestMapping(value = "/queryErrorUserSub", method = RequestMethod.POST)
    @ApiOperation(value = "查询异常用户统计-分表", notes = "查询异常用户统计-分表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<UserDeptErrorRepDTO> queryErrorUserSub(Integer page, Integer limit, String orgName, String orgCode, String orgIds) {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        return organizationSV.getUserErrorReportSub(pageNum, pageSize, orgName, orgCode, orgIds);
    }

    private void exportUserError(List<UserDeptErrorRepDTO> dataList, XSSFSheet sheet, boolean isTotal) {
        int indexRow = 2;
        // 序号
        int indexNo = 1;

        // 遍历结果集
        for (UserDeptErrorRepDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(indexNo);
            // 第二例-对应分公司
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getProvince());
            // 第三列-党组织名称
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getOrgName());
            // 第四列-党支部名称
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getBranchName());
            if (isTotal) {
                // 第五列-党支部党员数量
                XSSFCell cell5 = row.createCell(4);
                cell5.setCellValue(data.getBranchMemberNum());
                // 第六列-党小组党员总数量
                XSSFCell cell6 = row.createCell(5);
                cell6.setCellValue(data.getGroupMemberNum());
                // 第七列-异常党员数量
                XSSFCell cell7 = row.createCell(6);
                cell7.setCellValue(data.getErrorNum());
            } else {
                // 第五列-党员姓名
                XSSFCell cell5 = row.createCell(4);
                cell5.setCellValue(data.getUserName());
                // 第六列-当员工号
                XSSFCell cell6 = row.createCell(5);
                cell6.setCellValue(data.getHrId());
                // 第五列-党员手机号
                XSSFCell cell7 = row.createCell(6);
                cell7.setCellValue(data.getTelephone());
                // 第六列-当员属性
                XSSFCell cell8 = row.createCell(7);
                cell8.setCellValue(data.getStageDesc());
            }

            indexNo++;
            indexRow++;
        }
    }

    @RequestMapping(value = "/exportConstruction", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出运营建设内容统计", notes = "导出运营建设内容统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "objType", value = "统计数据类型（01时政新闻，02公司动态，03思想政治，04视频，05音频，06网上学习，07书籍）", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "操作权限的组织", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportConstruction(HttpServletResponse response, String objType, String startDate, String endDate, String orgCode) throws GeneralException {
        InputStream is = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/statistics_report_template.xlsx");
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);
            // 参数转换
            Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));

            // 结果数据集
            String dateFormat = "yyyy-MM-dd";
            List<ConstructionRepDTO> dataList = organizationSV.getConstructionReport(startDate, ConvertUtil.convertDateToString(end, dateFormat), objType, orgCode);
            XSSFSheet sheet = workbook.getSheet("内容建设日常统计");
            int retainSheetIndex = workbook.getSheetIndex(sheet);
            expConstruction(dataList, sheet);

            // 去除不需导出的sheet页
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (retainSheetIndex != i) {
                    workbook.removeSheetAt(i);
                }
            }

            String fileName = "内容建设日常统计表" + "（" + (StringUtils.isBlank(startDate) ? "" : startDate) + "-" + (StringUtils.isBlank(endDate) ? "" : endDate) + "）";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(FILE_READ_EXCEPTION);
        } finally {
            closeStream(is, workbook);
        }
    }

    @RequestMapping(value = "/queryConstruction", method = RequestMethod.POST)
    @ApiOperation(value = "查询运营建设内容统计", notes = "查询运营建设内容统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "objType", value = "统计数据类型（01时政新闻，02公司动态，03思想政治，04视频，05音频，06网上学习，07书籍）", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "操作权限的组织", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<ConstructionRepDTO> queryConstruction(Integer page, Integer limit, String objType, String startDate, String endDate, String orgCode) {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        // 参数转换
        Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));
        String dateFormat = "yyyy-MM-dd";
        return organizationSV.getConstructionReport(pageNum, pageSize, startDate, ConvertUtil.convertDateToString(end, dateFormat), objType, orgCode);
    }

    private void expConstruction(List<ConstructionRepDTO> dataList, XSSFSheet sheet) {
        int indexRow = 2;
        // 序号
        int indexNo = 1;

        // 遍历结果集
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        for (ConstructionRepDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(indexNo);
            // 第二例-类型
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getObjType());
            // 第三列-标题
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getTitle());
            // 第四列-创建时间
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getCreateTime());
            // 第五列-所属党组织
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(data.getOrgfName());
            // 第六列-创建者
            XSSFCell cell6 = row.createCell(5);
            cell6.setCellValue(data.getCreateUser());

            indexNo++;
            indexRow++;
        }
    }

    @RequestMapping(value = "/exportFinanceBudget", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出财务预算数据", notes = "导出财务预算数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "staType", value = "统计类型-01按组织归属，02按预算类别", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "financialYear", value = "财务年度", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportFinanceBudget(HttpServletResponse response, String staType, String financialYear, String orgCode, String orgIds) throws GeneralException {
        InputStream is = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/statistics_report_template.xlsx");

        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);

            Set<Integer> retainSheetIndexSet = new HashSet<>(10);
            if (STATISTICS_TYPE_TOTAL.equals(staType) || StringUtils.isBlank(staType)) {
                // 结果数据集
                List<PartyBudgetRepDTO> dataList = organizationSV.getPartyBudgetRepInOrg(financialYear, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("党费使用年度预算一");
                exportFinanceBudgetInOrg(dataList, sheet);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }
            if (STATISTICS_TYPE_SUB.equals(staType) || StringUtils.isBlank(staType)) {
                // 结果数据集
                List<PartyBudgetRepDTO> dataList = organizationSV.getPartyBudgetRepInType(financialYear, orgCode, orgIds);
                XSSFSheet sheet = workbook.getSheet("党费使用年度预算二");
                exportFinanceBudgetInType(dataList, sheet);
                retainSheetIndexSet.add(workbook.getSheetIndex(sheet));
            }

            // 去除不需导出的sheet页
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (!retainSheetIndexSet.contains(i)) {
                    workbook.removeSheetAt(i);
                }
            }

            String fileName = "党费使用年度预算统计表";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(FILE_READ_EXCEPTION);
        } finally {
            closeStream(is, workbook);
        }
    }

    @RequestMapping(value = "/queryFinanceBudgetInOrg", method = RequestMethod.POST)
    @ApiOperation(value = "查询党费预算统计-组织归属维度", notes = "查询党费预算统计-组织归属维度")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "financialYear", value = "财务年度", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<PartyBudgetRepDTO> queryFinanceBudgetInOrg(Integer page, Integer limit, String financialYear, String orgCode, String orgIds) {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        return organizationSV.getPartyBudgetRepInOrg(pageNum, pageSize, financialYear, orgCode, orgIds);
    }

    @RequestMapping(value = "/queryFinanceBudgetInType", method = RequestMethod.POST)
    @ApiOperation(value = "查询党费预算统计-预算类别维度", notes = "查询党费预算统计-预算类别维度")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "financialYear", value = "财务年度", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<PartyBudgetRepDTO> queryFinanceBudgetInType(Integer page, Integer limit, String financialYear, String orgCode, String orgIds) throws SystemFailureException {
        List<PartyBudgetRepDTO> budgetList = organizationSV.getPartyBudgetRepInType(financialYear, orgCode, orgIds);
        return new PageInfo<>(budgetList);
    }

    private void exportFinanceBudgetInOrg(List<PartyBudgetRepDTO> dataList, XSSFSheet sheet) {
        int indexRow = 2;
        // 序号
        int indexNo = 1;

        // 涉及人数、涉及次数、预算金额累计
        Integer involveNum = 0;
        Integer involveTimes = 0;
        BigDecimal involveAmount = new BigDecimal("0");

        // 遍历结果集
        for (PartyBudgetRepDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);

            // 第二列-分公司（合并单元格）
            CellRangeAddress callRangeAddress = new CellRangeAddress(indexRow, indexRow + data.getDetailList().size(), 1, 1);
            sheet.addMergedRegion(callRangeAddress);
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getOrgfName());

            for (BudgetDetailRepDTO detail : data.getDetailList()) {
                // 第一列-序号
                XSSFCell cell1 = row.createCell(0);
                cell1.setCellValue(indexNo);
                // 第三列-类别
                XSSFCell cell3 = row.createCell(2);
                cell3.setCellValue(data.getBudgetType());
                // 第四列-内容说明
                XSSFCell cell4 = row.createCell(3);
                cell4.setCellValue(data.getContentDesc());
                // 第五列-涉及人数
                XSSFCell cell5 = row.createCell(4);
                cell5.setCellValue(data.getInvolveNum());
                // 第六列-涉及次数
                XSSFCell cell6 = row.createCell(5);
                cell6.setCellValue(data.getInvolveTimes());
                // 第七列-预算金额（元）
                XSSFCell cell7 = row.createCell(6);
                cell7.setCellValue(data.getInvolveAmount());

                involveNum += null == detail.getInvolveNum() ? 0 : detail.getInvolveNum();
                involveTimes += null == detail.getInvolveTimes() ? 0 : detail.getInvolveTimes();
                involveAmount = involveAmount.add(new BigDecimal(detail.getInvolveAmount()));
                indexNo++;
                indexRow++;
            }
        }
        XSSFRow row = sheet.createRow(indexRow);
        CellRangeAddress callRangeAddress = new CellRangeAddress(indexRow, indexRow, 0, 3);
        sheet.addMergedRegion(callRangeAddress);
        XSSFCell cell1 = row.createCell(0);
        cell1.setCellValue("合计");
        XSSFCell cell5 = row.createCell(4);
        cell5.setCellValue(involveNum);
        XSSFCell cell6 = row.createCell(5);
        cell6.setCellValue(involveTimes);
        XSSFCell cell7 = row.createCell(6);
        cell7.setCellValue(involveAmount.toString());
    }

    private void exportFinanceBudgetInType(List<PartyBudgetRepDTO> dataList, XSSFSheet sheet) {
        int indexRow = 2;
        // 序号
        int indexNo = 1;

        // 涉及人数、涉及次数、预算金额累计
        Integer involveNum = 0;
        Integer involveTimes = 0;
        BigDecimal involveAmount = new BigDecimal("0");

        // 遍历结果集
        for (PartyBudgetRepDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);

            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(indexNo);
            // 第二列-类别
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getBudgetType());
            // 第三列-涉及人数
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getInvolveNum());
            // 第四列-涉及次数
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getInvolveTimes());
            // 第五列-预算金额（元）
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(data.getInvolveAmount());

            involveNum += null == data.getInvolveNum() ? 0 : data.getInvolveNum();
            involveTimes += null == data.getInvolveTimes() ? 0 : data.getInvolveTimes();
            involveAmount = involveAmount.add(new BigDecimal(data.getInvolveAmount()));
            indexNo++;
            indexRow++;
        }
        XSSFRow row = sheet.createRow(indexRow);
        CellRangeAddress callRangeAddress = new CellRangeAddress(indexRow, indexRow, 0, 1);
        sheet.addMergedRegion(callRangeAddress);
        XSSFCell cell1 = row.createCell(0);
        cell1.setCellValue("合计");
        XSSFCell cell5 = row.createCell(2);
        cell5.setCellValue(involveNum);
        XSSFCell cell6 = row.createCell(3);
        cell6.setCellValue(involveTimes);
        XSSFCell cell7 = row.createCell(4);
        cell7.setCellValue(involveAmount.toString());
    }

    private Date calNextDay(Date date) {
        if (null != date) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.add(Calendar.DATE, 1);
            return cal.getTime();
        }
        return null;
    }

    private String calNextDay(String timeStr, String format) {
        // String->Date
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            cal.setTime(sdf.parse(timeStr));
        } catch (ParseException e) {
            logger.error("日期格式错误");
            throw new ClassCastException("日期格式错误");
        }
        // 向后移位一天
        cal.add(Calendar.DATE, 1);
        return sdf.format(cal.getTime());
    }

    @RequestMapping(value = "/queryUserNotLogin", method = RequestMethod.POST)
    @ApiOperation(value = "党建未登录统计表", notes = "党建未登录统计表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<StaNotLoginDTO> queryUserNotLogin(Integer page, Integer limit, String startDate, String endDate, String orgCode, String orgIds) throws GeneralException {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
            return organizationSV.getUserNotLoginWithoutTime(pageNum, pageSize, orgCode, orgIds);
        } else {
            // 参数转换
            Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
            Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));
            return organizationSV.getUserNotLoginReport(pageNum, pageSize, start, end, orgCode, orgIds);
        }
    }

    @RequestMapping(value = "/exportUserNotLogin", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出党建未登录统计表", notes = "导出党建未登录统计表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportUserNotLogin(HttpServletResponse response, String startDate, String endDate, String orgCode, String orgIds) throws GeneralException, ParseException {
        InputStream is = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/statistics_report_template.xlsx");
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);

            Set<Integer> retainSheetIndexSet = new HashSet<>(10);
            // 结果数据集
            List<StaNotLoginDTO> dataList;
            if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
                dataList = organizationSV.getUserNotLoginWithoutTime(orgCode, orgIds);
            } else {
                // 参数转换
                Date start = ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT);
                Date end = calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT));
                dataList = organizationSV.getUserNotLoginReport(start, end, orgCode, orgIds);
            }

            XSSFSheet sheet = workbook.getSheet("党建未登录统计表");
            exportUserNotLoginData(dataList, sheet);
            retainSheetIndexSet.add(workbook.getSheetIndex(sheet));

            // 去除不需导出的sheet页
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (!retainSheetIndexSet.contains(i)) {
                    workbook.removeSheetAt(i);
                }
            }

            String fileName = "党建未登录统计表" + "（" + (StringUtils.isBlank(startDate) ? "" : startDate) + "-" + (StringUtils.isBlank(endDate) ? "" : endDate) + "）";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(FILE_READ_EXCEPTION);
        } finally {
            closeStream(is, workbook);
        }
    }

    private void exportUserNotLoginData(List<StaNotLoginDTO> dataList, XSSFSheet sheet) {
        int indexRow = 2;
        // 序号
        int indexNo = 1;
        // 时间格式化
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 遍历结果集
        for (StaNotLoginDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(indexNo);
            // 第二列-工号
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getHrId());
            // 第三列-姓名
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getUserName());
            // 第四列-联系电话
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getPhoneNum());
            // 第五列-所属组织
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(data.getOrgName());
            // 第六列-最近登陆时间
            XSSFCell cell6 = row.createCell(5);
            cell6.setCellValue(null == data.getLastLoginTime() ? "" : sdf.format(data.getLastLoginTime()));

            indexNo++;
            indexRow++;
        }
    }

    @RequestMapping(value = "/queryOrgElecionChange", method = RequestMethod.POST)
    @ApiOperation(value = "换届统计列表", notes = "换届统计列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgType", value = "党组机构类别(2公司党委,1党委,3分公司党总支,4党支部,5党小组)", required = false, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgElectionChangeStatisDTO> queryOrgElecionChange(Integer page, Integer limit, String startDate, String endDate, String orgName, String orgCode, Integer orgType) {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        // 参数转换
        VQueryElectionChangeBean paramBean = new VQueryElectionChangeBean();
        paramBean.setStartDate(ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT));
        paramBean.setEndDate(calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT)));
        paramBean.setOrgName(orgName);
        paramBean.setCurrentOrgCode(orgCode);
        paramBean.setOrgType(orgType);
        return organizationSV.getOrgElecionChange(pageNum, pageSize, paramBean);
    }

    @RequestMapping(value = "/exportOrgElecionChange", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出换届统计列表", notes = "导出换届统计列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgType", value = "党组机构类别(2公司党委,1党委,3分公司党总支,4党支部,5党小组)", required = false, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportOrgElecionChange(HttpServletResponse response, String startDate, String endDate, String orgName, String orgCode, Integer orgType) throws GeneralException {
        InputStream is = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/statistics_report_template.xlsx");
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);

            Set<Integer> retainSheetIndexSet = new HashSet<>(10);
            // 查询参数处理
            VQueryElectionChangeBean paramBean = new VQueryElectionChangeBean();
            paramBean.setStartDate(ConvertUtil.convertStringToDate(startDate, DEFAULT_DATE_FORMAT));
            paramBean.setEndDate(calNextDay(ConvertUtil.convertStringToDate(endDate, DEFAULT_DATE_FORMAT)));
            paramBean.setOrgName(orgName);
            paramBean.setCurrentOrgCode(orgCode);
            paramBean.setOrgType(orgType);
            // 结果数据集
            List<OrgElectionChangeStatisDTO> dataList = organizationSV.getOrgElecionChange(paramBean);

            XSSFSheet sheet = workbook.getSheet("换届统计报表");
            exportOrgElecionChange(dataList, sheet, startDate, endDate);
            retainSheetIndexSet.add(workbook.getSheetIndex(sheet));

            // 去除不需导出的sheet页
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (!retainSheetIndexSet.contains(i)) {
                    workbook.removeSheetAt(i);
                }
            }

            String fileName = "换届统计报表" + "（" + (StringUtils.isBlank(startDate) ? "" : startDate) + "-" + (StringUtils.isBlank(endDate) ? "" : endDate) + "）";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(FILE_READ_EXCEPTION);
        } finally {
            closeStream(is, workbook);
        }
    }

    private void exportOrgElecionChange(List<OrgElectionChangeStatisDTO> dataList, XSSFSheet sheet, String startDate, String endDate) {
        int indexRow = 2;
        // 序号
        int indexNo = 1;
        // 时间格式化
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        // 遍历结果集
        for (OrgElectionChangeStatisDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(indexNo);
            // 第二列-日期范围
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(startDate + "-" + endDate);
            // 第三列-党组织名称
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getOrgName());
            // 第四列-党组织类别
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(turnOrgTypeToDesc(data.getOrgType()));
            // 第五列-最近一次换届日期
            XSSFCell cell6 = row.createCell(4);
            cell6.setCellValue(null == data.getElectionChangeDate() ? "" : sdf.format(data.getElectionChangeDate()));

            indexNo++;
            indexRow++;
        }
    }

    @RequestMapping(value = "/queryOrgLeaderDefect", method = RequestMethod.POST)
    @ApiOperation(value = "缺额委员统计列表", notes = "缺额委员统计列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgType", value = "党组机构类别(2公司党委,1党委,3分公司党总支,4党支部,5党小组)", required = true, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgElectionChangeStatisDTO> queryOrgLeaderDefect(Integer page, Integer limit, String orgName, String orgCode, Integer orgType) throws GeneralException {
        // 参数校验
        if (null == orgType || 0 == orgType) {
            // 由于新增的需求（根节点去重判断书籍和委员人数和）,和原需求其他类型的组织统计逻辑有别，不便一起做列表和分页查询，故暂定不允许组织类型传空
            logger.error("组织类型有误");
            throw new GeneralException("PBMS_PM_0002", "组织类型有误");
        }
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        // 参数转换
        VQueryLeaderDefectBean paramBean = new VQueryLeaderDefectBean();
        paramBean.setOrgName(orgName);
        paramBean.setCurrentOrgCode(orgCode);
        paramBean.setOrgType(orgType);
        return organizationSV.getOrgLeaderDefect(pageNum, pageSize, paramBean);
    }

    @RequestMapping(value = "/exportOrgLeaderDefect", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出缺额委员统计报表", notes = "导出缺额委员统计报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgName", value = "组织名称（模糊查询）", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgType", value = "党组机构类别(2公司党委,1党委,3分公司党总支,4党支部,5党小组)", required = true, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportOrgLeaderDefect(HttpServletResponse response, String orgName, String orgCode, Integer orgType) throws GeneralException {
        // 参数校验
        if (null == orgType || 0 == orgType) {
            // 由于新增的需求（根节点去重判断书籍和委员人数和）,和原需求其他类型的组织统计逻辑有别，不便一起做列表和分页查询，故暂定不允许组织类型传空
            logger.error("组织类型有误");
            throw new GeneralException("PBMS_PM_0002", "组织类型有误");
        }

        InputStream is = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/statistics_report_template.xlsx");
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);

            Set<Integer> retainSheetIndexSet = new HashSet<>(10);
            // 查询参数处理
            VQueryLeaderDefectBean paramBean = new VQueryLeaderDefectBean();
            paramBean.setOrgName(orgName);
            paramBean.setCurrentOrgCode(orgCode);
            paramBean.setOrgType(orgType);
            // 结果数据集
            List<OrgElectionChangeStatisDTO> dataList = organizationSV.getOrgLeaderDefect(paramBean);

            XSSFSheet sheet = workbook.getSheet("缺额委员统计报表");
            exportOrgLeaderDefect(dataList, sheet);
            retainSheetIndexSet.add(workbook.getSheetIndex(sheet));

            // 去除不需导出的sheet页
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (!retainSheetIndexSet.contains(i)) {
                    workbook.removeSheetAt(i);
                }
            }

            String fileName = "缺额委员统计报表";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(FILE_READ_EXCEPTION);
        } finally {
            closeStream(is, workbook);
        }
    }

    private void exportOrgLeaderDefect(List<OrgElectionChangeStatisDTO> dataList, XSSFSheet sheet) {
        int indexRow = 2;
        // 序号
        int indexNo = 1;

        // 遍历结果集
        for (OrgElectionChangeStatisDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-序号
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(indexNo);
            // 第二列-党组织名称
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getOrgName());
            // 第三列-党组织类别
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(turnOrgTypeToDesc(data.getOrgType()));
            // 第四列-书记是否分配
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getShujiNum() > 0 ? "是" : "否");
            // 第五列-委员是否分配
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(data.getWeiyuanNum() > 0 ? "是" : "否");
            // 第六列-委员配置数量
            XSSFCell cell6 = row.createCell(5);
            cell6.setCellValue(data.getWeiyuanNum());

            indexNo++;
            indexRow++;
        }
    }

    private String turnOrgTypeToDesc(int orgType) {
        String result = "未知";
        if (1 == orgType || 2 == orgType) {
            result = "党委";
        } else if (3 == orgType) {
            result = "党总支";
        } else if (4 == orgType) {
            result = "党支部";
        } else if (5 == orgType) {
            result = "党小组";
        }
        return result;
    }

    @RequestMapping(value = "/exportUserInfo", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "用户查询导出", notes = "用户查询导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "searchParam", value = "查询条件json", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织权限编码", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportUserInfo(HttpServletResponse response, String searchParam, String orgCode) throws GeneralException {
        // 参数处理
        Map<String, Object> paramMap = new HashMap<>(16);
        if (StringUtils.isNotBlank(searchParam)) {
            paramMap = (Map<String, Object>) JSON.parse(searchParam);
        }
        paramMap.put("roleType", getUser().getCurrentUserRole().getRoleType());
        paramMap.put("currentOrgCode", orgCode);
        // 结果数据集
        List<UserListBean> dataList = userSV.getByParamsForList(paramMap);
        InputStream is = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/statistics_report_template.xlsx");
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);

            Set<Integer> retainSheetIndexSet = new HashSet<>(10);
            // 结果数据集
//            List<UserListBean> dataList = userSV.getByParamsForList(paramMap);

            XSSFSheet sheet = workbook.getSheet("用户查询导出名单");
            XSSFCellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setBorderBottom(BorderStyle.THIN); // 下边框
            cellStyle.setBorderLeft(BorderStyle.THIN);// 左边框
            cellStyle.setBorderTop(BorderStyle.THIN);// 上边框
            cellStyle.setBorderRight(BorderStyle.THIN);// 右边框

            exportUserInfo(dataList, sheet, cellStyle);
            retainSheetIndexSet.add(workbook.getSheetIndex(sheet));

            // 去除不需导出的sheet页
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (!retainSheetIndexSet.contains(i)) {
                    workbook.removeSheetAt(i);
                }
            }

            String fileName = "用户查询导出名单";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(FILE_READ_EXCEPTION);
        } finally {
            closeStream(is, workbook);
        }
    }

    private void exportUserInfo(List<UserListBean> dataList, XSSFSheet sheet, XSSFCellStyle cellStyle) {
        int indexRow = 2;
        // 时间格式化
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 党员属性
        Map<String, String> userStageMap = dictionaryItemsSV.getDictionaryMapByCode(DICT_CODE_USER_STAGE);
        // 在职状态
        Map<Integer, String> workStatusMap = new HashMap<>(2);
        workStatusMap.put(1, "在职");
        workStatusMap.put(2, "离职");

        // 遍历结果集
        for (UserListBean data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-姓名
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(data.getUsername());
            cell1.setCellStyle(cellStyle);
            // 第二列-工号
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getHrid());
            cell2.setCellStyle(cellStyle);
            // 第三列-手机号
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getTelephones());
            cell3.setCellStyle(cellStyle);
            // 第四列-入党日期
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(null == data.getJoinDate() ? "" : sdf.format(data.getJoinDate()));
            cell4.setCellStyle(cellStyle);
            // 第五列-党员属性
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(userStageMap.containsKey(data.getStage()) ? userStageMap.get(data.getStage()) : "");
            cell5.setCellStyle(cellStyle);
            // 第六列-所属党组织
            XSSFCell cell6 = row.createCell(5);
            cell6.setCellValue(data.getOrgName());
            cell6.setCellStyle(cellStyle);
            // 第六列-是否在职
            XSSFCell cell7 = row.createCell(6);
            cell7.setCellValue(workStatusMap.containsKey(data.getWorkStatus()) ? workStatusMap.get(data.getWorkStatus()) : "");
            cell7.setCellStyle(cellStyle);
            // 第六列-用户标签
            XSSFCell cell8 = row.createCell(7);
            cell8.setCellValue(data.getUserLabelName());
            cell8.setCellStyle(cellStyle);

            // 第七列-是否上传头像
            XSSFCell cell9 = row.createCell(8);
            cell9.setCellValue(StringUtils.isBlank(data.getProfilephoto()) ? "否" : "是");
            cell9.setCellStyle(cellStyle);

            indexRow++;
        }
    }

    @RequestMapping(value = "/queryMediaProgress", method = RequestMethod.POST)
    @ApiOperation(value = "音视频播放进度统计", notes = "音视频播放进度统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "组织ID", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "specId", value = "音视频专题ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "mediaName", value = "音视频名称", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "mediaType", value = "媒体类型（1-音频，2-视频）", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "userLabel", value = "用户标签", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "userName", value = "用户姓名", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<StisForMediaProgressDTO> queryMediaProgress(Integer page, Integer limit, String orgId, String orgCode, String specId, String mediaName, Integer mediaType, String userLabel, String userName) throws GeneralException, ParseException {
        // 参数校验
        if (StringUtils.isBlank(specId)) {
            logger.error("请求参数错误");
            throw new GeneralException(PARAM_INVALID);
        }
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        return scoreDtlmeetingPlanSV.selectMediaProgressStis(pageSize, pageNum, userName, specId, mediaName, mediaType, orgId, userLabel, orgCode);
    }

    @RequestMapping(value = "/exportMediaProgress", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出音视频播放进度统计", notes = "导出音视频播放进度统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgId", value = "组织ID", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "specId", value = "音视频专题ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "mediaName", value = "音视频名称", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "mediaType", value = "媒体类型（1-音频，2-视频）", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "userLabel", value = "用户标签", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "userName", value = "用户姓名", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportMediaProgress(HttpServletResponse response, String orgId, String orgCode, String specId, String mediaName, Integer mediaType, String userLabel, String userName) throws GeneralException {
        // 参数校验
        if (StringUtils.isBlank(specId)) {
            logger.error("请求参数错误");
            throw new GeneralException(PARAM_INVALID);
        }

        InputStream is = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/statistics_report_template.xlsx");
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);

            Set<Integer> retainSheetIndexSet = new HashSet<>(10);
            // 结果数据集
            List<StisForMediaProgressDTO> dataList = scoreDtlmeetingPlanSV.selectMediaProgressStis(userName, specId, mediaName, mediaType, orgId, userLabel, orgCode);

            XSSFSheet sheet = workbook.getSheet("音视频播放进度统计报表");
            exportMediaProgressData(dataList, sheet);
            retainSheetIndexSet.add(workbook.getSheetIndex(sheet));

            // 去除不需导出的sheet页
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (!retainSheetIndexSet.contains(i)) {
                    workbook.removeSheetAt(i);
                }
            }

            String fileName = "音视频播放进度统计报表";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(FILE_READ_EXCEPTION);
        } catch (ParseException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("PBMS_UC_0006");
        } finally {
            closeStream(is, workbook);
        }
    }

    private void exportMediaProgressData(List<StisForMediaProgressDTO> dataList, XSSFSheet sheet) {
        int indexRow = 2;
        // 时间格式化
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 遍历结果集
        for (StisForMediaProgressDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-专题名称
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(data.getSpecName());
            // 第二列-音频/视频标题
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getTitle());
            // 第三列-多媒体类型
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(turnMediaTypeToDesc(data.getMediaType()));
            // 第四列-创建时间
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(sdf.format(data.getCreateTime()));
            // 第五列-出处
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(data.getMakeFrom());
            // 第六列-所属组织
            XSSFCell cell6 = row.createCell(5);
            cell6.setCellValue(data.getOrgfName());
            // 第七列-党员姓名
            XSSFCell cell7 = row.createCell(6);
            cell7.setCellValue(data.getUserName());
            // 第八列-观看进度
            XSSFCell cell8 = row.createCell(7);
            cell8.setCellValue(data.getProgressRate());
            // 第九列-观看有效时长
            XSSFCell cell9 = row.createCell(8);
            cell9.setCellValue(data.getPlayTimeStr());

            indexRow++;
        }
    }

    private String turnMediaTypeToDesc(int mediaType) {
        String result = "未知";
        if (1 == mediaType) {
            result = "音频";
        } else if (2 == mediaType) {
            result = "视频";
        }
        return result;
    }

    @RequestMapping(value = "/queryOrgBranchLife", method = RequestMethod.POST)
    @ApiOperation(value = "组织生活统计查询", notes = "组织生活统计查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "yearParam", value = "开始日期", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "season", value = "结束日期", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<StatisOrgdBranchLifeDTO> queryOrgBranchLife(Integer page, Integer limit, Integer yearParam, Integer season, String orgCode, String orgIds) {
        // 防sonar报错
        int pageNum;
        if (null == page || page < 1) {
            pageNum = DEFAULT_PAGE_NUMBER;
        } else {
            pageNum = page;
        }
        int pageSize;
        if (null == limit || limit < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        } else {
            pageSize = limit;
        }
        return organizationSV.getOrgBranchLife(pageNum, pageSize, yearParam, season, orgCode, orgIds, true);
    }

    @RequestMapping(value = "/exportOrgBranchLife", method = RequestMethod.GET)
    @ApiOperation(value = "组织生活统计导出", notes = "组织生活统计导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "yearParam", value = "开始日期", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "season", value = "结束日期", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码（权限查询）", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgIds", value = "组织架构id，多项中间用,分隔", required = false, dataType = "string", paramType = "query")})
    public void exportOrgBranchLife(Integer yearParam, Integer season, String orgCode, String orgIds, HttpServletResponse response, HttpServletRequest request) throws GeneralException {

        PageInfo<StatisOrgdBranchLifeDTO> pageInfo = organizationSV.getOrgBranchLife(null, null, yearParam, season, orgCode, orgIds, false);
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            // 生成一个表格
            XSSFSheet sheet = workbook.createSheet("三会一课完成率统计表");
            // 设置表格默认列宽度为15个字节
            sheet.setDefaultColumnWidth((short) 15);

            XSSFCellStyle styleFirst = workbook.createCellStyle();
            styleFirst.setAlignment(HorizontalAlignment.CENTER);
            styleFirst.setVerticalAlignment(VerticalAlignment.CENTER);
            styleFirst.setBorderBottom(BorderStyle.THIN);
            styleFirst.setBorderLeft(BorderStyle.THIN);
            styleFirst.setBorderRight(BorderStyle.THIN);
            styleFirst.setBorderTop(BorderStyle.THIN);
            XSSFFont font = workbook.createFont();
            font.setFontHeightInPoints((short) 12);
            font.setBold(true);
            styleFirst.setFont(font);

            XSSFCellStyle styleSecond = workbook.createCellStyle();
            styleSecond.setAlignment(HorizontalAlignment.RIGHT);
            styleSecond.setVerticalAlignment(VerticalAlignment.CENTER);
            styleSecond.setBorderBottom(BorderStyle.THIN);
            styleSecond.setBorderLeft(BorderStyle.THIN);
            styleSecond.setBorderRight(BorderStyle.THIN);
            styleSecond.setBorderTop(BorderStyle.THIN);
            XSSFFont fontSecond = workbook.createFont();
            fontSecond.setFontHeightInPoints((short) 12);
            fontSecond.setBold(true);
            styleSecond.setFont(fontSecond);

            XSSFCellStyle styleContent = workbook.createCellStyle();
            styleContent.setAlignment(HorizontalAlignment.CENTER);
            styleContent.setVerticalAlignment(VerticalAlignment.CENTER);
            styleContent.setBorderBottom(BorderStyle.THIN);
            styleContent.setBorderLeft(BorderStyle.THIN);
            styleContent.setBorderRight(BorderStyle.THIN);
            styleContent.setBorderTop(BorderStyle.THIN);

            // 生成表头
            genExcelHead(sheet, yearParam, season, styleFirst, styleSecond);
            // 生成内容
            genExcelContent(sheet, ((PageInfo) pageInfo).getList(), styleContent);

            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + checkUserAgentSetFileName("组织生活统计表.xlsx", request));
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.info(e.getMessage(), e);
            throw new SystemFailureException("导出文件失败");
        }
    }

    private void genExcelHead(XSSFSheet sheet, Integer yearParam, Integer season, XSSFCellStyle styleFirst, XSSFCellStyle styleSecond) {
        // 首行-题目
        XSSFRow firstRow = sheet.createRow(0);
        firstRow.setHeight((short) 600);
        firstRow.createCell(0).setCellStyle(styleFirst);
        firstRow.getCell(0).setCellValue("三会一课完成率统计表");

        // 第二行-统计时间范围
        XSSFRow secondRow = sheet.createRow(1);
        secondRow.setHeight((short) 600);
        secondRow.createCell(0).setCellStyle(styleSecond);
        secondRow.getCell(0).setCellValue("统计范围：" + yearParam + "年" + (null == season ? "" : "第" + season + "季度"));

        // 第三、四行-标题行
        XSSFRow thirdRow = sheet.createRow(2);
        restRow(thirdRow, 21, styleFirst);
        thirdRow.getCell(0).setCellValue("所属分公司");
        thirdRow.getCell(1).setCellValue("支部名称");
        thirdRow.getCell(2).setCellValue("创建日期");
        thirdRow.getCell(3).setCellValue("支部党员大会");
        thirdRow.getCell(7).setCellValue("支部委员会");
        thirdRow.getCell(11).setCellValue("党课");
        thirdRow.getCell(15).setCellValue("党小组会");
        thirdRow.getCell(19).setCellValue("三会一课完成率");
        thirdRow.getCell(20).setCellValue("集中学习完成次数");
        thirdRow.getCell(21).setCellValue("主题党日活动完成次数");
        XSSFRow fourthRow = sheet.createRow(3);
        restRow(fourthRow, 21, styleFirst);
        fourthRow.getCell(3).setCellValue("总会议量");
        fourthRow.getCell(4).setCellValue("要求次数");
        fourthRow.getCell(5).setCellValue("完成次数");
        fourthRow.getCell(6).setCellValue("完成率");
        fourthRow.getCell(7).setCellValue("总会议量");
        fourthRow.getCell(8).setCellValue("要求次数");
        fourthRow.getCell(9).setCellValue("完成次数");
        fourthRow.getCell(10).setCellValue("完成率");
        fourthRow.getCell(11).setCellValue("总会议量");
        fourthRow.getCell(12).setCellValue("要求次数");
        fourthRow.getCell(13).setCellValue("完成次数");
        fourthRow.getCell(14).setCellValue("完成率");
        fourthRow.getCell(15).setCellValue("总会议量");
        fourthRow.getCell(16).setCellValue("要求次数");
        fourthRow.getCell(17).setCellValue("完成次数");
        fourthRow.getCell(18).setCellValue("完成率");

        // 合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 21));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 21));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 3, 6));
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 7, 10));
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 11, 14));
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 15, 18));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 19, 19));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 20, 20));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 21, 21));
    }

    private void genExcelContent(XSSFSheet sheet, List<StatisOrgdBranchLifeDTO> dataList, XSSFCellStyle style) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        int beginIndex = 4;
        for (StatisOrgdBranchLifeDTO data : dataList) {
            XSSFRow row = sheet.createRow(beginIndex++);
            restRow(row, 21, style);

            row.getCell(0).setCellValue(data.getProvinceName());
            row.getCell(1).setCellValue(data.getOrgsname());
            row.getCell(2).setCellValue(sdf.format(data.getCreateddate()));

            row.getCell(3).setCellValue(getStringCell(data.getZbdydhTotalNum()));
            row.getCell(4).setCellValue(getStringCell(data.getZbdydhPlanNum()));
            row.getCell(5).setCellValue(getStringCell(data.getZbdydhFinishNum()));
            row.getCell(6).setCellValue(data.getZbdydhFinishRateStr());

            row.getCell(7).setCellValue(getStringCell(data.getZbwyhTotalNum()));
            row.getCell(8).setCellValue(getStringCell(data.getZbwyhPlanNum()));
            row.getCell(9).setCellValue(getStringCell(data.getZbwyhFinishNum()));
            row.getCell(10).setCellValue(data.getZbwyhFinishRateStr());

            row.getCell(11).setCellValue(getStringCell(data.getDkTotalNum()));
            row.getCell(12).setCellValue(getStringCell(data.getDkPlanNum()));
            row.getCell(13).setCellValue(getStringCell(data.getDkFinishNum()));
            row.getCell(14).setCellValue(data.getDkFinishRateStr());

            row.getCell(15).setCellValue(getStringCell(data.getDxzhTotalNum()));
            row.getCell(16).setCellValue(getStringCell(data.getDxzhPlanNum()));
            row.getCell(17).setCellValue(getStringCell(data.getDxzhFinishNum()));
            row.getCell(18).setCellValue(data.getDxzhFinishRateStr());

            row.getCell(19).setCellValue(data.getFinishRateStr());
            row.getCell(20).setCellValue(data.getStudyMeetingNum());
            row.getCell(21).setCellValue(data.getActiveNum());
        }
    }

    private String getStringCell(Integer num) {
        if (null == num) {
            return "无";
        }
        return "" + num;
    }

    private void restRow(XSSFRow row, Integer cellIndex, XSSFCellStyle style) {
        for (int i = 0; i <= cellIndex; i++)
            row.createCell(i).setCellStyle(style);
    }

    @RequestMapping(value = "/staMeCompletionRate", method = RequestMethod.POST)
    @ApiOperation(value = "三会一课完成率统计", notes = "三会一课完成率统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "yearParam", value = "年度", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "season", value = "季度", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "staDate", value = "统计日期", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "组织架构id", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<OrgMeetingFinishRateDTOTmp> staMeCompletionRate(String orgId, Integer yearParam, Integer season, Date staDate, Integer page, Integer limit) throws GeneralException {
        if (null == page || null == limit || yearParam == null || season == null) {
            logger.error("请求参数错误");
            throw new GeneralException(PARAM_INVALID);
        }
        PageInfo<OrgMeetingFinishRateDTOTmp> datas = bigScreenSV.staMeCompletionInfo(orgId, yearParam, season, page, limit, true);
        setDatas(datas, yearParam, season, staDate);
        return datas;
    }

    private void setDatas(PageInfo<OrgMeetingFinishRateDTOTmp> pageInfo, Integer yearParam, Integer season, Date staDate) throws GeneralException {
        //查询本部党支部
        List<Organization> HQbranchs = bigScreenSV.getHQOrgs();
        Map<String, Organization> HQmap = new HashMap<>();
        for (Organization organization : HQbranchs) {
            HQmap.put(organization.getId(), organization);
        }

        if (null != staDate) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(staDate);
            calendar.add(Calendar.DATE, 1);
            staDate = calendar.getTime();
        }

        int i = 0;
        for (OrgMeetingFinishRateDTOTmp org : pageInfo.getList()) {
            String initType = "01";
            if (HQmap.get(org.getOrgId()) != null) {
                initType = "02";
            }

            OrgMeetingFinishRateDTOTmp nOrg = bigScreenSV.meCompletionInfo(org.getOrgId(), yearParam, season, initType, staDate);
            nOrg.setOrgFullName(org.getOrgFullName());
            nOrg.setOrgId(org.getOrgId());
            pageInfo.getList().set(i, nOrg);
            i++;
        }

    }

    @RequestMapping(value = "/exportMeCompletionRate", method = {RequestMethod.GET, RequestMethod.POST})
    @ApiOperation(value = "三会一课完成率统计导出", notes = "三会一课完成率统计导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "yearParam", value = "年度", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "season", value = "季度", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "staDate", value = "统计日期", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "组织架构id", required = false, dataType = "string", paramType = "query")})
    public void exportMeCompletionRate(String orgId, Integer yearParam, Integer season, Date staDate, HttpServletResponse response, HttpServletRequest request) throws GeneralException {
        if (yearParam == null || season == null) {
            logger.error("请求参数错误");
            throw new GeneralException(PARAM_INVALID);
        }

        PageInfo<OrgMeetingFinishRateDTOTmp> datas = bigScreenSV.staMeCompletionInfo(orgId, yearParam, season, null, null, false);

        setDatas(datas, yearParam, season, staDate);

        XSSFWorkbook workbook = new XSSFWorkbook();

        XSSFSheet sheet = workbook.createSheet("三会一课完成次数统计");

        XSSFCellStyle styleFirst = workbook.createCellStyle();
        styleFirst.setAlignment(HorizontalAlignment.CENTER);
        styleFirst.setVerticalAlignment(VerticalAlignment.CENTER);

        XSSFRow firstRow = sheet.createRow(0);
        firstRow.createCell(0).setCellValue("三会一课完成次数统计");
        firstRow.getCell(0).setCellStyle(styleFirst);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));

        XSSFRow secondRow = sheet.createRow(1);
        secondRow.createCell(0).setCellValue("序号");
        secondRow.createCell(1).setCellValue("党组织名称");
        secondRow.createCell(2).setCellValue("要求完成次数");
        secondRow.createCell(3).setCellValue("计划内完成次数");
        secondRow.createCell(4).setCellValue("实际完成次数");
        secondRow.createCell(5).setCellValue("完成率");

        int contentRow = 2;
        int idx = 1;
        for (OrgMeetingFinishRateDTOTmp tmp : datas.getList()) {
            XSSFRow row = sheet.createRow(contentRow);
            row.createCell(0).setCellValue(idx);
            row.createCell(1).setCellValue(tmp.getOrgFullName());
            row.createCell(2).setCellValue(tmp.getTotalNum());
            row.createCell(3).setCellValue(tmp.getCountFinishNum());
            row.createCell(4).setCellValue(tmp.getFinishNum());
            row.createCell(5).setCellValue(tmp.getFinishRate());
            contentRow++;
            idx++;
        }

        OutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + checkUserAgentSetFileName("三会一课完成次数统计表.xlsx", request));
            workbook.write(outputStream);
            outputStream.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("导出文件失败");
        }

    }

    @RequestMapping(value = "/staCeActs", method = RequestMethod.POST)
    @ApiOperation(value = "主题党日活动完成率统计", notes = "主题党日活动完成率统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "yearParam", value = "年度", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "season", value = "季度", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "staDate", value = "统计日期", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "组织架构id", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<StaCeActs> staCeActs(String orgId, Integer yearParam, Integer season, Date staDate, Integer page, Integer limit) throws GeneralException {
        if (null == page || null == limit || yearParam == null || season == null) {
            logger.error("请求参数错误");
            throw new GeneralException(PARAM_INVALID);
        }
        if (null != staDate) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(staDate);
            calendar.add(Calendar.DATE, 1);
            staDate = calendar.getTime();
        }
        return statisticsSV.getAllBranchCeAct(yearParam, season, page, limit, true, staDate);
    }

    @RequestMapping(value = "/exportStaCeActs", method = {RequestMethod.GET, RequestMethod.POST})
    @ApiOperation(value = "主题党日活动完成率统计导出", notes = "主题党日活动完成率统计导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "yearParam", value = "年度", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "season", value = "季度", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "staDate", value = "统计日期", required = false, dataType = "date", paramType = "query")})
    public void exportStaCeActs(Integer yearParam, Integer season, Date staDate, HttpServletResponse response, HttpServletRequest request) throws GeneralException {
        if (yearParam == null || season == null) {
            logger.error("请求参数错误");
            throw new GeneralException(PARAM_INVALID);
        }

        if (null != staDate) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(staDate);
            calendar.add(Calendar.DATE, 1);
            staDate = calendar.getTime();
        }
        PageInfo<StaCeActs> datas = statisticsSV.getAllBranchCeAct(yearParam, season, null, null, false, staDate);

        XSSFWorkbook workbook = new XSSFWorkbook();

        XSSFSheet sheet = workbook.createSheet("主题党日活动完成次数统计");

        XSSFCellStyle styleFirst = workbook.createCellStyle();
        styleFirst.setAlignment(HorizontalAlignment.CENTER);
        styleFirst.setVerticalAlignment(VerticalAlignment.CENTER);

        XSSFRow firstRow = sheet.createRow(0);
        firstRow.createCell(0).setCellValue("主题党日活动完成次数统计");
        firstRow.getCell(0).setCellStyle(styleFirst);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));

        XSSFRow secondRow = sheet.createRow(1);
        secondRow.createCell(0).setCellValue("序号");
        secondRow.createCell(1).setCellValue("党组织名称");
        secondRow.createCell(2).setCellValue("要求完成次数");
        secondRow.createCell(3).setCellValue("计划内完成次数");
        secondRow.createCell(4).setCellValue("实际完成次数");
        secondRow.createCell(5).setCellValue("完成率");

        int contentRow = 2;
        int idx = 1;
        for (StaCeActs tmp : datas.getList()) {
            XSSFRow row = sheet.createRow(contentRow);
            row.createCell(0).setCellValue(idx);
            row.createCell(1).setCellValue(tmp.getOrgFullName());
            row.createCell(2).setCellValue(tmp.getTotalNum());
            row.createCell(3).setCellValue(tmp.getCountFinishNum());
            row.createCell(4).setCellValue(tmp.getFinishNum());
            row.createCell(5).setCellValue(tmp.getFinishRate());
            contentRow++;
            idx++;
        }

        OutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + checkUserAgentSetFileName("主题党日活动完成次数统计表.xlsx", request));
            workbook.write(outputStream);
            outputStream.flush();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("导出文件失败");
        }
    }

    @RequestMapping(value = "/getPartyMemberStatistics", method = {RequestMethod.POST})
    @ApiOperation(value = "年度党员统计", notes = "年度党员统计")
    public PageInfo<PartyMemberStatisticsDTO> getPartyMemberStatistics(String orgId, Date startDate, Date endDate, Integer page, Integer limit) throws GeneralException {
        String orgCode = getUser().getCurrentRoleOrg().getCodestr();
        if (orgCode != null && !orgCode.isEmpty()) {
            orgCode = orgCode.split("\\.")[0];
        }
        return statisticsSV.getPartyMemberStatistics(orgId,startDate, endDate, true, page, limit,orgCode);
    }
    @RequestMapping(value = "/getOrganizationStatistics", method = {RequestMethod.POST})
    @ApiOperation(value = "党组织生活统计", notes = "党组织生活统计")
    public PageInfo<OrgLifeStatistics> getOrganizationStatistics(String orgId, Date startDate,Date endDate, Integer page, Integer limit){
        if (startDate == null){
            //startDate
            // 获取当前年份的1月1日
            LocalDate firstDayOfYear = LocalDate.of(LocalDate.now().getYear(), 1, 1);
            // 将LocalDate转换为Date
            startDate = Date.from(firstDayOfYear.atStartOfDay(ZoneId.systemDefault()).toInstant());
        }
        if (endDate == null){
            //当前时间
            endDate = new Date();
        }
        if (!StringUtils.isNotBlank(orgId)){
            //默认为当前组织id
            orgId = getUser().getOrgid();
        }
        return statisticsSV.getOrganizationStatistics(orgId, startDate,endDate,true, page, limit);
    }
    @RequestMapping(value = "/getInternalCommendationStatistics", method = {RequestMethod.POST})
    @ApiOperation(value = "党内表彰统计", notes = "党内表彰统计")
    public PageInfo<InternalCommendationStatisticsDTO> internalCommendationStatistics(String orgId, Date staDate,Date endDate, Integer page, Integer limit){
        //todo 获得党内表彰数量统计 待业务确定
        List<InternalCommendationStatisticsDTO> list = new ArrayList<>();
        return new PageInfo<>(list);
    }
    //党员学历分布统计
    @RequestMapping(value = "/getEducationalDistributionStatistics", method = {RequestMethod.POST})
    @ApiOperation(value = "党员学历分布统计", notes = "党员学历分布统计")
    public PageInfo<EducationalDistributionStatisticsDTO> getEducationalDistributionStatistics(String orgId, Date staDate,Date endDate, Integer page, Integer limit) throws GeneralException {
        if (staDate == null){
            //当前年1月1日
            // 获取当前年份的1月1日
            LocalDate firstDayOfYear = LocalDate.of(LocalDate.now().getYear(), 1, 1);
            // 将LocalDate转换为Date
            staDate = Date.from(firstDayOfYear.atStartOfDay(ZoneId.systemDefault()).toInstant());
        }
        if (endDate == null){
            //当前时间
            endDate = new Date();
        }
        if (!StringUtils.isNotBlank(orgId)){
            //默认为当前组织id
            orgId = getUser().getOrgid();
        }
        return statisticsSV.getEducationalDistributionStatistics(orgId, staDate,endDate,true, page, limit);
    }
    @RequestMapping(value = "/getPartyOrganizationCadresStatistics", method = {RequestMethod.POST})
    @ApiOperation(value = "党组织干部统计", notes = "党组织干部统计")
    public PageInfo<PartyOrganizationCadresStatisticsDTO> getPartyOrganizationCadresStatistics(String orgId, Integer page, Integer limit) throws GeneralException {
        if (!StringUtils.isNotBlank(orgId)){
            //默认为当前组织id
            orgId = getUser().getOrgid();
        }
        return statisticsSV.getPartyOrganizationCadresStatistics(orgId, page, limit);
    }

    /**
     * 任务完成情况统计(省中心下发)
     */
    @RequestMapping(value = "getTaskCompletionStatistics",method = {RequestMethod.POST})
    @ApiOperation(value = "任务完成情况统计(省中心下发)", notes = "任务完成情况统计(省中心下发)")
    public PageInfo<TaskCompletionStatisticsDTO> getTaskCompletionStatistics(String orgId, Date staDate,Date endDate,Integer page, Integer limit) throws GeneralException {
        if (staDate == null){
            //当前年1月1日
            // 获取当前年份的1月1日
            LocalDate firstDayOfYear = LocalDate.of(LocalDate.now().getYear(), 1, 1);
            // 将LocalDate转换为Date
            staDate = Date.from(firstDayOfYear.atStartOfDay(ZoneId.systemDefault()).toInstant());
        }
        if (endDate == null){
            //当前时间
            endDate = new Date();
        }
        if (!StringUtils.isNotBlank(orgId)){
            //默认为当前组织id
            orgId = getUser().getOrgid();
        }
        return statisticsSV.getTaskCompletionStatistics(orgId,staDate,endDate,page, limit);
    }

    @RequestMapping(value = "/getPartyStatistics", method = {RequestMethod.POST})
    @ApiOperation(value = "在册党员统计", notes = "在册党员统计")
    public PageInfo<PartyStatisticsVo> getPartyStatistics(String orgId, String name, Date startDate, Date endDate, Integer yearParam, Integer season, Date staDate, Integer page, Integer limit) throws GeneralException {
        String orgCode = getUser().getCurrentRoleOrg().getCodestr();
        return statisticsSV.getPartyStatistics(orgId, name, startDate, endDate, yearParam, season, staDate,true, page, limit,orgCode);
    }

    @RequestMapping(value = "/getApplyForPartyStatistics", method = {RequestMethod.POST})
    @ApiOperation(value = "申请入党名册", notes = "入党积极份子名册")
    public PageInfo<ApplyForPartyStatisticsVo> getApplyForPartyStatistics(String orgId, Integer yearParam, Integer season, Date staDate, Integer page, Integer limit) throws GeneralException {
        String orgCode = getUser().getCurrentRoleOrg().getCodestr();
        return statisticsSV.getApplyForPartyStatistics(orgId, yearParam, season, staDate, true, page, limit, orgCode);
    }

    @RequestMapping(value = "/getActivePartyMemberStatistics", method = {RequestMethod.POST})
    @ApiOperation(value = "入党积极份子名册", notes = "入党积极份子名册")
    public PageInfo<ActivePartyMemberStatisticsVo> getActivePartyMemberStatistics(String orgId, Integer yearParam, Integer season, Date staDate, Integer page, Integer limit) throws GeneralException {
        String orgCode = getUser().getCurrentRoleOrg().getCodestr();
        return statisticsSV.getActivePartyMemberStatistics(orgId, yearParam, season, staDate, true, page, limit, orgCode);
    }

    @RequestMapping(value = "/getPartyMemberDevelopmentTargets", method = {RequestMethod.POST})
    @ApiOperation(value = "党员发展对象统计", notes = "党员发展对象统计")
     public PageInfo<PartyMemberDevelopmentTargets>  getPartyMemberDevelopmentTargets(String orgId, Integer yearParam, Integer season, Date staDate, Integer page, Integer limit) throws GeneralException {
        return statisticsSV.getPartyMemberDevelopmentTargets(orgId, yearParam, season, staDate,true, page, limit);
     }

    @RequestMapping(value = "/setActivePartyMember", method = {RequestMethod.POST})
    @ApiOperation(value = "入党积极分子状态修改", notes = "入党积极分子状态修改")
    public int setActivePartyMember(String id, Integer status) throws GeneralException {
        return userSV.setActivePartyMember(id, status);
    }

    @RequestMapping(value = "/setInspectionAndEvaluation", method = {RequestMethod.POST})
    @ApiOperation(value = "考察评价修改", notes = "考察评价修改")
    public int setInspectionAndEvaluation(String id, String inspectionAndEvaluation) throws GeneralException {
        return userSV.setInspectionAndEvaluation(id, inspectionAndEvaluation);
    }

    @RequestMapping(value = "/getOrganizationTypeStatistics", method = {RequestMethod.POST})
    @ApiOperation(value = "党组织统计", notes = "党组织统计")
    public PageInfo<OrgTypeStatisticsDTO> getOrgTypeStatistics(String orgId, Integer page, Integer limit) throws GeneralException {
        if (!StringUtils.isNotBlank(orgId)){
            //默认为当前组织id
            orgId = getUser().getOrgid();
        }
        return statisticsSV.getOrgTypeStatistics(orgId, page, limit);
    }


    @RequestMapping(value = "/getOrganizationTypeAndUserDegreeStatistics", method = {RequestMethod.POST})
    @ApiOperation(value = "党组织类型与用户学历统计", notes = "党组织类型与用户学历统计")
    public OrgTypeCountAndUserDegreesStatisticsDTO getOrganizationTypeAndUserDegreeStatistics() throws GeneralException {

        return statisticsSV.getOrgTypeCountAndUserDegreesStatistics();
    }

    @RequestMapping(value = "/getOrganizationGanBuNeedSetTips", method = {RequestMethod.POST})
    @ApiOperation(value = "获取组织需要设置干部的提醒", notes = "获取组织需要设置干部的提醒")
    public PageInfo<PartyOrganizationCadresStatisticsDTO> getOrganizationGanBuNeedSetTips() throws GeneralException {
        /**
         * 按用户角色查询
         * 1、系统管理员角色，提示所有组织需要设置干部的提醒
         * 2、党务工作者，提示所属组织的干部设置
         */
        UserRole userRole = getUser().getCurrentUserRole();
        System.out.println("#getOrganizationGanBuNeedSetTips userRole = " + JSON.toJSONString(userRole));
        String roleType = userRole.getRoleType();
        String orgId = null;
        if ("6".equals(roleType)){// 系统管理员角色，可弹全部未设置党支部书记，党支部支委信息

        } else if ("1".equals(roleType)){
            // 角色：党务工作者，仅查当前组织
            orgId = userRole.getOrgid();
        }

        PageInfo<PartyOrganizationCadresStatisticsDTO> pageInfo = statisticsSV.getPartyOrganizationCadresStatistics(orgId, 1, 100);

        List<PartyOrganizationCadresStatisticsDTO> list = pageInfo.getList();
        List<PartyOrganizationCadresStatisticsDTO> result = list.stream().filter(p -> p.getDangzhibushuji() == 0 || p.getDangzhibuweiyuan() == 0).collect(Collectors.toList());
        pageInfo.setList(result);
        pageInfo.setPageSize(100);
        pageInfo.setTotal(result.size());
//        if (!StringUtils.isNotBlank(orgId)){
//            //默认为当前组织id
//            orgId = getUser().getOrgid();
//        }
        return pageInfo;
    }

}
