package com.cmos.pbms.web.controller.dd;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.cmos.common.exception.ValidationException;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.common.validator.common.VSearchParam;
import com.cmos.pbms.beans.common.Attachments;
import com.cmos.pbms.beans.dd.Answers;
import com.cmos.pbms.beans.dd.COpinipnBean;
import com.cmos.pbms.beans.dd.Question;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.iservice.common.IAttachmentsSV;
import com.cmos.pbms.iservice.dd.IAnswererSV;
import com.cmos.pbms.utils.OnestUtil;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.assertj.core.util.Strings;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/answerer")
@Validated
@Api(description = "民主评议参与者信息操作控制器")
public class AnswererController extends BaseController {

    @Reference(group = "pbms")
    private IAnswererSV answererSV;

    @Reference(group = "pbms")
    private IAttachmentsSV attachmentsSV;

    @RequestMapping(value = "/queryNumBySubId", method = RequestMethod.GET)
    @ApiOperation(value = "根据id查询活动参与者信息", notes = "根据id查询活动参与者信息")
    @ApiImplicitParams(@ApiImplicitParam(name = "subjectid", value = "活动参与者id", required = true, dataType = "String", paramType = "query"))
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Map<String, Object> queryNumBySubId(@RequestParam String subjectid) throws ValidationException {
        if (Strings.isNullOrEmpty(subjectid)) {
            throw new ValidationException("PBMS_AC_0004");
        }
        return answererSV.getNumBySubjectid(subjectid);
    }

    @RequestMapping(value = "/queryOpinionBySubId", method = RequestMethod.POST)
    @ApiOperation(value = "根据民主评议id查询提交列表", notes = "根据民主评议id查询提交列表")
    @ApiImplicitParams(@ApiImplicitParam(name = "params", value = "条件", required = true, dataType = "String", paramType = "query"))
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<COpinipnBean> queryOpinionBySubId(VSearchParam params) throws ValidationException {
        Map<String, Object> searchParam = new HashMap<>(16);
        int pageNum = params.getPage();
        int pageSize = params.getLimit();
        if (!Strings.isNullOrEmpty(params.getSearchParam())) {
            searchParam = (Map<String, Object>) JSON.parse(params.getSearchParam());
            if (!searchParam.containsKey("subjectid")) {
                throw new ValidationException("PBMS_AC_0004");
            }
        }

        return answererSV.getOpinipnList(pageNum, pageSize, searchParam);
    }

    @RequestMapping(value = "/updateAnswerer", method = RequestMethod.POST)
    @ApiOperation(value = "修改活动参与者信息(组织回复)", notes = "修改活动参与者信息(组织回复)")
    @ApiImplicitParams(@ApiImplicitParam(name = "actors", value = "活动参与者", required = true, dataType = "String", paramType = "query"))
    public void updateAnswerer(@ModelAttribute Answers answers) {
        Users currentUser = getUser();
        String userid = currentUser.getId();
        answererSV.updateReplier(answers, userid);
    }

    @RequestMapping(value = "/queryStatistical", method = RequestMethod.GET)
    @ApiOperation(value = "根据民主评议id查询统计结果(后台和APP用到)", notes = "根据民主评议id查询统计结果(后台和APP用到)")
    @ApiImplicitParams(@ApiImplicitParam(name = "subjectid", value = "民主评议id", required = true, dataType = "String", paramType = "query"))
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public List<Map<String, Object>> queryStatistical(@RequestParam String subjectid) throws ValidationException {
        if (Strings.isNullOrEmpty(subjectid)) {
            throw new ValidationException("PBMS_AC_0004");
        }
        Users currentUser = getUser();
        String userid = currentUser.getId();
        //从onest上拿数据
        Attachments attachments = attachmentsSV.selectByDD(subjectid);
        if (attachments == null) {
            return null;
        }
        String qjson = OnestUtil.getContent(attachments.getUrl());
        List<Question> qlist = Strings.isNullOrEmpty(qjson) ? null : JSON.parseArray(qjson, Question.class);
        return answererSV.getStatistical(qlist, userid);
    }

}
