package com.cmos.pbms.web.controller.bc;

import com.alibaba.dubbo.config.annotation.Reference;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.exception.SystemFailureException;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.dto.*;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.iservice.bc.IBcTaskSV;
import com.cmos.pbms.utils.ValidateUtil;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.List;

@RestController
@RequestMapping(value = "/bcTask")
@Validated
@Api(description = "支部名片控制器")
public class BcTaskController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(BcTaskController.class);

    @Reference(group = "pbms")
    private IBcTaskSV bcTaskSV;

    @RequestMapping(value = "/getToDoBcTaskList", method = RequestMethod.POST)
    @ApiOperation(value = "分页查询当前用户的支部名片任务列表", notes = "分页查询当前用户的支部名片任务列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "todoState", value = "状态（ 0:代办，1：已办。）", required = false, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页记录数", required = true, dataType = "Integer", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<BcToDoTaskListDTO> getToDoBcTaskList(Integer todoState, Integer page, Integer limit) throws GeneralException {
        ValidateUtil.isNotNull(page, limit);
        Users currentUser = getUser();
//        if (!"1".equals(currentUser.getCurrentUserRole().getRoleType()))
//            return new PageInfo<>(new ArrayList<BcToDoTaskListDTO>());
        return bcTaskSV.getToDoBcTaskList(currentUser.getCurrentRoleOrg().getId(), todoState, page, limit, currentUser.getId(), currentUser.getCurrentRoleId());
    }

    @RequestMapping(value = "/getToDoBcTaskDetail", method = RequestMethod.POST)
    @ApiOperation(value = "支部名片任务详情", notes = "支部名片任务详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "bctaId", value = "答案记录主键", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public BcToDoTaskDetailDTO getToDoBcTaskDetail(String bctaId) throws GeneralException {
        ValidateUtil.isNotEmpty(bctaId);

        return bcTaskSV.getToDoBcTaskDetail(bctaId);
    }

    @RequestMapping(value = "/getLastCycleAmQuestionDetail", method = RequestMethod.POST)
    @ApiOperation(value = "获取上周期支部名片任务详情", notes = "获取上周期支部名片任务详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "bctaId", value = "答案记录主键", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public BcToDoTaskDetailDTO getLastCycleAmQuestionDetail(String bctaId) throws GeneralException {
        ValidateUtil.isNotEmpty(bctaId);

        return bcTaskSV.getLastCycleAmQuestionDetail(bctaId);
    }

    @RequestMapping(value = "/getBcTaskList", method = RequestMethod.POST)
    @ApiOperation(value = "分页查询当前用户的支部名片任务列表", notes = "分页查询当前用户的支部名片任务列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataDate", value = "工作周期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bcTitle", value = "任务名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bctState", value = "状态（ 0:待处理，1：已保存，2待初评，3：待终评，4：已完成。）", required = false, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页记录数", required = true, dataType = "Integer", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<BcTaskListDTO> getBcTaskList(String dataDate, String bcTitle, Integer bctState, Integer page, Integer limit) throws GeneralException {
        ValidateUtil.isNotNull(page, limit);
        Users currentUser = getUser();

        return bcTaskSV.getBcTaskList(currentUser.getCurrentRoleOrg().getId(), dataDate, bcTitle, bctState, page, limit);
    }

    @RequestMapping(value = "/getBcTaskDetail", method = RequestMethod.POST)
    @ApiOperation(value = "支部名片任务详情", notes = "支部名片任务详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "bctId", value = "业务主键（任务id）", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public BcTaskDetailDTO getBcTaskDetail(String bctId) throws GeneralException {
        ValidateUtil.isNotEmpty(bctId);

        return bcTaskSV.getBcTaskDetail(bctId);
    }

    @RequestMapping(value = "/getBcTaskQuestionList", method = RequestMethod.POST)
    @ApiOperation(value = "任务答案数据列表", notes = "任务答案数据列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "bctId", value = "业务主键（任务id）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bctaState", value = "业务主键（任务id）", required = false, dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "taskType", value = "业务主键（任务id）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页记录数", required = true, dataType = "Integer", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<BcTaskQuestionListDTO> getBcTaskQuestionList(String bctId, Integer bctaState, String taskType, Integer page, Integer limit) throws GeneralException {
        ValidateUtil.isNotEmpty(bctId);
        ValidateUtil.isNotNull(page, limit);

        return bcTaskSV.getBcTaskQuestionList(bctId, bctaState, taskType, page, limit);
    }

    @RequestMapping(value = "/getBcTaskQuestionDetail", method = RequestMethod.POST)
    @ApiOperation(value = "任务答案详情", notes = "任务答案详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "bctaId", value = "任务答案主键", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public BcTaskQuestionDetailDTO getBcTaskQuestionDetail(String bctaId) throws GeneralException {
        ValidateUtil.isNotEmpty(bctaId);

        return bcTaskSV.getBcTaskQuestionDetail(bctaId);
    }

    @RequestMapping(value = "/getAuditOrExpertBcBranchCardInfoDetail", method = RequestMethod.POST)
    @ApiOperation(value = "查询考核任务初评或者终评详情", notes = "查询考核任务初评或者终评详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "bcqId", value = "题目id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bctType", value = "0：全部 1：初评 2：终评", required = true, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public BcAuditOrExpertBcBranchCardInfoDetailDTO getAuditOrExpertBcTaskDetail(String bcqId, Integer bctType) throws GeneralException {
        ValidateUtil.isNotEmpty(bcqId);
        ValidateUtil.isNotNull(bctType);
        return bcTaskSV.getAuditOrExpertBcTaskDetail(bcqId, bctType);
    }

    @RequestMapping(value = "/getBcTaskFinScoreList", method = RequestMethod.POST)
    @ApiOperation(value = "查询堡垒指数排名表", notes = "查询堡垒指数排名表")
    @ApiImplicitParams({@ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "行数", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "bcId", value = "支部名片id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "所属组织", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<BcTaskFinScoreListDTO> getBcTaskFinScoreList(Integer page, Integer limit, String bcId, String orgId) throws GeneralException {
        ValidateUtil.isNotNull(page, limit);
        ValidateUtil.isNotEmpty(bcId);
        return bcTaskSV.getBcTaskFinScoreList(page, limit, bcId, orgId);
    }

    @RequestMapping(value = "/exportBcTaskFinScoreList", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出堡垒指数排名表", notes = "导出堡垒指数排名表")
    @ApiImplicitParams({@ApiImplicitParam(name = "bcId", value = "支部名片id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "所属组织", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportBcTaskFinScoreList(String bcId, String orgId, HttpServletResponse response) throws GeneralException {
        ValidateUtil.isNotEmpty(bcId);

        List<BcTaskFinScoreListDTO> bcTaskFinScoreList = bcTaskSV.getAllBcTaskFinScoreList(bcId, orgId);

        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFCellStyle cellStyle1 = workbook.createCellStyle();
        cellStyle1.setAlignment(HorizontalAlignment.CENTER);
        cellStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle1.setBorderBottom(BorderStyle.THIN);
        cellStyle1.setBorderLeft(BorderStyle.THIN);
        cellStyle1.setBorderRight(BorderStyle.THIN);
        cellStyle1.setBorderTop(BorderStyle.THIN);

        XSSFCellStyle cellStyle2 = workbook.createCellStyle();
        cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle2.setBorderBottom(BorderStyle.THIN);
        cellStyle2.setBorderLeft(BorderStyle.THIN);
        cellStyle2.setBorderRight(BorderStyle.THIN);
        cellStyle2.setBorderTop(BorderStyle.THIN);

        // 自动换行样式
        XSSFCellStyle cellStyle3;

        // sheet1
        XSSFSheet sheet = workbook.createSheet("积分排名统计表");
        //设置表格头
        // 生成表格标题行
        String[] headers1 = {"组织名称", "省份", "本期堡垒指数", "上期堡垒指数", "本期分公司内排名", "本期全网排名", "失分项"};

        XSSFRow row = sheet.createRow(0);
        for (short i = 0; i < headers1.length; i++) {
            XSSFCell cell = row.createCell(i);
            cell.setCellStyle(cellStyle1);
            cell.setCellValue(headers1[i]);
        }

        // 设置列宽
        sheet.setDefaultColumnWidth(20);
        sheet.setColumnWidth(0, 10 * 1204);
        sheet.setColumnWidth(6, 30 * 1204);

        for (int i = 0; i < bcTaskFinScoreList.size(); i++) {
            BcTaskFinScoreListDTO bcTaskFinScoreListDTO = bcTaskFinScoreList.get(i);
            row = sheet.createRow(i + 1);
            Cell cell = row.createCell(0);
            cell.setCellStyle(cellStyle2);
            cell.setCellValue(bcTaskFinScoreListDTO.getOrgName());
            cell = row.createCell(1);
            cell.setCellStyle(cellStyle2);
            cell.setCellValue(bcTaskFinScoreListDTO.getProvinceStr());
            cell = row.createCell(2);
            cell.setCellStyle(cellStyle2);
            cell.setCellValue(divide_0(bcTaskFinScoreListDTO.getFinScore(), 100));
            Cell cell2 = row.createCell(3);
            cell2.setCellStyle(cellStyle2);
//            cell2.setCellStyle(textStyle);//设置单元格格式为"文本"
            cell2.setCellType(CellType.STRING);
            cell2.setCellValue(null == bcTaskFinScoreListDTO.getFinScoreComparison() ? "--" : divide_0(bcTaskFinScoreListDTO.getFinScoreComparison(), 100));
            cell = row.createCell(4);
            cell.setCellStyle(cellStyle2);
            cell.setCellType(CellType.STRING);
            if (null == bcTaskFinScoreListDTO.getvProvinceComparison()) {
                cell.setCellValue("--");
            } else {
                if (bcTaskFinScoreListDTO.getvProvinceComparison() < 0)
                    cell.setCellValue("" + bcTaskFinScoreListDTO.getvProvinceRanking_num() + "（环比上升" + bcTaskFinScoreListDTO.getvProvinceComparison() + "名）");
                else
                    cell.setCellValue("" + bcTaskFinScoreListDTO.getvProvinceRanking_num() + "（环比下降" + Math.abs(bcTaskFinScoreListDTO.getvProvinceComparison()) + "名）");
            }

            cell = row.createCell(5);
            cell.setCellStyle(cellStyle2);
            cell.setCellType(CellType.STRING);
            if (null == bcTaskFinScoreListDTO.getvComparison()) {
                cell.setCellValue("--");
            } else {
                if (bcTaskFinScoreListDTO.getvComparison() < 0)
                    cell.setCellValue("" + bcTaskFinScoreListDTO.getvRankingNum() + "（环比上升" + bcTaskFinScoreListDTO.getvComparison() + "名）");
                else
                    cell.setCellValue("" + bcTaskFinScoreListDTO.getvRankingNum() + "（环比下降" + Math.abs(bcTaskFinScoreListDTO.getvComparison()) + "名）");
            }

            cell = row.createCell(6);
            cellStyle3 = cellStyle2;
            cellStyle3.setWrapText(true);
            cell.setCellStyle(cellStyle3);
            cell.setCellValue(bcTaskFinScoreListDTO.getMissedItem());
        }

        String fileName = "堡垒指数全网排名表";
        try {
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=".concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            throw new SystemFailureException("导出报表失败");
        }
    }

    private String divide_0(Integer src, Integer divideBy) {
        DecimalFormat df = new DecimalFormat("#.##");
        return df.format(Double.valueOf(src) / Double.valueOf(divideBy));
    }

}
