package com.cmos.pbms.web.controller.bc;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONValidator;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.bc.BcQuestion;
import com.cmos.pbms.beans.bc.BcTaskAnswer;
import com.cmos.pbms.beans.dto.AuditMeetingListDTO;
import com.cmos.pbms.beans.enums.ProcessTypeEnum;
import com.cmos.pbms.beans.enums.WorkTaskTypeEnum;
import com.cmos.pbms.beans.sys.ReviewLog;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.iservice.bc.IBcQuestionSV;
import com.cmos.pbms.iservice.bc.IBcTaskAnswerSV;
import com.cmos.pbms.iservice.common.IWorkTaskSV;
import com.cmos.pbms.iservice.me.IMeetingsSV;
import com.cmos.pbms.iservice.sys.IReviewLogSV;
import com.cmos.pbms.utils.UIDUtil;
import com.cmos.pbms.utils.ValidateUtil;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@RequestMapping(value = "/bcTaskAnswer")
@Validated
@Api(description = "支部名片答案控制器")
public class BcTaskAnswerController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(BcTaskAnswerController.class);

    @Reference(group = "pbms")
    private IBcTaskAnswerSV bcTaskAnswerSV;
    @Reference(group = "pbms")
    private IReviewLogSV reviewLogSV;
    @Reference(group = "pbms")
    private IWorkTaskSV workTaskSV;
    @Reference(group = "pbms")
    private IMeetingsSV meetingsSV;
    @Reference(group = "pbms")
    private IBcQuestionSV bcQuestionSV;

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "新增支部名片答案明细", notes = "新增支部名片答案明细")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "答案明细id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bctaId", value = "答案id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "contentType", value = "答案明细内容类型", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "answer", value = "文本答案", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "docName", value = "附件名字", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "docUrl", value = "附件地址", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "objName", value = "业务名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "objType", value = "业务类型", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "objId", value = "业务主键", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isSummit", value = "0：保存 1：提交", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "bctaId", value = "答案id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "subContent", value = "填报备注", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "fileDescParam", value = "附件名称参数", required = false, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Integer save(String requestBody, String bctaId, String subContent, Integer isSummit, String fileDescParam) throws GeneralException {
        ValidateUtil.isNotNull(isSummit);
        ValidateUtil.isNotEmpty(bctaId);
        if (JSONValidator.from(requestBody).getType() != JSONValidator.Type.Array)
            throw new GeneralException("PBMS_BC_0001");
        Date current = new Date();
        return bcTaskAnswerSV.save(JSONArray.parseArray(requestBody), bctaId, subContent, isSummit, getUser(), current, StringUtils.isNotBlank(fileDescParam) ? JSONArray.parseArray(fileDescParam) : null);
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ApiOperation(value = "删除支部名片答案明细", notes = "删除支部名片答案明细")
    @ApiImplicitParams({@ApiImplicitParam(name = "bctaiId", value = "答案明细id", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Integer delete(String bctaiId) throws GeneralException {
        ValidateUtil.isNotEmpty(bctaiId);

        Users currentUser = getUser();
        Date current = new Date();

        return bcTaskAnswerSV.delete(bctaiId, currentUser.getId(), current);
    }

    @RequestMapping(value = "/saveAuditOrExpert", method = RequestMethod.POST)
    @ApiOperation(value = "稽核和专家打分接口", notes = "稽核和专家打分接口")
    @ApiImplicitParams({@ApiImplicitParam(name = "bctaId", value = "答案明细id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "score", value = "分值", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "stainContent", value = "扣分原因", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "brightContent", value = "亮点", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "auditOrExpert", value = "稽核还是专家打分 1：稽核 2：专家打分", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "saveOrCommit", value = "稽核还是专家打分 1：保存 2：提交", required = true, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Integer saveAuditOrExpert(String bctaId, Integer score, String stainContent, String brightContent, Integer auditOrExpert, Integer saveOrCommit) throws GeneralException {
        ValidateUtil.isNotEmpty(bctaId);
        ValidateUtil.isNotNull(score, auditOrExpert, saveOrCommit);

        Users currentUser = getUser();
        Date current = new Date();

        return bcTaskAnswerSV.saveAuditOrExpert(bctaId, score, stainContent, brightContent, auditOrExpert, saveOrCommit, currentUser.getId(), currentUser.getUsername(), current);
    }

    @RequestMapping(value = "/auditBranchAnswer", method = RequestMethod.POST)
    @ApiOperation(value = "六好支部-中台稽核", notes = "六好支部-中台稽核")
    @ApiResponses({
            @ApiResponse(code = 0, message = "稽核成功", response = Integer.class),
            @ApiResponse(code = 999, message = "稽核失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bctaId", value = "支部名片任务答案id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "auditState", value = "0:保存 2：不通过 3：通过", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "auditReason", value = "不通过的原因", required = false, dataType = "String", paramType = "query")})
    public Integer auditBranchAnswer(String bctaId, Integer auditState, String auditReason) throws GeneralException {
        Users user = getUser();
        ValidateUtil.isNotNull(auditReason);
        ValidateUtil.isNotEmpty(bctaId);

        // 查询支部名片任务答案
        BcTaskAnswer taskAnswer = bcTaskAnswerSV.getByPrimaryKey(bctaId);
        if (null == taskAnswer) {
            logger.error("参数信息有误：未查询到答案信息（ID=‘" + bctaId + "’");
            throw new GeneralException("PBMS_COM_1001");
        }
        // 查询题目
        BcQuestion bcQuestion = bcQuestionSV.getByPrimaryKey(taskAnswer.getBcqId());

        Date sysDate = new Date();
        taskAnswer.setModifiedby(user.getId());
        taskAnswer.setModifieddate(sysDate);
        if (StringUtils.isBlank(auditReason)) {
            taskAnswer.setAuditStainContent("");
        } else {
            taskAnswer.setAuditStainContent(auditReason);
        }

        // 如果当前操作为保存，则只对审核信息进行保存，不做其他操作
        if (auditState == 0) {
            return bcTaskAnswerSV.updateByPrimaryKeySelective(taskAnswer);
        }

        ReviewLog reviewLog = new ReviewLog();
        reviewLog.setId(UIDUtil.getUID());
        reviewLog.setProcessType(ProcessTypeEnum.BC_AUDIT.getCode());
        reviewLog.setObjectId(taskAnswer.getId());
        // 6001( 1：提交稽核 2：稽核不通过 3：稽核通过)
        reviewLog.setOperationType(auditState);
        reviewLog.setOperationDesc(2 == auditState ? "稽核不通过" : "稽核通过");
        reviewLog.setOperationUserId(user.getId());
        reviewLog.setOperationUserName(user.getUsername());
        reviewLog.setOperationTime(sysDate);
        reviewLog.setResuseReason(auditReason);
        reviewLog.setRemark("中台" + user.getUsername() + "已" + reviewLog.getOperationDesc());
        reviewLogSV.insertSelective(reviewLog);
        // 3：稽核保存 2：待稽核 7：稽核未通过 6：稽核通过
        if (auditState == 2) {
            taskAnswer.setBctaState(7);
        } else {
            taskAnswer.setBctaState(6);
        }
        // 更新稽核人和时间
        taskAnswer.setAuditUserId(user.getId());
        taskAnswer.setAuditDate(new Date());
        if (3 == auditState) {
            // 稽核通过
            bcTaskAnswerSV.updateByPrimaryKeySelective(taskAnswer);
            // 更新待办
            return workTaskSV.doneByObjidAndTypeAndUserId(bctaId, WorkTaskTypeEnum.BC_AUDIT.getCode(), Integer.valueOf(1), null, WorkTaskTypeEnum.BC_AUDIT.getCode(), user.getId(), sysDate);
        } else if (2 == auditState) {
            // 稽核不通过
            bcTaskAnswerSV.updateByPrimaryKeySelective(taskAnswer);
            // 短信提醒
            meetingsSV.sendBackAuditMsg(getUser(), bctaId, bcQuestion.getqTitle(), 172, ProcessTypeEnum.BC_AUDIT.getCode(), taskAnswer.getSubUserId());
            // 更新待办
            return workTaskSV.doneByObjidAndTypeAndUserId(bctaId, WorkTaskTypeEnum.BC_AUDIT.getCode(), Integer.valueOf(1), null, WorkTaskTypeEnum.BC_AUDIT.getCode(), user.getId(), sysDate);
        } else {
            logger.error("稽核状态异常，请联系管理员");
            throw new GeneralException("PBMS_ME_1065");
        }
    }

    @RequestMapping(value = "/getAuditTaskAnswerList", method = RequestMethod.POST)
    @ApiOperation(value = "查询支部名片稽核列表", notes = "查询支部名片稽核列表")
    @ApiResponses({
            @ApiResponse(code = 0, message = "查询成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "查询失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "qType", value = "六好类型(基础工作好-lhzb_jcgzh，作用发挥好-lhzb_zyfhh)", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "topic", value = "题目标题", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "auditState", value = "稽核状态(2：待稽核 7：稽核未通过 6：稽核通过 9：已超期)", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "所属组织id", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "auditTodoTimeStart", value = "提交稽核时间开始", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "auditTodoTimeEnd", value = "提交稽核时间结束", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "auditExampleTimeStart", value = "稽核审核时间开始", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "auditExampleTimeEnd", value = "稽核审核时间结束", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数量", required = true, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<AuditMeetingListDTO> getAuditMeetingList(String qType, String topic, Integer auditState, String orgId, Date auditTodoTimeStart, Date auditTodoTimeEnd, Date auditExampleTimeStart, Date auditExampleTimeEnd, Integer page, Integer limit) throws GeneralException {

        ValidateUtil.isNotNull(page, limit);
        Users currentUser = getUser();

        return bcTaskAnswerSV.getAuditTaskAnswerList(currentUser.getId(), currentUser.getCurrentRoleId(), qType, topic, auditState, orgId, auditTodoTimeStart, auditTodoTimeEnd, auditExampleTimeStart, auditExampleTimeEnd, page, limit);
    }
}
