package com.cmos.pbms.web.utils.Yzy;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Formatter;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
@Slf4j
public class YzyMessageClient {
    private static final String SEND_PATH = "/msgc/open-api/msg/sendYZY";

    @Autowired
    private TspConfig config;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final CloseableHttpClient httpClient = HttpClients.createDefault();

    public SendYzyMessageResponse sendMessage(SendYzyMessageRequest req) throws Exception {
        if (CollectionUtils.isEmpty(req.getReceivers())){
            log.info("消息接受人为空");
            SendYzyMessageResponse sendYzyMessageResponse = new SendYzyMessageResponse();
            sendYzyMessageResponse.setSuccess(Boolean.TRUE);
            sendYzyMessageResponse.setMessage("没有接受人");
            return sendYzyMessageResponse;
        }
        //去一下重 req
        req.setReceivers(req.getReceivers().stream().distinct().collect(Collectors.toList()));
        long timestamp = System.currentTimeMillis() / 1000;
        String nonce = UUID.randomUUID().toString();
        String signature = generateSignature(timestamp, nonce, config.getPaasToken());

        String url = config.getBaseUrl() + SEND_PATH;
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-Type", "application/json");
        post.setHeader("x-tsp-paasid", config.getPaasId());
        post.setHeader("x-tsp-timestamp", String.valueOf(timestamp));
        post.setHeader("x-tsp-nonce", nonce);
        post.setHeader("x-tsp-signature", signature);

        String jsonBody = objectMapper.writeValueAsString(req);
        post.setEntity(new StringEntity(jsonBody, StandardCharsets.UTF_8));

        // 打印请求详情
        log.info("发送粤政易消息请求 URL: {}", url);
        log.info("请求头信息:");
        for (Header header : post.getAllHeaders()) {
            log.info("    {} : {}", header.getName(), header.getValue());
        }
        log.info("请求体: {}", jsonBody);

        try (CloseableHttpResponse resp = httpClient.execute(post)) {
            // 获取并打印原始响应
            HttpEntity entity = resp.getEntity();
            String responseString = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            log.info("原始响应状态码: {}", resp.getStatusLine().getStatusCode());
            log.info("原始响应内容: {}", responseString);

            // 将响应字符串转换为对象并返回
            return objectMapper.readValue(responseString, SendYzyMessageResponse.class);
        }
    }

    private String generateSignature(long timestamp, String nonce, String paasToken) throws Exception {
        String raw = timestamp + paasToken + nonce + timestamp;
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] digest = md.digest(raw.getBytes(StandardCharsets.UTF_8));
        try (Formatter fmt = new Formatter()) {
            for (byte b : digest) {
                fmt.format("%02X", b);
            }
            return fmt.toString();
        }
    }
}
