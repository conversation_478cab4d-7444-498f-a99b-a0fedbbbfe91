package com.cmos.pbms.web.controller.bp;

import com.alibaba.dubbo.config.annotation.Reference;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.spring.AppEnv;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.bp.BpCulturalConstruction;
import com.cmos.pbms.beans.dto.BpCulturalConstructionBannerDTO;
import com.cmos.pbms.beans.dto.BpCulturalConstructionDetailDTO;
import com.cmos.pbms.beans.dto.BpCulturalConstructionListDTO;
import com.cmos.pbms.beans.dto.BpCulturalConstructionWebListDTO;
import com.cmos.pbms.beans.pm.Organization;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.iservice.bp.IBpCulturalConstructionSV;
import com.cmos.pbms.iservice.common.IAttachmentsSV;
import com.cmos.pbms.iservice.pm.IOrganizationSV;
import com.cmos.pbms.utils.OnestUtil;
import com.cmos.pbms.utils.UIDUtil;
import com.cmos.pbms.utils.ValidateUtil;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 支部阵地-文化建设
 *
 * <AUTHOR>
 * @date 2020-10-30
 */
@RestController
@RequestMapping(value = "/culturalConstruction")
@Validated
@Api("支部阵地-文化建设操作控制器")
public class BpCulturalConstructionController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(BpBranchWarsongController.class);

    @Reference(group = "pbms")
    private IBpCulturalConstructionSV bpCulturalConstructionSV;
    @Reference(group = "pbms")
    private IAttachmentsSV attachmentsSV;
    @Reference(group = "pbms")
    private IOrganizationSV organizationSV;
    private static final String contextPath = AppEnv.getString("server.context-path");

    /**
     * 列表
     * 图片列表
     * 查询单个
     * 新增/保存
     */

    @RequestMapping(value = "/getCulturalBannerByOrgId", method = RequestMethod.POST)
    @ApiOperation(value = "查询图片列表（APP首页使用）", notes = "查询图片列表（APP首页使用）")
    @ApiImplicitParams({@ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "支部ID", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<BpCulturalConstructionBannerDTO> getCulturalBannerByOrgId(Integer page, Integer limit, String orgId) throws GeneralException {
        ValidateUtil.isNotNull(page, limit, orgId);
        return bpCulturalConstructionSV.getCulturalBannerByOrgId(page, limit, orgId);
    }

    @RequestMapping(value = "/getCulturalListByOrgId", method = RequestMethod.POST)
    @ApiOperation(value = "查询列表(APP)", notes = "查询列表(APP)")
    @ApiImplicitParams({@ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "支部ID", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<BpCulturalConstructionListDTO> getCulturalListByOrgId(Integer page, Integer limit, String orgId) throws GeneralException {
        ValidateUtil.isNotNull(page, limit, orgId);
        return bpCulturalConstructionSV.getCulturalListByOrgId(page, limit, orgId);
    }

    @RequestMapping(value = "/saveCultural", method = RequestMethod.POST)
    @ApiOperation(value = "新增/保存文化建设（APP，已停用）", notes = "新增/保存文化建设（APP，已停用）")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "支部ID", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "culturalTitle", value = "文化建设名称", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "culturalDescription", value = "文化建设描述", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orderNo", value = "排序号", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "isSubmit", value = "是否提交（0-保存，1-提交）", required = true, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int saveCultural(String id, String orgId, String culturalTitle, String culturalDescription, Long orderNo, Integer isSubmit) throws GeneralException {
        // 参数校验
        ValidateUtil.isNotNull(id, orgId, culturalTitle, isSubmit);

        Users currUser = getUser();
        Date currTime = new Date();

        BpCulturalConstruction bpCulturalConstruction = bpCulturalConstructionSV.getByPrimaryKey(id);
        if (null == bpCulturalConstruction) {
            // 新增
            bpCulturalConstruction = new BpCulturalConstruction();
            bpCulturalConstruction.setId(id);
            bpCulturalConstruction.setOrgId(orgId);
            bpCulturalConstruction.setCulturalTitle(culturalTitle);
            bpCulturalConstruction.setCulturalDescription(culturalDescription);
            bpCulturalConstruction.setOrderNo(orderNo);
            bpCulturalConstruction.setDataStatus(isSubmit);
            bpCulturalConstruction.setEnableStatus(1);
            bpCulturalConstruction.setIsdeleted(0);
            bpCulturalConstruction.setCreatedby(currUser.getId());
            bpCulturalConstruction.setCreateddate(currTime);
            return bpCulturalConstructionSV.insertSelective(bpCulturalConstruction);
        } else {
            // 更新
            bpCulturalConstruction.setCulturalTitle(culturalTitle);
            bpCulturalConstruction.setCulturalDescription(culturalDescription);
            bpCulturalConstruction.setOrderNo(orderNo);
            bpCulturalConstruction.setDataStatus(isSubmit);
            bpCulturalConstruction.setModifiedby(currUser.getId());
            bpCulturalConstruction.setModifieddate(currTime);
            return bpCulturalConstructionSV.updateByPrimaryKey(bpCulturalConstruction);
        }
    }

    @RequestMapping(value = "/delBpCulturalConstruction", method = RequestMethod.POST)
    @ApiOperation(value = "删除文化建设", notes = "删除文化建设",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "文化建设ID", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int delBpCulturalConstruction(String id) throws GeneralException {
        ValidateUtil.isNotNull(id);

        BpCulturalConstruction bpCulturalConstruction = new BpCulturalConstruction();
        bpCulturalConstruction.setId(id);
        bpCulturalConstruction.setIsdeleted(1);
        bpCulturalConstruction.setModifiedby(getUser().getId());
        bpCulturalConstruction.setModifieddate(new Date());
        return bpCulturalConstructionSV.updateByPrimaryKeySelective(bpCulturalConstruction);
    }

    @RequestMapping(value = "/getCulturalList", method = RequestMethod.POST)
    @ApiOperation(value = "查询列表(WEB)", notes = "查询列表(WEB)")
    @ApiImplicitParams({@ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "culturalTitle", value = "标题", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "查询时间-开始", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "查询时间-截止", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "dataStatus", value = "状态（0-草稿，1-已发布）", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "createdby", value = "创建人", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "makeFrom", value = "出处", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "所属党组织", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<BpCulturalConstructionWebListDTO> getCulturalList(Integer page, Integer limit, String culturalTitle, Date startDate, Date endDate, Integer dataStatus, String createdby, String makeFrom, String orgId, String orgCode) throws GeneralException {
        ValidateUtil.isNotNull(page, limit);
        return bpCulturalConstructionSV.getCulturalList(page, limit, culturalTitle, startDate, endDate, dataStatus, createdby, makeFrom, orgId, orgCode);
    }

    @RequestMapping(value = "/getCulturalWebById", method = RequestMethod.POST)
    @ApiOperation(value = "查询文化建设详情（WEB，APP，该接口还用于文化建设分享）", notes = "查询文化建设详情（WEB， APP，该接口还用于文化建设分享）")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public BpCulturalConstructionDetailDTO getCulturalWebById(String id) throws GeneralException {
        ValidateUtil.isNotNull(id);
        Users user = getUser();
        BpCulturalConstructionDetailDTO result = bpCulturalConstructionSV.getCulturalWebDetail(id, null == user ? null : user.getId());

        if (ValidateUtil.isValid(result)) {
            return null;
        }
        bpCulturalConstructionSV.addReadCount(result.getId());

        //获取新闻内容
        if (StringUtils.isNotBlank(result.getCcContent())) {
            String ccContent = OnestUtil.getContent(result.getCcContent());
            if (StringUtils.isNotBlank(ccContent)) {
                ccContent = ccContent.replaceAll("src=\"/webrdp-web/systemQuery.do", "src=\"" + contextPath + "/webrdp-web/systemQuery.do");
            }
            result.setCcContent(ccContent);
        }
        return result;
    }

    @RequestMapping(value = "/saveCulturalWeb", method = RequestMethod.POST)
    @ApiOperation(value = "新增/保存文化建设（WEB）", notes = "新增/保存文化建设（WEB）")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "ccContentWithHtml", value = "正文内容", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "culturalTitle", value = "文化建设名称", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "makeFrom", value = "出处", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "layoutStyle", value = "布局样式（01-无图；02-上下布局；03-左右布局）", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "enablereview", value = "是否允许评论（1：允许；0：不允许）", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "enablethumbs", value = "是否允许点赞（1：允许；0：不允许）", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "themepic", value = "主题图", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "isSubmit", value = "是否提交（0-保存，1-提交）", required = true, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int saveCulturalWeb(String id, String ccContentWithHtml, String culturalTitle, String makeFrom, Integer layoutStyle, Integer enablereview, Integer enablethumbs, String themepic, Integer isSubmit) throws GeneralException {
        // 参数校验
        ValidateUtil.isNotNull(id, ccContentWithHtml, culturalTitle, makeFrom, layoutStyle, enablereview, enablethumbs, isSubmit);

        Users currUser = getUser();
        Date currTime = new Date();
        Organization branch = organizationSV.getBranchByOrg(currUser.getOrgid());
        if (null == branch) {
            logger.error("当前用户不归属于任何党支部,不能创建支部文化建设");
            throw new GeneralException("PBMS_BP_0004");
        }

        String url = OnestUtil.storeByStream(ccContentWithHtml, UIDUtil.getUID() + ".html", "ccContent", currUser.getProvince(), currUser.getOrgid());
        String contentUrl = OnestUtil.subStringUrl(url);

        BpCulturalConstruction bpCulturalConstruction = bpCulturalConstructionSV.getByPrimaryKey(id);
        if (null == bpCulturalConstruction) {
            // 新增
            bpCulturalConstruction = new BpCulturalConstruction();
            bpCulturalConstruction.setId(id);
            bpCulturalConstruction.setOrgId(branch.getId());
            bpCulturalConstruction.setCulturalTitle(culturalTitle);
            bpCulturalConstruction.setCcContent(contentUrl);
            bpCulturalConstruction.setOrderNo(currTime.getTime());
            bpCulturalConstruction.setMakeFrom(makeFrom);
            bpCulturalConstruction.setLayoutStyle(layoutStyle);
            bpCulturalConstruction.setEnablereview(enablereview);
            bpCulturalConstruction.setEnablethumbs(enablethumbs);
            bpCulturalConstruction.setThemepic(themepic);

            if (Integer.valueOf(1).equals(isSubmit)) {
                bpCulturalConstruction.setSubmitUser(currUser.getId());
                bpCulturalConstruction.setPushtime(currTime);
            }

            bpCulturalConstruction.setDataStatus(isSubmit);
            bpCulturalConstruction.setEnableStatus(1);
            bpCulturalConstruction.setIsdeleted(0);
            bpCulturalConstruction.setCreatedby(currUser.getId());
            bpCulturalConstruction.setCreateddate(currTime);
            return bpCulturalConstructionSV.insertSelective(bpCulturalConstruction);
        } else {
            // 更新
            bpCulturalConstruction.setCulturalTitle(culturalTitle);
            bpCulturalConstruction.setCcContent(contentUrl);
            bpCulturalConstruction.setOrderNo(currTime.getTime());
            bpCulturalConstruction.setMakeFrom(makeFrom);
            bpCulturalConstruction.setLayoutStyle(layoutStyle);
            bpCulturalConstruction.setEnablereview(enablereview);
            bpCulturalConstruction.setEnablethumbs(enablethumbs);
            bpCulturalConstruction.setThemepic(themepic);

            if (Integer.valueOf(1).equals(isSubmit)) {
                bpCulturalConstruction.setSubmitUser(currUser.getId());
                bpCulturalConstruction.setPushtime(currTime);
            } else {
                bpCulturalConstruction.setSubmitUser(null);
                bpCulturalConstruction.setPushtime(null);
            }

            bpCulturalConstruction.setDataStatus(isSubmit);
            bpCulturalConstruction.setModifiedby(currUser.getId());
            bpCulturalConstruction.setModifieddate(currTime);
            return bpCulturalConstructionSV.updateByPrimaryKey(bpCulturalConstruction);
        }
    }
}
