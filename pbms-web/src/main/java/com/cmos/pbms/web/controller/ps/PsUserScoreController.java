package com.cmos.pbms.web.controller.ps;

import com.alibaba.dubbo.config.annotation.Reference;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.dto.PsPoineerScoreDTO;
import com.cmos.pbms.beans.dto.PsPoineerSocreListDTO;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.iservice.ps.IPsItemInfoSV;
import com.cmos.pbms.iservice.ps.IPsPoineerScoreInfoSV;
import com.cmos.pbms.iservice.ps.IPsUserScoreSV;
import com.cmos.pbms.utils.CacheServiceUtil;
import com.cmos.pbms.utils.DateUtil;
import com.cmos.pbms.utils.ValidateUtil;
import com.cmos.pbms.utils.constants.PbmsConstants;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@RequestMapping("/psUserScore")
@Validated
@Api(description = "先锋指数-用户得分信息操作控制器")
public class PsUserScoreController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(PsUserScoreController.class);

    @Reference(group = "pbms")
    private IPsItemInfoSV psItemInfoSV;
    @Reference(group = "pbms")
    private IPsPoineerScoreInfoSV psPoineerScoreInfoSV;
    @Reference(group = "pbms")
    private IPsUserScoreSV psUserScoreSV;

    @RequestMapping(value = "/getPoineerScore", method = RequestMethod.POST)
    @ApiOperation(value = "查询先锋指数详情", notes = "查询先锋指数详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "dataDate", value = "查询日期（yyyyMMdd）", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PsPoineerScoreDTO getPoineerScore(String userId, Integer dataDate) throws GeneralException {
        ValidateUtil.isNotNull(userId);
        if(null == dataDate){
            dataDate = Integer.valueOf(DateUtil.format(new Date(), "yyyyMMdd"));
        }
        return psUserScoreSV.getPoineerScore(userId, dataDate);
    }

    @RequestMapping(value = "/getPoineerScoreDetail", method = RequestMethod.POST)
    @ApiOperation(value = "查询先锋指数明细", notes = "查询先锋指数明细")
    @ApiImplicitParams({@ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "dataDate", value = "查询日期（yyyyMMdd）", required = false, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PsPoineerScoreDTO getPoineerScoreDetail(String userId, Integer dataDate) throws GeneralException {
        ValidateUtil.isNotNull(userId);
        if(null == dataDate){
            dataDate = Integer.valueOf(DateUtil.format(new Date(), "yyyyMMdd"));
        }
        return psUserScoreSV.getPoineerScoreDetail(userId, dataDate);
    }

    @RequestMapping(value = "/getPoineerScoreList", method = RequestMethod.POST)
    @ApiOperation(value = "查询往期先锋指数", notes = "查询先锋指数明细")
    @ApiImplicitParams({@ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<PsPoineerSocreListDTO> getPoineerScoreList(Integer page, Integer limit, String userId) throws GeneralException {
        ValidateUtil.isNotNull(userId);
        return psUserScoreSV.getPoineerScoreList(page, limit, userId);
    }

    @RequestMapping(value = "/calUserScore", method = RequestMethod.POST)
    @ApiOperation(value = "更新个人先锋指数", notes = "更新个人先锋指数")
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int calUserScore() throws GeneralException {
        Users currUser = getUser();
        String userId = currUser.getId();

        String key = PbmsConstants.POINEER_SCORE_PRE.concat(userId);
        String updateStatus = CacheServiceUtil.getService().getString(key);
        if (StringUtils.isBlank(updateStatus) || "000".equals(updateStatus)) {
            CacheServiceUtil.getService().setString(key, "111");
            CacheServiceUtil.getService().expire(key, 10 * 60);

            return psPoineerScoreInfoSV.calUserPoineerScore(userId, new Date());
        }
        logger.error("两次刷新需要间隔十分钟，请稍后再试。");
        throw new GeneralException("PBMS_PS_0002");
    }
}
