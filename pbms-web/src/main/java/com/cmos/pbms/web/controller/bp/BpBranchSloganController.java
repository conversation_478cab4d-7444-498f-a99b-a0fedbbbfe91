package com.cmos.pbms.web.controller.bp;

import com.alibaba.dubbo.config.annotation.Reference;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.bp.BpBranchSlogan;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.iservice.bp.IBpBranchSloganSV;
import com.cmos.pbms.utils.ValidateUtil;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 支部阵地-支部口号
 *
 * <AUTHOR>
 * @date 2020-10-30
 */
@RestController
@RequestMapping(value = "/branchSlogan")
@Validated
@Api("支部阵地-支部口号操作控制器")
public class BpBranchSloganController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(BpBranchSloganController.class);

    @Reference(group = "pbms")
    private IBpBranchSloganSV bpBranchSloganSV;

    /**
     * 新增/保存
     * 查看
     */

    @RequestMapping(value = "/getSloganByOrgId", method = RequestMethod.POST)
    @ApiOperation(value = "查询支部口号", notes = "查询支部口号")
    @ApiImplicitParams({@ApiImplicitParam(name = "orgId", value = "支部ID", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public BpBranchSlogan getSloganByOrgId(String orgId) throws GeneralException {
        ValidateUtil.isNotNull(orgId);
        return bpBranchSloganSV.getByOrgId(orgId);
    }

    @RequestMapping(value = "/getSloganById", method = RequestMethod.POST)
    @ApiOperation(value = "查询支部口号", notes = "查询支部口号")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "口号ID", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public BpBranchSlogan getSloganById(String id) throws GeneralException {
        ValidateUtil.isNotNull(id);
        return bpBranchSloganSV.getByPrimaryKey(id);
    }

    @RequestMapping(value = "/saveSlogan", method = RequestMethod.POST)
    @ApiOperation(value = "新增/保存支部口号", notes = "新增/保存支部口号")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "支部ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "slogan", value = "文件ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "isSubmit", value = "是否提交（0-保存，1-提交）", required = true, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int saveSlogan(String id, String orgId, String slogan, Integer isSubmit) throws GeneralException {
        // 参数校验
        ValidateUtil.isNotNull(id, orgId, slogan, isSubmit);
        Users currUser = getUser();
        Date currTime = new Date();

        BpBranchSlogan bpBranchSlogan = bpBranchSloganSV.getByPrimaryKey(id);
        if(null == bpBranchSlogan){
            // 新增
            bpBranchSlogan = new BpBranchSlogan();
            bpBranchSlogan.setId(id);
            bpBranchSlogan.setOrgId(orgId);
            bpBranchSlogan.setSlogan(slogan);
            bpBranchSlogan.setDataStatus(isSubmit);
            bpBranchSlogan.setEnableStatus(1);
            bpBranchSlogan.setIsdeleted(0);
            bpBranchSlogan.setCreatedby(currUser.getId());
            bpBranchSlogan.setCreateddate(currTime);
            return bpBranchSloganSV.insertSelective(bpBranchSlogan);
        }else{
            // 更新
            bpBranchSlogan.setSlogan(slogan);
            bpBranchSlogan.setDataStatus(isSubmit);
            bpBranchSlogan.setModifiedby(currUser.getId());
            bpBranchSlogan.setModifieddate(currTime);
            return bpBranchSloganSV.updateByPrimaryKey(bpBranchSlogan);
        }
    }
}
