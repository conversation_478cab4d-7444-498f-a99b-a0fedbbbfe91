package com.cmos.pbms.web.controller.bp;

import com.alibaba.dubbo.config.annotation.Reference;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.bp.BpBranchHonor;
import com.cmos.pbms.beans.dto.BpBranchHonorBannerDTO;
import com.cmos.pbms.beans.dto.BpBranchHonorListDTO;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.iservice.bp.IBpBranchHonorSV;
import com.cmos.pbms.iservice.common.IAttachmentsSV;
import com.cmos.pbms.utils.ValidateUtil;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 支部阵地-支部荣誉
 *
 * <AUTHOR>
 * @date 2020-10-30
 */
@RestController
@RequestMapping(value = "/branchHonor")
@Validated
@Api("支部阵地-支部荣誉操作控制器")
public class BpBranchHonorController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(BpBranchWarsongController.class);

    @Reference(group = "pbms")
    private IBpBranchHonorSV bpBranchHonorSV;
    @Reference(group = "pbms")
    private IAttachmentsSV attachmentsSV;

    /**
     * 列表
     * 图片列表
     * 查询单个
     * 新增/保存
     */

    @RequestMapping(value = "/getHonorBannerByOrgId", method = RequestMethod.POST)
    @ApiOperation(value = "查询列表（轮播使用）", notes = "查询列表（轮播使用）")
    @ApiImplicitParams({@ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "支部ID", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<BpBranchHonorBannerDTO> getHonorBannerByOrgId(Integer page, Integer limit, String orgId) throws GeneralException {
        ValidateUtil.isNotNull(page, limit, orgId);
        return bpBranchHonorSV.getHonorBannerByOrgId(page, limit, orgId);
    }

    @RequestMapping(value = "/getHonorListByOrgId", method = RequestMethod.POST)
    @ApiOperation(value = "查询列表", notes = "查询列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数目", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "支部ID", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<BpBranchHonorListDTO> getHonorListByOrgId(Integer page, Integer limit, String orgId) throws GeneralException {
        ValidateUtil.isNotNull(page, limit, orgId);
        return bpBranchHonorSV.getHonorListByOrgId(page, limit, orgId);
    }

    @RequestMapping(value = "/getHonorById", method = RequestMethod.POST)
    @ApiOperation(value = "查询支部荣誉", notes = "查询支部荣誉")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public BpBranchHonor getHonorById(String id) throws GeneralException {
        ValidateUtil.isNotNull(id);
        BpBranchHonor result = bpBranchHonorSV.getByPrimaryKey(id);
        result.setFileList(attachmentsSV.getListByObjId(id));
        return result;
    }

    @RequestMapping(value = "/saveHonor", method = RequestMethod.POST)
    @ApiOperation(value = "新增/保存支部荣誉", notes = "新增/保存支部荣誉")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "支部ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "honorTitle", value = "荣誉名称", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "honorDescription", value = "荣誉描述", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orderNo", value = "排序号", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "isSubmit", value = "是否提交（0-保存，1-提交）", required = true, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int saveHonor(String id, String orgId, String honorTitle, String honorDescription, Integer orderNo, Integer isSubmit) throws GeneralException {
        // 参数校验
        ValidateUtil.isNotNull(id, orgId, honorTitle, isSubmit);
        Users currUser = getUser();
        Date currTime = new Date();

        BpBranchHonor bpBranchHonor = bpBranchHonorSV.getByPrimaryKey(id);
        if (null == bpBranchHonor) {
            // 新增
            bpBranchHonor = new BpBranchHonor();
            bpBranchHonor.setId(id);
            bpBranchHonor.setOrgId(orgId);
            bpBranchHonor.setHonorTitle(honorTitle);
            bpBranchHonor.setHonorDescription(honorDescription);
            bpBranchHonor.setOrderNo(orderNo);
            bpBranchHonor.setDataStatus(isSubmit);
            bpBranchHonor.setEnableStatus(1);
            bpBranchHonor.setIsdeleted(0);
            bpBranchHonor.setCreatedby(currUser.getId());
            bpBranchHonor.setCreateddate(currTime);
            return bpBranchHonorSV.insertSelective(bpBranchHonor);
        } else {
            // 更新
            bpBranchHonor.setHonorTitle(honorTitle);
            bpBranchHonor.setHonorDescription(honorDescription);
            bpBranchHonor.setOrderNo(orderNo);
            bpBranchHonor.setDataStatus(isSubmit);
            bpBranchHonor.setModifiedby(currUser.getId());
            bpBranchHonor.setModifieddate(currTime);
            return bpBranchHonorSV.updateByPrimaryKey(bpBranchHonor);
        }
    }

    @RequestMapping(value = "/delBpBranchHonor", method = RequestMethod.POST)
    @ApiOperation(value = "删除支部荣誉", notes = "删除支部荣誉",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "支部荣誉ID", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int delBpBranchHonor(String id) throws GeneralException {
        ValidateUtil.isNotNull(id);

        BpBranchHonor bpBranchHonor = new BpBranchHonor();
        bpBranchHonor.setId(id);
        bpBranchHonor.setIsdeleted(1);
        bpBranchHonor.setModifiedby(getUser().getId());
        bpBranchHonor.setModifieddate(new Date());
        return bpBranchHonorSV.updateByPrimaryKeySelective(bpBranchHonor);
    }
}
