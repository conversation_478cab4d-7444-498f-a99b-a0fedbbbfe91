package com.cmos.pbms.web.controller.rep;

import com.alibaba.dubbo.config.annotation.Reference;
import com.cmos.common.exception.GeneralException;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.dto.PartyStudTaskDTO;
import com.cmos.pbms.beans.dto.PartyStudyBranchDTO;
import com.cmos.pbms.beans.dto.PartyStudyCompanyDTO;
import com.cmos.pbms.beans.dto.PartyTodoTaskListDTO;
import com.cmos.pbms.iservice.rep.IPartyTaskSV;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Component
public class PartyStudyExportService {

    // 日志处理
    private static final Logger logger = LoggerFactory.getLogger(PartyStudyExportService.class);

    @Reference(group = "pbms")
    private IPartyTaskSV partyTaskSV;

    private String getTimeScope(Date startTime, Date endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        if (null != startTime) {
            if (null != endTime) {
                return sdf.format(startTime) + "-" + sdf.format(endTime);
            } else {
                return sdf.format(startTime) + "-";
            }
        } else {
            if (null != endTime) {
                return "-" + sdf.format(endTime);
            } else {
                return "-";
            }
        }
    }

    public void exportTodoTaskUserList(XSSFWorkbook wb, Date startTime, Date endTime, String userName, String telephones, String branchName, String companyName, String orgId) throws GeneralException {
        // 查询数据
        List<PartyTodoTaskListDTO> userList = partyTaskSV.selectTodoTaskUserList(startTime, endTime, userName, telephones, branchName, companyName, orgId);

        try {
            //创建一个SHEET页
            XSSFSheet sheet = wb.createSheet("党员学习待办明细表");

            XSSFCellStyle alignBoderStyle = wb.createCellStyle();
            alignBoderStyle.setAlignment(HorizontalAlignment.CENTER);
            alignBoderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            alignBoderStyle.setBorderBottom(BorderStyle.THIN);
            alignBoderStyle.setBorderLeft(BorderStyle.THIN);
            alignBoderStyle.setBorderRight(BorderStyle.THIN);
            alignBoderStyle.setBorderTop(BorderStyle.THIN);

            //设置表格头
            setTitleForExportUser(wb, alignBoderStyle, sheet, getTimeScope(startTime, endTime));

            if (!CollectionUtils.isEmpty(userList)) {
                //设置表格内容
                setContentForExportUser(sheet, userList);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("PBMS_UC_0003");
        }
    }

    public void exportCompanyTodoTaskUserList(XSSFWorkbook wb, Date startDate, Date endDate, String companyName, Double minRate, Double maxRate) throws GeneralException {
        // 查询数据
        List<PartyStudyCompanyDTO> dataList = partyTaskSV.selectCompanyTodoTaskUserList(startDate, endDate, companyName, minRate, maxRate);

        try {
            //创建一个SHEET页
            XSSFSheet sheet = wb.createSheet("党员学习待办总表");

            XSSFCellStyle alignBoderStyle = wb.createCellStyle();
            alignBoderStyle.setAlignment(HorizontalAlignment.CENTER);
            alignBoderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            alignBoderStyle.setBorderBottom(BorderStyle.THIN);
            alignBoderStyle.setBorderLeft(BorderStyle.THIN);
            alignBoderStyle.setBorderRight(BorderStyle.THIN);
            alignBoderStyle.setBorderTop(BorderStyle.THIN);

            //设置表格头
            setTitleForExportCompany(wb, alignBoderStyle, sheet, getTimeScope(startDate, endDate));

            if (!CollectionUtils.isEmpty(dataList)) {
                //设置表格内容
                setContentForExportCompany(sheet, dataList);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("PBMS_UC_0003");
        }
    }

    public void exportBranchTodoTaskUserList(XSSFWorkbook wb, Date startDate, Date endDate, String branchName, Double minRate, Double maxRate) throws GeneralException {
        // 查询数据

        List<PartyStudyBranchDTO> dataList = partyTaskSV.selectBranchTodoTaskUserList(startDate, endDate, branchName, minRate, maxRate);

        try {
            //创建一个SHEET页
            XSSFSheet sheet = wb.createSheet("党员学习待办分表");

            XSSFCellStyle alignBoderStyle = wb.createCellStyle();
            alignBoderStyle.setAlignment(HorizontalAlignment.CENTER);
            alignBoderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            alignBoderStyle.setBorderBottom(BorderStyle.THIN);
            alignBoderStyle.setBorderLeft(BorderStyle.THIN);
            alignBoderStyle.setBorderRight(BorderStyle.THIN);
            alignBoderStyle.setBorderTop(BorderStyle.THIN);

            //设置表格头
            setTitleForExportBranch(wb, alignBoderStyle, sheet, getTimeScope(startDate, endDate));

            if (!CollectionUtils.isEmpty(dataList)) {
                //设置表格内容
                setContentForExportBranch(sheet, dataList);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("PBMS_UC_0003");
        }
    }

    public void exportTaskList(XSSFWorkbook wb, Date startDate, Date endDate, Integer taskType, String title, Double minRate, Double maxRate) throws GeneralException {
        // 查询数据

        List<PartyStudTaskDTO> dataList = partyTaskSV.selectPartyStudyTask(startDate, endDate, taskType, title, minRate, maxRate);

        try {
            //创建一个SHEET页
            XSSFSheet sheet = wb.createSheet("党员学习待办（任务维度全网）");

            XSSFCellStyle alignBoderStyle = wb.createCellStyle();
            alignBoderStyle.setAlignment(HorizontalAlignment.CENTER);
            alignBoderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            alignBoderStyle.setBorderBottom(BorderStyle.THIN);
            alignBoderStyle.setBorderLeft(BorderStyle.THIN);
            alignBoderStyle.setBorderRight(BorderStyle.THIN);
            alignBoderStyle.setBorderTop(BorderStyle.THIN);

            //设置表格头
            setTitleForExportTask(wb, alignBoderStyle, sheet, getTimeScope(startDate, endDate));

            if (!CollectionUtils.isEmpty(dataList)) {
                //设置表格内容
                setContentForExportTask(sheet, dataList);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("PBMS_UC_0003");
        }
    }

    private void setContentForExportUser(XSSFSheet sheet, List<PartyTodoTaskListDTO> dataList) {
        // 起始行号
        int indexRow = 2;

        // 遍历结果集
        for (PartyTodoTaskListDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-所属分公司
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(data.getCompanyName());
            // 第二例-支部名称
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getBranchName());
            // 第二例-党员姓名
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getUserName());
            // 第二例-联系电话
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getTelephones());
            // 第二例-已完成量
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(data.getDoneNum());
            // 第二例-未完成量
            XSSFCell cell6 = row.createCell(5);
            cell6.setCellValue(data.getTodoNum());
            indexRow++;
        }
    }

    private void setContentForExportCompany(XSSFSheet sheet, List<PartyStudyCompanyDTO> dataList) {
        // 起始行号
        int indexRow = 3;

        // 遍历结果集
        for (PartyStudyCompanyDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-组织名称
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(data.getOrgName());
            // 第二例-已完成党员数
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getDoneNum());
            // 第二例-未完成党员数
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getTodoNum());
            // 第二例-党员完成占比（100%）
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getDoneRateStr());
            indexRow++;
        }
    }

    private void setContentForExportBranch(XSSFSheet sheet, List<PartyStudyBranchDTO> dataList) {
        // 起始行号
        int indexRow = 3;

        // 遍历结果集
        for (PartyStudyBranchDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-所属分公司
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(data.getComName());
            // 第一列-组织名称
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getBranchName());
            // 第二例-已完成党员数
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getDoneNum());
            // 第二例-未完成党员数
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getTodoNum());
            // 第二例-党员完成占比（100%）
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(data.getDoneRateStr());
            indexRow++;
        }
    }

    private void setContentForExportTask(XSSFSheet sheet, List<PartyStudTaskDTO> dataList) {
        // 起始行号
        int indexRow = 3;

        // 遍历结果集
        for (PartyStudTaskDTO data : dataList) {
            // 向sheet中写入数据
            XSSFRow row = sheet.createRow(indexRow);
            // 第一列-待办类型
            XSSFCell cell1 = row.createCell(0);
            cell1.setCellValue(getDescByType(data.getTaskType()));
            // 第一列-标题
            XSSFCell cell2 = row.createCell(1);
            cell2.setCellValue(data.getTitle());
            // 第二例-待办创建时间
            XSSFCell cell3 = row.createCell(2);
            cell3.setCellValue(data.getCreateTime());
            // 第二例-未学习党员数
            XSSFCell cell4 = row.createCell(3);
            cell4.setCellValue(data.getTodoNum());
            // 第二例-已学习党员数
            XSSFCell cell5 = row.createCell(4);
            cell5.setCellValue(data.getDoneNum());
            // 第二例-党员学习占比
            XSSFCell cell6 = row.createCell(5);
            cell6.setCellValue(data.getDoneRateStr());
            indexRow++;
        }
    }

    private void setTitleForExportUser(XSSFWorkbook wb, XSSFCellStyle alignBoderStyle, XSSFSheet sheet, String timeScope) {
        XSSFCellStyle alignCenterStyle = wb.createCellStyle();
        alignCenterStyle.setAlignment(HorizontalAlignment.CENTER);
        alignCenterStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        XSSFFont titleFont = wb.createFont();
        titleFont.setFontHeightInPoints(Short.valueOf("22"));
        alignCenterStyle.setFont(titleFont);

        // 生成表格标题行
        String[] headers = {"所属分公司", "支部名称", "党员姓名", "联系电话", "已完成量", "未完成量"};

        XSSFRow row0 = sheet.createRow(0);
        XSSFCell cell0 = row0.createCell(0);
        cell0.setCellStyle(alignCenterStyle);
        cell0.setCellValue("党员学习待办明细表");
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headers.length - 1));

//        XSSFRow row1 = sheet.createRow(1);
//        XSSFCellStyle alignCenterStyle1 = wb.createCellStyle();
//        alignCenterStyle1.setAlignment(HorizontalAlignment.RIGHT);
//        alignCenterStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
//        XSSFCell cell1 = row1.createCell(0);
//        cell1.setCellStyle(alignCenterStyle1);
//        cell1.setCellValue("统计范围：" + timeScope);
//        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, headers.length - 1));

        XSSFRow row = sheet.createRow(1);
        for (short i = 0; i < headers.length; i++) {
            XSSFCell cell = row.createCell(i);
            cell.setCellStyle(alignBoderStyle);
            cell.setCellValue(headers[i]);
        }

        // 设置列宽
        sheet.setColumnWidth(0, 1024 * 5);
        sheet.setColumnWidth(1, 1024 * 4);
    }

    private void setTitleForExportCompany(XSSFWorkbook wb, XSSFCellStyle alignBoderStyle, XSSFSheet sheet, String timeScope) {
        XSSFCellStyle alignCenterStyle = wb.createCellStyle();
        alignCenterStyle.setAlignment(HorizontalAlignment.CENTER);
        alignCenterStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        XSSFFont titleFont = wb.createFont();
        titleFont.setFontHeightInPoints(Short.valueOf("22"));
        alignCenterStyle.setFont(titleFont);

        // 生成表格标题行
        String[] headers = {"组织名称", "已完成党员数", "未完成党员数", "党员完成占比"};

        XSSFRow row0 = sheet.createRow(0);
        XSSFCell cell0 = row0.createCell(0);
        cell0.setCellStyle(alignCenterStyle);
        cell0.setCellValue("党员学习待办总表");
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headers.length - 1));

        XSSFRow row1 = sheet.createRow(1);
        XSSFCellStyle alignCenterStyle1 = wb.createCellStyle();
        alignCenterStyle1.setAlignment(HorizontalAlignment.RIGHT);
        alignCenterStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
        XSSFCell cell1 = row1.createCell(0);
        cell1.setCellStyle(alignCenterStyle1);
        cell1.setCellValue("统计范围：" + timeScope);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, headers.length - 1));

        XSSFRow row = sheet.createRow(2);
        for (short i = 0; i < headers.length; i++) {
            XSSFCell cell = row.createCell(i);
            cell.setCellStyle(alignBoderStyle);
            cell.setCellValue(headers[i]);
        }

        // 设置列宽
        sheet.setColumnWidth(0, 1024 * 5);
        sheet.setColumnWidth(1, 1024 * 4);
    }

    private void setTitleForExportBranch(XSSFWorkbook wb, XSSFCellStyle alignBoderStyle, XSSFSheet sheet, String timeScope) {
        XSSFCellStyle alignCenterStyle = wb.createCellStyle();
        alignCenterStyle.setAlignment(HorizontalAlignment.CENTER);
        alignCenterStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        XSSFFont titleFont = wb.createFont();
        titleFont.setFontHeightInPoints(Short.valueOf("22"));
        alignCenterStyle.setFont(titleFont);

        // 生成表格标题行
        String[] headers = {"所属分公司", "组织名称", "已完成党员数", "未完成党员数", "党员完成占比"};

        XSSFRow row0 = sheet.createRow(0);
        XSSFCell cell0 = row0.createCell(0);
        cell0.setCellStyle(alignCenterStyle);
        cell0.setCellValue("党员学习待办分表");
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headers.length - 1));

        XSSFRow row1 = sheet.createRow(1);
        XSSFCellStyle alignCenterStyle1 = wb.createCellStyle();
        alignCenterStyle1.setAlignment(HorizontalAlignment.RIGHT);
        alignCenterStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
        XSSFCell cell1 = row1.createCell(0);
        cell1.setCellStyle(alignCenterStyle1);
        cell1.setCellValue("统计范围：" + timeScope);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, headers.length - 1));

        XSSFRow row = sheet.createRow(2);
        for (short i = 0; i < headers.length; i++) {
            XSSFCell cell = row.createCell(i);
            cell.setCellStyle(alignBoderStyle);
            cell.setCellValue(headers[i]);
        }

        // 设置列宽
        sheet.setColumnWidth(0, 1024 * 5);
        sheet.setColumnWidth(1, 1024 * 4);
    }

    private void setTitleForExportTask(XSSFWorkbook wb, XSSFCellStyle alignBoderStyle, XSSFSheet sheet, String timeScope) {
        XSSFCellStyle alignCenterStyle = wb.createCellStyle();
        alignCenterStyle.setAlignment(HorizontalAlignment.CENTER);
        alignCenterStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        XSSFFont titleFont = wb.createFont();
        titleFont.setFontHeightInPoints(Short.valueOf("22"));
        alignCenterStyle.setFont(titleFont);

        // 生成表格标题行
        String[] headers = {"待办类型", "标题", "待办创建时间", "未学习党员数", "已学习党员数", "党员学习占比"};

        XSSFRow row0 = sheet.createRow(0);
        XSSFCell cell0 = row0.createCell(0);
        cell0.setCellStyle(alignCenterStyle);
        cell0.setCellValue("党员学习待办（任务维度全网）");
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headers.length - 1));

        XSSFRow row1 = sheet.createRow(1);
        XSSFCellStyle alignCenterStyle1 = wb.createCellStyle();
        alignCenterStyle1.setAlignment(HorizontalAlignment.RIGHT);
        alignCenterStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
        XSSFCell cell1 = row1.createCell(0);
        cell1.setCellStyle(alignCenterStyle1);
        cell1.setCellValue("统计范围：" + timeScope);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, headers.length - 1));

        XSSFRow row = sheet.createRow(2);
        for (short i = 0; i < headers.length; i++) {
            XSSFCell cell = row.createCell(i);
            cell.setCellStyle(alignBoderStyle);
            cell.setCellValue(headers[i]);
        }

        // 设置列宽
        sheet.setColumnWidth(0, 1024 * 5);
        sheet.setColumnWidth(1, 1024 * 4);
    }

    private String getDescByType(Integer type) {
        String desc = "未知类型";
        if (Integer.valueOf(180).equals(type)) {
            desc = "党建资讯";
        } else if (Integer.valueOf(190).equals(type)) {
            desc = "音频";
        }
        if (Integer.valueOf(191).equals(type)) {
            desc = "视频";
        }
        return desc;
    }
}
