package com.cmos.pbms.web.controller.rn;

import com.alibaba.dubbo.config.annotation.Reference;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.exception.SystemFailureException;
import com.cmos.common.exception.ValidationException;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.common.validator.rn.VBook;
import com.cmos.common.web.upload.exception.StorageException;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.common.Attachments;
import com.cmos.pbms.beans.dto.BookReviewSearchDTO;
import com.cmos.pbms.beans.dto.ComboSelectBean;
import com.cmos.pbms.beans.dto.FileProDTO;
import com.cmos.pbms.beans.enums.ProcessTypeEnum;
import com.cmos.pbms.beans.pm.Organization;
import com.cmos.pbms.beans.rn.Book;
import com.cmos.pbms.beans.rn.Likedbook;
import com.cmos.pbms.beans.sys.ReviewLog;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.iservice.common.IAttachmentsSV;
import com.cmos.pbms.iservice.pm.IOrganizationSV;
import com.cmos.pbms.iservice.rn.IBookSV;
import com.cmos.pbms.iservice.rn.ILikedbookSV;
import com.cmos.pbms.iservice.sys.IDictionariesSV;
import com.cmos.pbms.iservice.sys.IDictionaryItemsSV;
import com.cmos.pbms.iservice.sys.IReviewLogSV;
import com.cmos.pbms.utils.OnestUtil;
import com.cmos.pbms.utils.UIDUtil;
import com.cmos.pbms.utils.ZipUtil;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Strings;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/book")
@Validated
@Api("书籍信息操作控制器")
public class BookController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(BookController.class);
    private static final String MAP_KEY_CONTENTS = "contents";
    private static final String MAP_KEY_SUMMARY = "summary";
    private static final String OBJTYPE_RNEBOOK = "rnebook";
    private static final String OBJTYPE_RNBOOKTHEMEPIC = "rnbookthemepic";
    private static final Integer PUBLIC_SCOPE_YES = 1; // 可见范围-APP：公开
    private static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
    @Reference(group = "pbms")
    private IBookSV bookSV;
    @Reference(group = "pbms")
    private IOrganizationSV organizationSV;
    @Reference(group = "pbms")
    private IDictionaryItemsSV dictionaryItemsSV;
    @Reference(group = "pbms")
    private IDictionariesSV dictionariesSV;
    @Reference(group = "pbms")
    private ILikedbookSV likedbookmeetingPlanSV;
    @Reference(group = "pbms")
    private IAttachmentsSV attachmentmeetingPlanSV;
    @Reference(group = "pbms")
    private IReviewLogSV reviewLogSV;

    @RequestMapping(value = "/saveBook", method = RequestMethod.POST)
    @ApiOperation(value = "新建/编辑书籍", notes = "新建/编辑书籍")
    public int saveBook(VBook bookData, HttpServletRequest request, boolean isCommit) throws GeneralException {

        logger.info("111111111:" + request.getParameter("bookDetailIds"));
        // 当前登录用户
        Users user = getUser();
        String userId = user.getId();

        Set<ConstraintViolation<VBook>> constraintViolations = validator.validate(bookData);

        // 如果校验不通过，则结果集中会保存每一条校验失败的信息，判断结果集长度即可知道是否通过校验
        if (!constraintViolations.isEmpty()) {
            logger.error(constraintViolations.iterator().next().getMessage());
            throw new ValidationException("PBMS_SYS_1005", constraintViolations.iterator().next().getMessage());
        }
        try {
            if (!Strings.isNullOrEmpty(bookData.getIsbn())) {
                Long.valueOf(bookData.getIsbn());
            }
        } catch (NumberFormatException e) {
            logger.error("书籍ISBN号只能是13位的正整数，输入参数错误：" + bookData.getIsbn());
            throw new ValidationException("PBMS_SYS_1005", "书籍ISBN号只能是13位的正整数，输入参数错误");
        }

        Book book = bookSV.getByPrimaryKey(bookData.getId());

        boolean isCreate;

        if (book == null) {
            isCreate = true;
            book = new Book();
            book.setId(bookData.getId());
        } else {
            isCreate = false;
        }

        // 将书籍简介和目录存储到ONest
        Calendar calendar = Calendar.getInstance();
        Date current = calendar.getTime();

        InputStream summaryInputStream = new ByteArrayInputStream(bookData.getSummary().getBytes());
        InputStream contentsInputStream = new ByteArrayInputStream(bookData.getContents().getBytes());

        String fileName = UIDUtil.getUID() + ".html";

        String summaryUrl = storeOnest(summaryInputStream, MAP_KEY_SUMMARY + fileName, book.getSummary(), isCreate, user);
        String contentsUrl = storeOnest(contentsInputStream, MAP_KEY_CONTENTS + fileName, book.getContents(), isCreate, user);

        book.setOrgid(bookData.getOrgid());
        book.setBookname(bookData.getBookname());
        book.setAuthor(bookData.getAuthor());
        book.setIsbn(bookData.getIsbn());
        book.setPublisher(bookData.getPublisher());
        book.setCover(bookData.getCover());
        book.setEbook(bookData.getEbook());
        book.setSummary(summaryUrl);
        book.setContents(contentsUrl);
        book.setBooktype(bookData.getBooktype());
        book.setIsstop(bookData.getIsstop());
        book.setPubnum(bookData.getPubnum());
        book.setPubtime(bookData.getPubtime());
        book.setBookDetailIds(bookData.getBookDetailIds());
        book.setMakeFrom(bookData.getMakeFrom());
        book.setTurnFrom(bookData.getTurnFrom());
        book.setPublicScope(bookData.getPublicScope());

        if (isCommit) {
            //提交成待审核状态
            book.setReviewStatus(1);
            book.setSubmitUser(userId);
            book.setSubmitTime(current);

            // 生成操作记录
            ReviewLog log = new ReviewLog();
            log.setId(UIDUtil.getUID());
            log.setProcessType(ProcessTypeEnum.BOOK_REVIEW.getCode());
            log.setObjectId(book.getId());
            log.setOperationType(1);
            log.setOperationDesc("提交审核");
            log.setOperationUserId(userId);
            log.setOperationUserName(user.getUsername());
            log.setOperationTime(current);
            log.setResuseReason(null);
            log.setRemark(null);
            log.setUserOrgId(user.getOrgid());
            log.setUserOrgName(organizationSV.getByPrimaryKey(user.getOrgid()).getOrgsname());
            reviewLogSV.insertInAsync(log);
        } else {
            if (PUBLIC_SCOPE_YES.equals(bookData.getPublicScope())) {
                book.setReviewStatus(0); // 保存成草稿
            } else {
                book.setReviewStatus(4); // 无需审核，直接更新为已发布
                book.setReleaseTime(current);
            }
        }

        if (isCreate) {
            return createBook(book, user);
        } else {
            return updateBook(book, user);
        }
    }

    private String storeOnest(InputStream summaryInputStream, String fileName, String oldUrl, Boolean isCreate, Users user) throws GeneralException {
        // 存储到oNest并获取存储路径
        String url = OnestUtil.storeByStream(summaryInputStream, fileName, "rn", user.getProvince(), user.getOrgid());
        try {

            // 删除oNest的旧文件
            if (!isCreate && StringUtils.isNotBlank(oldUrl)) {
                OnestUtil.delete(oldUrl);
            }

        } catch (StorageException e) {
            logger.error(e.getMessage(), e);
            throw new SystemFailureException("书籍保存到ONest出错");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        return url;
    }

    @RequestMapping(value = "/getBookListByParams", method = RequestMethod.POST)
    @ApiOperation(value = "根据查询条件获取书籍列表", notes = "根据查询条件获取书籍列表")
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<Book> getBookListByParams(Book vSearchBean, String orgCode) throws GeneralException {
        Set<ConstraintViolation<Book>> constraintViolations = validator.validate(vSearchBean);

        // 如果校验不通过，则结果集中会保存每一条校验失败的信息，判断结果集长度即可知道是否通过校验
        if (!constraintViolations.isEmpty()) {
            logger.error(constraintViolations.iterator().next().getMessage());
            throw new ValidationException("PBMS_SYS_1005", constraintViolations.iterator().next().getMessage());
        }

        PageInfo<Book> bookPageInfo = bookSV.getBookListByParams(vSearchBean);
        List<Book> bookList = bookPageInfo.getList();

        for (Book book : bookList) {
            if (StringUtils.isBlank(orgCode) ||
                    (StringUtils.isNotBlank(book.getOrgCode()) && book.getOrgCode().contains(orgCode))) {
                book.setEnableEdit(1);
            }
        }

        return bookPageInfo;
    }

    @RequestMapping(value = "/getListByParamsForApp", method = RequestMethod.POST)
    @ApiOperation(value = "根据查询条件获取书籍列表（APP端）", notes = "根据查询条件获取书籍列表（APP端）")
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<Map<String, Object>> getListByParamsForApp(Integer page, Integer limit, String keyword) {
        Integer pageNum = page == null || page <= 0 ? 1 : page;
        Integer pageSize = limit == null || limit <= 0 ? 1 : limit;
        String searchKeyWord = StringUtils.isBlank(keyword) ? null : keyword;
        Users user = getUser();

        PageInfo<Map<String, Object>> bookPage = bookSV.getListByParamsForApp(pageNum, pageSize, searchKeyWord, user.getMaxRoleOrg().getCodestr());

        List<Map<String, Object>> bookList = bookPage.getList();

        for (Map<String, Object> bookMap :
                bookList) {
            String summary = (String) bookMap.get(MAP_KEY_SUMMARY);

            if (StringUtils.isNotBlank(summary)) {
                summary = OnestUtil.getContent(summary);
            }

            bookMap.put(MAP_KEY_SUMMARY, summary);
        }

        return bookPage;
    }

    @RequestMapping(value = "/getBookShelfForApp", method = RequestMethod.POST)
    @ApiOperation(value = "获取当前用户书架数据（APP端）", notes = "获取当前用户书架数据（APP端）")
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<Map<String, Object>> getBookShelfForApp(Integer page, Integer limit, String keyword) {
        Integer pageNum = page == null || page <= 0 ? 1 : page;
        Integer pageSize = limit == null || limit <= 0 ? 1 : limit;
        String searchKeyWord = StringUtils.isBlank(keyword) ? null : keyword;

        Users user = getUser();

        PageInfo<Map<String, Object>> bookPage = bookSV.getBookShelfForApp(pageNum, pageSize, searchKeyWord, user.getId());

        List<Map<String, Object>> bookList = bookPage.getList();

        for (int i = 0; i < bookList.size(); i++) {
            Map<String, Object> bookMap = bookList.get(i);

            String summary = (String) bookMap.get(MAP_KEY_SUMMARY);

            if (StringUtils.isNotBlank(summary)) {
                summary = OnestUtil.getContent(summary);
            }

            bookMap.put(MAP_KEY_SUMMARY, summary);
        }

        return bookPage;
    }

    @RequestMapping(value = "/getContentByOnestUrl", method = RequestMethod.POST)
    @ApiOperation(value = "获取当前用户书架数据（APP端）", notes = "获取当前用户书架数据（APP端）")
    public String getContentByOnestUrl(String url) {
        String content = "";

        if (StringUtils.isNotBlank(url)) {
            content = OnestUtil.getContent(url);
        }

        return content;
    }

    @RequestMapping(value = "/getBookInfoById", method = RequestMethod.POST)
    @ApiOperation(value = "获取书籍详情信息", notes = "获取书籍详情信息")
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Map<String, Object> getBookInfoById(String id) throws GeneralException {
        if (StringUtils.isBlank(id)) {
            logger.error("获取书籍详情的请求参数【id】不能为空");
            throw new ValidationException("PBMS_SYS_1005", "获取书籍详情的请求参数【id】不能为空");
        }
        Users user = getUser();
        Map<String, Object> resulMap = bookSV.getBookDetailById(id, null == user ? null : user.getId());

        // 获取推荐者信息
        String orgid = (String) resulMap.get("recommendor");
        if (StringUtils.isNotBlank(orgid)) {
            Organization org = organizationSV.getByPrimaryKey(orgid);
            if (null != org && !org.getOrgsname().isEmpty()) {
                resulMap.put("recommendor", org.getOrgsname());
            }
        }

        // 获取图书分类
        String booktypeCode = (String) resulMap.get("booktype");
        if (StringUtils.isNotBlank(booktypeCode)) {
            String booktype = dictionaryItemsSV.getTextByCodeAndDictcode(booktypeCode, "BOOKTYPE");
            resulMap.put("booktype", booktype);
        }

        // 获取电子书籍信息资源信息
        List<Attachments> ebook = attachmentmeetingPlanSV.getListByObjIdType(id, OBJTYPE_RNEBOOK);

        if (!ebook.isEmpty()) {
            resulMap.put("ebookFileName", ebook.get(0).getAttname());
            resulMap.put("ebookFileSrc", ebook.get(0).getUrl());
        }

        // 从ONest获取书籍简介内容
        String summaryUrl = (String) resulMap.get(MAP_KEY_SUMMARY);
        if (StringUtils.isNotBlank(summaryUrl)) {
            String summary = OnestUtil.getContent(summaryUrl);
            resulMap.put(MAP_KEY_SUMMARY, summary);
        }

        // 从ONest获取书籍目录内容
        String contentsUrl = (String) resulMap.get(MAP_KEY_CONTENTS);
        if (StringUtils.isNotBlank(contentsUrl)) {
            String contents = OnestUtil.getContent(contentsUrl);
            if (StringUtils.isNotBlank(contents)) {
                contents = contents.replace("\n", "<br>");
                resulMap.put(MAP_KEY_CONTENTS, contents);
            }
        }

        return resulMap;
    }

    @RequestMapping(value = "/getBookDetailById", method = RequestMethod.POST)
    @ApiOperation(value = "获取书籍详情信息（APP端）", notes = "获取书籍详情信息（APP端）")
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Map<String, Object> getBookDetailById(String id) throws GeneralException {
        if (StringUtils.isBlank(id)) {
            logger.error("获取书籍详情的请求参数【id】不能为空");
            throw new ValidationException("PBMS_SYS_1005", "获取书籍详情的请求参数【id】不能为空");
        }
        Users user = getUser();
        Map<String, Object> resulMap = bookSV.getBookDetailById(id, null == user ? null : user.getId());

        // 获取电子书籍信息资源信息
        List<Attachments> ebook = attachmentmeetingPlanSV.getListByObjIdType(id, OBJTYPE_RNEBOOK);

        if (!ebook.isEmpty()) {
            resulMap.put("ebookFileName", ebook.get(0).getAttname());
            resulMap.put("ebookFileSrc", ebook.get(0).getUrl());
        }

        // 从ONest获取书籍简介内容
        String summaryUrl = (String) resulMap.get(MAP_KEY_SUMMARY);
        if (StringUtils.isNotBlank(summaryUrl)) {
            String summary = OnestUtil.getContent(summaryUrl);
            resulMap.put(MAP_KEY_SUMMARY, summary);
        }

        // 从ONest获取书籍目录内容
        String contentsUrl = (String) resulMap.get(MAP_KEY_CONTENTS);
        if (StringUtils.isNotBlank(contentsUrl)) {
            String contents = OnestUtil.getContent(contentsUrl);
            if (StringUtils.isNoneBlank(contents))
                contents = contents.replace("\n", "<br>");
            resulMap.put(MAP_KEY_CONTENTS, contents);
        }

        // 查询当前书籍书否已添加到当前用户的书架
        resulMap.put("hasLiked", bookSV.isLiked(id, user.getId()));

        return resulMap;
    }

    @RequestMapping(value = "/deleteBookById", method = RequestMethod.POST)
    @ApiOperation(value = "删除书籍（逻辑删除）", notes = "删除书籍（逻辑删除）")
    public int deleteBookById(String id) {
        Users user = getUser();
        Date current = new Date();

        return bookSV.deleteByParams(id, user.getId(), current);
    }

    @RequestMapping(value = "/isRecommended", method = RequestMethod.POST)
    @ApiOperation(value = "查询书籍是否已被读书活动所推荐", notes = "查询书籍是否已被读书活动所推荐")
    public int isRecommended(String bookid) {
        return bookSV.isRecommended(bookid);
    }

    @RequestMapping(value = "/getBookById", method = RequestMethod.POST)
    @ApiOperation(value = "根据id获取书籍数据记录信息", notes = "根据id获取书籍数据记录信息")
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Book getBookById(String id) {

        Book book = bookSV.getByPrimaryKey(id);

        List<Attachments> themepic = attachmentmeetingPlanSV.getListByObjIdType(book.getId(), OBJTYPE_RNBOOKTHEMEPIC);
        List<Attachments> ebook = attachmentmeetingPlanSV.getListByObjIdType(book.getId(), OBJTYPE_RNEBOOK);

        if (!themepic.isEmpty()) {
            book.setThemepicAttr(themepic.get(0));
        }

        if (!ebook.isEmpty()) {
            book.setEbookAttr(ebook.get(0));
        }

        // 从ONest获取书籍简介内容
        if (StringUtils.isNotBlank(book.getSummary())) {
            String summary = OnestUtil.getContent(book.getSummary());
            book.setSummary(summary);
        }

        // 从ONest获取书籍目录内容
        if (StringUtils.isNotBlank(book.getContents())) {
            String contents = OnestUtil.getContent(book.getContents());
            book.setContents(contents);
        }

        return book;
    }

    @RequestMapping(value = "/getBookComboSelect", method = RequestMethod.POST)
    @ApiOperation(value = "根据书名初始化书籍可搜索下拉组件数据", notes = "根据书名初始化书籍可搜索下拉组件数据")
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public List<ComboSelectBean> getBookComboSelect(String bookname) {
        String keyword = StringUtils.isBlank(bookname) ? null : bookname;
        return bookSV.listByBookname(keyword);
    }

    @RequestMapping(value = "/toggleLikeBook", method = RequestMethod.POST)
    @ApiOperation(value = "添加图书到当前用户书架", notes = "添加图书到当前用户书架")
    public int toggleLikeBook(String bookid, Boolean isLike) {

        Users user = getUser();

        int result;

        if (isLike) {
            Likedbook likedbook = new Likedbook();
            likedbook.setId(UIDUtil.getUID());
            likedbook.setBookid(bookid);
            likedbook.setUserid(user.getId());
            likedbook.setOrdernum(1);
            likedbook.setIsdeleted(0);
            likedbook.setCreatedby(user.getId());
            likedbook.setCreateddate(new Date());

            result = likedbookmeetingPlanSV.insert(likedbook);
        } else {
            result = likedbookmeetingPlanSV.deleteByBooidAndUserid(bookid, user.getId());
        }

        return result;
    }

    /**
     * 新增书籍
     *
     * @param book 书籍实体类
     * @param user 当前登录用户
     * @return 操作数据库记录数
     */
    private int createBook(Book book, Users user) {
        Calendar calendar = Calendar.getInstance();
        Date current = calendar.getTime();

        book.setIsdeleted(0);
        book.setCreatedby(user.getId());
        book.setCreateddate(current);
        book.setModifiedby(null);
        book.setModifieddate(null);

        return bookSV.insertBookAndBookDetail(book);
    }

    /**
     * 更新书籍信息
     *
     * @param book 书籍实体类
     * @param user 当前登录用户
     * @return 操作数据库记录数
     */
    private int updateBook(Book book, Users user) {
        Calendar calendar = Calendar.getInstance();
        Date current = calendar.getTime();
        book.setModifieddate(current);
        book.setModifiedby(user.getId());
        return bookSV.updateBookAdnBookDetail(book);
    }

    @RequestMapping(value = "/downloadInBatch", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "批量下载电子书", notes = "批量下载电子书",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功"),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "bookId", value = "会议id", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void downloadInBatch(String bookId, HttpServletResponse response) throws GeneralException {
        // 查询书籍信息
        Book book = bookSV.getByPrimaryKey(bookId);
        // 获取电子书籍信息资源信息
        List<Attachments> ebooks = attachmentmeetingPlanSV.getListByObjIdType(bookId, OBJTYPE_RNEBOOK);

        // 定义文件结构Entry
        FileProDTO fileDTO = new FileProDTO();
        Map<String, InputStream> fileAndDir = new HashMap<>(1);

        for (Attachments temp : ebooks) {
            try {
                fileAndDir.put(book.getBookname().concat(File.separator) + temp.getAttname(), OnestUtil.getInputStreamByONestUrl(temp.getUrl()));
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                throw new GeneralException("PBMS_ME_1052");
            }
        }
        fileDTO.setPathAndContent(fileAndDir);

        try {
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(book.getBookname().getBytes("utf-8"), "ISO8859-1") + ".zip");

            // 压缩
            ZipOutputStream zipOutputStream = new ZipOutputStream(out);
            ZipUtil.toZip(fileDTO, zipOutputStream);

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("PBMS_ME_1052");
        }
    }

    @RequestMapping(value = "/batchAuditBook", method = RequestMethod.POST)
    @ApiOperation(value = "批量审批书籍", notes = "批量审批书籍",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "书籍ID集合（多记录中间用英文逗号隔开）", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "passOrNot", value = "审核通过与否（true;通过；false:拒绝）", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "refuseReason", value = "拒绝原因（审核拒绝时为必填）", dataType = "date", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int batchAuditBook(String ids, boolean passOrNot, String refuseReason) throws GeneralException {
        // 参数校验
        if (StringUtils.isBlank(ids)) {
            logger.error("请求参数错误，请选择要审批的书籍");
            throw new GeneralException("PBMS_COM_1001", "请求参数错误，请选择要审批的书籍");
        }

        Users user = getUser();

        return bookSV.batchAuditBook(ids.split(","), passOrNot, user.getId(), user.getUsername(), refuseReason);
    }

    @RequestMapping(value = "/getBookReviewList", method = RequestMethod.POST)
    @ApiOperation(value = "根据查询条件获取书籍审核列表", notes = "根据查询条件获取书籍审核列表",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = PageInfo.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgId", value = "推荐者", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "bookName", value = "书名", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "bookAuthor", value = "作者", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "makeFrom", value = "出处", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "reviewStatus", value = "审核状态", required = false, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<Book> getBookReviewList(BookReviewSearchDTO vSearchBean) throws GeneralException {
        Set<ConstraintViolation<BookReviewSearchDTO>> constraintViolations = validator.validate(vSearchBean);
        // 如果校验不通过，则结果集中会保存每一条校验失败的信息，判断结果集长度即可知道是否通过校验
        if (!constraintViolations.isEmpty()) {
            logger.error(constraintViolations.iterator().next().getMessage());
            throw new ValidationException("PBMS_SYS_1005", constraintViolations.iterator().next().getMessage());
        }
        return bookSV.getBookReviewList(vSearchBean);
    }
}
