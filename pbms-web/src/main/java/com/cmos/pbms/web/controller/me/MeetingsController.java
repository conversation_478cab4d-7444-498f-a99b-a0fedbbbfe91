package com.cmos.pbms.web.controller.me;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cmos.cache.service.ICacheService;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.exception.SystemFailureException;
import com.cmos.common.exception.ValidationException;
import com.cmos.common.spring.AppEnv;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.common.validator.me.*;
import com.cmos.common.web.upload.exception.StorageException;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.onest.ONestUtil;
import com.cmos.pbms.beans.common.Attachments;
import com.cmos.pbms.beans.common.PushMsg;
import com.cmos.pbms.beans.common.QrCodeMsg;
import com.cmos.pbms.beans.common.WorkTask;
import com.cmos.pbms.beans.dto.*;
import com.cmos.pbms.beans.enums.*;
import com.cmos.pbms.beans.me.*;
import com.cmos.pbms.beans.pl.StudySource;
import com.cmos.pbms.beans.pm.Organization;
import com.cmos.pbms.beans.sys.DictionaryItems;
import com.cmos.pbms.beans.sys.ReviewLog;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.iservice.common.IAttachmentsSV;
import com.cmos.pbms.iservice.common.IPushMsgSV;
import com.cmos.pbms.iservice.common.IQrCodeMsgSV;
import com.cmos.pbms.iservice.common.IWorkTaskSV;
import com.cmos.pbms.iservice.me.*;
import com.cmos.pbms.iservice.pl.IStudySourceSV;
import com.cmos.pbms.iservice.pm.IOrganizationSV;
import com.cmos.pbms.iservice.sys.*;
import com.cmos.pbms.iservice.vd.IVdMeetingsSV;
import com.cmos.pbms.open.response.RespOAApproveSubmitDataBean;
import com.cmos.pbms.open.service.ApprovalProcessServiceOAImpl;
import com.cmos.pbms.utils.*;
import com.cmos.pbms.utils.constants.PbmsConstants;
import com.cmos.pbms.web.annotation.Log;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.common.ComService;
import com.cmos.pbms.web.config.DateValueFormatter;
import com.github.pagehelper.PageInfo;
import com.itextpdf.text.pdf.BaseFont;
import com.lowagie.text.DocumentException;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.*;
import org.jsoup.helper.StringUtil;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.ResourceUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.templateresolver.FileTemplateResolver;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会议接口控制器
 *
 * <AUTHOR>
 * @date 2018-6-22 15:59:14
 */
@RestController
@RequestMapping(value = "/meeting")
@Validated
@Api("会议操作控制器")
@Slf4j
public class MeetingsController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(MeetingsController.class);
    private static final String NO_MEETING_ERR_MSG = "未能查询到会议信息";
    private static final String MSG_REQUEST_PARAMS_ERROR = "PBMS_COM_1001";
    private static final String NO_DATA_FOR_PARAM = "PBMS_COM_1002";
    private static final String JSON_FORMAT_ERROR = "PBMS_COM_1004";
    private static final String NEED_CANCEL_REASON = "PBMS_ME_1001";
    private static final String NO_MEETING_ERROR = "PBMS_ME_1002";
    private static final String SHOULD_TO_BE_NOTIFY = "PBMS_ME_1003";
    private static final String TEACHER_NOT_EMPTY = "PBMS_ME_1004";
    private static final String SING_NOT_EMPTY = "PBMS_ME_1005";
    private static final String PICTURE_NOT_EMPTY = "PBMS_ME_1006";
    private static final String MINUTES_NOT_EMPTY = "PBMS_ME_1007";
    private static final String SHOULD_CREATE_BY_GROUP_LEADER = "PBMS_ME_1009";
    private static final String NO_ORG_INFO = "PBMS_ME_1010";
    private static final String NO_BRANCH_INFO = "PBMS_ME_1070";
    private static final String QR_SIGN_ERROR = "PBMS_ME_1043";
    private static final String QR_PROP_ERROR = "PBMS_ME_1051";
    private static final String QR_SIGN_SYNTHESIS = "PBMS_ME_1045";
    private static final String ERROR_ORG_TYPE = "PBMS_ME_1046";
    private static final String NEED_MORE_LEADER = "PBMS_ME_1047";
    private static final String ME_STATUS_ERROR = "PBMS_ME_1020";
    private static final String MINUTES_NOT_REVIEW_PASS = "PBMS_ME_1022";
    private static final String MINUTES_NOT_SUBMIT = "PBMS_ME_1023";
    private static final String DK_NOT_NEED_MINUTES = "PBMS_ME_1024";
    private static final String SYS_NO_PARTIER_ROLE = "PBMS_SYS_0025";
    private static final String MSG_NOTICE_PARAMS_ERROR = "PBMS_ME_1029";
    private static final String MSG_SUBMIT_PARAMS_ERROR = "PBMS_ME_1030";
    private static final String MSG_HOLD_PARAMS_ERROR = "PBMS_ME_1031";
    private static final String HOLED_VC_ADDR = "PBMS_ME_1038";
    private static final String HOLED_VC_SAVE = "PBMS_ME_1039";
    private static final String NOT_SUPPLY_ONLINE = "PBMS_ME_1042";
    private static final String NOT_BE_SUPPLY = "PBMS_ME_1041";
    private static final Integer MSG_TYPE_IMPORT = 9; // 推送消息重要程度（0：紧急）
    private static final Integer meetingPlanSV_TYPE_MEETING = 50; // 推送消息业务类型（50：会议）
    private static final Integer TASK_STATUS_TODO = 0; // 事项状态（0：待办）
    private static final Integer PUBLIC_MOD_NO = 0; // 公开方式（0：不公开）
    private static final Short MEETING_ISPLAN_YES = 1; // 会议是否计划内（1：是）
    private static final Integer MSG_STATUS_UNREAD = 0; // 消息状态（0：未读）
    private static final String HEADIMG_URL = AppEnv.getString("pbms.headimgurl");
    private static final String VC_ADDRESS = AppEnv.getString("pbms.vcaddress");    //视频直播平台点播视频列表接入地址
    private static final String VC_ACCOUNT = AppEnv.getString("pbms.vcaccount");    //视频直播平台账号
    private static final String VC_PLAY_ID = AppEnv.getString("pbms.vcplayid");    //视频直播平台直播ID
    private static final String VC_TOKEN_KEY = AppEnv.getString("pbms.vctokenkey"); //视频直播平台tokenkey
    private static final String MEETING_TYPE_UNDERLINE = "01";  //会议类型，线下会议类型
    private static final String MEETING_TYPE_ONLINE = "02"; //会议类型，线上会议类型
    private static final String MEETING_TIMELINESS_REALTIME = "01"; // 会议类型，01-实时
    private static final String MEETING_TIMELINESS_SUPPLY = "02"; // 会议类型，02-补录
    private static final String PROCESS_TYPE_MAIN = "01";   //流程类型，主流程
    private static final String PROCESS_TYPE_DISTRIB = "02";    //流程类型，分发流程
    private static final String HOLD_MEETING = "HOLD_MEETING"; //归档标准字典项类型代码
    private static final String HOLD_MEETING_STUDY = "STUDY"; //归档标准字典项类型代码
    private static final String HOLD_MEETING_ONLINE = "ONLINE"; //归档标准，线上会议类型代码
    private static final String HOLD_MEETING_UNDERLINE = "UNDERLINE"; //归档标准，线下会议类型代码
    private static final String NOTICE_MSG_OBJ_TYPE = "01"; // 短信提醒类型 01-三会一课
    private static final String FREE_ROOM_FAILED = "PBMS_ME_1037";
    private static final String POST_NAME_DANGXIAOZUZHANG = "DANGXIAOZUZHANG"; // 字典表配置党小组长职务编码
    private static final String ACITITY_NOT_EMPTY = "PBMS_ME_1069";
    private static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
    private static String vodcid = AppEnv.getString("pbms.vodcid");
    private static String vodtokenkey = AppEnv.getString("pbms.vodtokenkey");
    private static String vodBaseUrl = AppEnv.getString("pbms.vodBaseUrl");
    private static String logo_me_sign = AppEnv.getString("pbms.logo_me_sign"); //三会一课二维码logo地址

    private static String qr_me_bg = AppEnv.getString("pbms.qr_me_bg"); //三会一课二维码合成背景图地址

    @Value("${pbms.animeet.key}")
    private String animeetKey;

    @Value("${pbms.animeet.host}")
    private String animeetHost;

    @Value("${pbms.animeet.loginUrl}")
    private String animeetLoginUrl;

    @Value("${pbms.animeet.getTextJson}")
    private String animeetGetTextJson;

    @Reference(group = "pbms")
    private IMeetingsSV meetingsSV;

    @Reference(group = "pbms")
    private IConventioneerSV conventioneerSV;

    @Reference(group = "pbms")
    private ICheckListSV checkListSV;

    @Reference(group = "pbms")
    private IUserSV userSV;

    @Reference(group = "pbms")
    private IWorkTaskSV workTaskSV;

    @Reference(group = "pbms")
    private IOrganizationSV organizationSV;

    @Reference(group = "pbms")
    private IPushMsgSV pushMsgSV;

    @Reference(group = "pbms")
    private IPlanTaskSV planTaskSV;

    @Reference(group = "pbms")
    private IDictionaryItemsSV dictionaryItemsSV;

    @Reference(group = "pbms")
    private IAttachmentsSV attachmentsSV;

    @Reference(group = "pbms")
    private IMeetingRequireSV meetingRequireSV;

    @Reference(group = "pbms")
    private IReviewLogSV reviewLogSV;

    @Reference(group = "pbms")
    private IRoleSV roleSV;

    @Reference(group = "pbms")
    private IMeetingMsgSettingSV meetingMsgSettingSV;

    @Reference(group = "pbms")
    private IProcessRelationSV processRelationSV;

    @Reference(group = "pbms")
    private IUserRoleSV userRoleSV;

    @Reference(group = "pbms")
    private INoticeMsgTempinfoSV noticeMsgTempinfoSV;

    @Reference(group = "pbms")
    private IStudySourceSV studySourceSV;

    @Reference(group = "pbms")
    private IQrCodeMsgSV qrCodeMsgSV;

    @Reference(group = "pbms")
    private IVdMeetingsSV vdMeetingsSV;

    @Reference(group = "pbms")
    private IAttendancesSV attendancesSV;

    @Autowired
    private ComService comService;

    @Autowired
    private MeetingExportService meetingExportService;

    @Reference(group = "pbms")
    private IMeVoiceTranslationInfoSV meVoiceTranslationInfoSV;
    @Resource
    private ApprovalProcessServiceOAImpl approvalProcessServiceOA;

    private static void setHorizontal(XWPFTableCell cell) {
        CTTc cttc = cell.getCTTc();
        CTP ctp = cttc.getPList().get(0);
        CTPPr ctppr = ctp.getPPr();
        if (ctppr == null) {
            ctppr = ctp.addNewPPr();
        }
        CTJc ctjc = ctppr.getJc();
        if (ctjc == null) {
            ctjc = ctppr.addNewJc();
        }
        ctjc.setVal(STJc.CENTER); // 水平居中
    }

    // word跨列合并单元格
    public static void mergeCellsHorizontal(XWPFTable table, int row, int fromCell, int toCell) {
        for (int cellIndex = fromCell; cellIndex <= toCell; cellIndex++) {
            XWPFTableCell cell = table.getRow(row).getCell(cellIndex);
            if (cellIndex == fromCell) {
                // The first merged cell is set with RESTART merge value
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                // Cells which join (merge) the first one, are set with CONTINUE
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    @RequestMapping(value = "/signRefuse", method = RequestMethod.POST)
    @ApiOperation(value = "书记签字拒绝", notes = "书记签字拒绝",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = MeetingsDetailDTO.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingId", value = "会议唯一标识", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "refuseReason", value = "签名拒绝原因", required = true, dataType = "Integer", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Integer signRefuse(String meetingId, String refuseReason) throws GeneralException {
        Users user = getUser();
        ValidateUtil.isNotEmpty(meetingId, refuseReason);

        return meetingsSV.signRefuse(meetingId, refuseReason, user.getOrgid(), user.getUsername(), user.getId(), new Date());
    }

    @RequestMapping(value = "/sign", method = RequestMethod.POST)
    @ApiOperation(value = "书记签名接口", notes = "书记签名接口",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = MeetingsDetailDTO.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingId", value = "会议唯一标识", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "signImageUrl", value = "签字图片路径", required = true, dataType = "Integer", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void sign(String meetingId, String signImageUrl) throws GeneralException {
        Users user = getUser();
        ValidateUtil.isNotEmpty(meetingId, signImageUrl);
        Meetings meetings = meetingsSV.sign(meetingId, signImageUrl, user.getId(), user.getUsername(), new Date());
        if (null != meetings.getSignState() && 3 == meetings.getSignState())
            doHoldMeeting(meetings, 1, null, null, null, null, user, true, null);
    }

    @RequestMapping(value = "/getMeetingsDetailByIdForApp", method = RequestMethod.POST)
    @ApiOperation(value = "根据id获取会议详情", notes = "根据id获取会议详情",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = MeetingsDetailDTO.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = MeetingsDetailDTO.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingsId", value = "会议唯一标识", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public MeetingsDetailDTO getMeetingsDetailByIdForApp(String meetingsId) throws GeneralException {
        //如果是新增 返回一个id就行了
        Assert.isTrue(StringUtils.isNotBlank(meetingsId),"id不能为空");
        Users user = getUser();
        if ("add_meeting".equals(meetingsId)){
            MeetingsDetailDTO temp = new MeetingsDetailDTO();
            temp.setId(UIDUtil.getUID());
            temp.setIsAdd(1);
            //返回当前组织
            String orgId = user.getCurrentRoleOrg().getId();
            Organization organization = organizationSV.getByPrimaryKey(orgId);
            if (null == organization) {
                logger.error("请求参数错误：没有查询到党支部/党小组信息");
                throw new GeneralException(NO_ORG_INFO);
            }
            temp.setOrgid(organization.getId());
            temp.setOrgcode(organization.getCodestr());
            temp.setOrgname(organization.getOrgName());
            temp.setOrganization(organization);
//            //获取期数 (用党支部)
//            Organization branchByOrg = organizationSV.getBranchByOrg(organization.getId());
//            if (null == branchByOrg) {
//                logger.error("请求错误，找不到所属党支部");
//                throw new GeneralException(NO_BRANCH_INFO);
//            }
            //根据所属党支部去获取期数
//            String period = meetingsSV.getPeriod(branchByOrg.getId());
            //查询预计人数，根据组织查
//            int orgUserCount = organizationSV.getOrgUserCount(organization.getCodestr());
//            temp.setExpectedNum(String.valueOf(orgUserCount));
            return temp;
        }

        MeetingsDetailDTO meetingsDetailDTO = meetingsSV.getFullDetailById(meetingsId, user);

        if (null != meetingsDetailDTO) {

            //过度数据处理代码，后期可以移除 -- begin
            Meetings meetings = meetingsSV.getByPrimaryKey(meetingsId);
            Organization organization = organizationSV.getByPrimaryKey(meetings.getOrgid());
            if (!Objects.isNull(organization) && !organization.getCodestr().equals(meetings.getOrgcode())) {
                meetings.setOrgcode(organization.getCodestr());
                meetingsSV.updateByPrimaryKeySelective(meetings);
            }
            meetingsDetailDTO.setOrgcode(meetings.getOrgcode());
            //过度数据处理代码，后期可以移除 -- end

            meetingsDetailDTO.setVcAddress(VC_ADDRESS);
            meetingsDetailDTO.setVcAccount(VC_ACCOUNT);
            meetingsDetailDTO.setVcPlayId(VC_PLAY_ID);
            meetingsDetailDTO.setVcTokenKey(VC_TOKEN_KEY);
            meetingsDetailDTO.setVodBaseUrl(vodBaseUrl);
            meetingsDetailDTO.setVodcid(vodcid);
            meetingsDetailDTO.setVodtokenkey(vodtokenkey);

            List<Attachments> qrList = attachmentsSV.getListByObjIdType(meetings.getId(), PbmsConstants.QR_BUS_ME_SIGN);
            if (!qrList.isEmpty()) {
                String qrurl = qrList.get(0).getUrl();
                qrurl = OnestUtil.subStringUrl(qrurl);
                meetingsDetailDTO.setQrUrl(qrurl);
            }

            meetingsDetailDTO.setAttendancesList(attendancesSV.getByMeetingId(meetingsDetailDTO.getId()));
            //如果是集中学习 算一下出勤率 总人数 参加人数
            if (Objects.equals(MeetingTypeEnum.STUDY.getCode(), meetingsDetailDTO.getType())){
                meetingsDetailDTO.setAttendanceMsg(buildAttendanceMsg(meetingsDetailDTO));
            }
            //设置关联的任务
            meetingsDetailDTO.setRelatedTasks(meetingsSV.getRelatedTasks(meetingsDetailDTO.getId()));
        }
        return meetingsDetailDTO;
    }


    /**
     * 生成出勤情况消息
     *
     * @param meetingsDetailDTO 会议详情 DTO，包含 orgcode 和 attendancesList
     * @return 格式化后的消息字符串，例如 “总人数：10人，参加人数：8人，出勤率：80.00%（只统计系统内人员）”
     */
    public String buildAttendanceMsg(MeetingsDetailDTO meetingsDetailDTO) {
        // 1. 获取当前组织有多少人
        Map<String, Object> params = new HashMap<>(16);
        params.put("orgcode", meetingsDetailDTO.getOrgcode());
        PageInfo<Map<String, Object>> orgPage = userSV.getUserByStageAndOrg(
                1,               // stage 参数示例
                10,              // pageSize 参数示例
                params,
                new ArrayList<>()             // 其他查询条件
        );
        long total = orgPage == null ? 0L : orgPage.getTotal();

        // 2. 统计参加人数（personId 不为空视为“参加”）
        List<Attendances> attendancesList = meetingsDetailDTO.getAttendancesList();
        long count = 0L;
        if (attendancesList != null) {
            count = attendancesList.stream()
                    .filter(a -> StringUtils.isNotBlank(a.getPersonId()))
                    .count();
        }

        // 3. 计算出勤率，保留两位小数
        String rate;
        if (total <= 0) {
            // 避免除以 0，total 为 0 或负都当作 0%
            rate = "0.00%";
        } else {
            BigDecimal bdCount = BigDecimal.valueOf(count);
            BigDecimal bdTotal = BigDecimal.valueOf(total);
            BigDecimal percent = bdCount
                    .divide(bdTotal, 4, RoundingMode.HALF_UP)  // 先算 4 位小数
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP);        // 再保留 2 位
            rate = percent.toPlainString() + "%";
        }

        // 4. 组装消息
        String msgTemplate = "总人数：%s人，参加人数：%s人，出勤率：%s （只统计系统内人员）";
        return String.format(msgTemplate, total, count, rate);
    }

    @RequestMapping(value = "/getMinutesDetail", method = RequestMethod.POST)
    @ApiOperation(value = "根据id获取会议纪要详情", notes = "根据id获取会议纪要详情",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = MeMinutesDetailDTO.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = MeMinutesDetailDTO.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingsId", value = "会议唯一标识", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public MeMinutesDetailDTO getMinutesDetail(String meetingsId) {
        return meetingsSV.getMinutesDetail(meetingsId);
    }

    @RequestMapping(value = "/switchReviewFlag", method = RequestMethod.POST)
    @ApiOperation(value = "更改‘会议是否需要审核’", notes = "更改‘会议是否需要审核’",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingsId", value = "会议唯一标识", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isNeedReview", value = "是否需要审核（true:是, false:否）", required = true, dataType = "boolean", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int switchReviewFlag(String meetingsId, boolean isNeedReview) throws GeneralException {
        Users user = getUser();
        Date sysDate = new Date();

        // 查询会议
        Meetings meetings = meetingsSV.getByPrimaryKey(meetingsId);
        if (null == meetings) {
            logger.error("参数信息有误：未查询到会议信息（ID=‘" + meetingsId + "’");
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR);
        }

        // 状态校验
        if (MeetingStatusEnum.FINISHED.getCode().equals(meetings.getStatus())
                || MeetingStatusEnum.CANCELED.getCode().equals(meetings.getStatus())) {
            logger.error("不能对【已完成】状态的会议/党课进行修改");
            throw new GeneralException(ME_STATUS_ERROR);
        }

        // 更新属性
        meetings.setModifiedby(user.getId());
        meetings.setModifieddate(sysDate);
        meetings.setExtfld1(isNeedReview ? SimpleDataEnum.MEETINGNEEDREVIEW.getCode() : SimpleDataEnum.MEETINGNOTNEEDREVIEW.getCode());
        // 是->否时，删除相关待办
        if (!isNeedReview) {
            workTaskSV.deleteByObjidAndType(meetingsId, user.getId(), sysDate, WorkTaskClassEnum.REVIEW.getCode());
        }

        return meetingsSV.updateMeetingsByPrimaryKey(meetings);
    }

    @RequestMapping(value = "/viewMeetingsQrCode")
    @ApiOperation(value = "根据会议id获取会议二维码预览或下载", notes = "根据会议id获取会议二维码预览或下载")
    public void viewMeetingsQrCode(String meetingsId, HttpServletRequest request, HttpServletResponse response) throws GeneralException {
        //获取会议信息
        Meetings meetings = meetingsSV.getByPrimaryKey(meetingsId);
        //获取会议二维码
        List<Attachments> qrList = attachmentsSV.getListByObjIdType(meetings.getId(), PbmsConstants.QR_BUS_ME_SIGN);
        //会议二维码url
        String qrUrl;
        //如果二维码存在则开始加载背景底图进行合成，否则就先创建二维码后再进行合成
        if (qrList.isEmpty()) {
            //二维码不存在，进行创建二维码
            qrUrl = createQrCode(meetings).getUrl();
        } else {
            qrUrl = qrList.get(0).getUrl();
        }

        //合成并输出图片
        InputStream qrInput = null;
        InputStream bgInputStream = null;
        try {
            //从onest获取二维码文件流
            qrInput = OnestUtil.getInputStreamByONestUrl(qrUrl);
            //读取二维码图像
            BufferedImage qrImg = ImageIO.read(qrInput);
            //读取背景底图
            bgInputStream = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX.concat(qr_me_bg));
//                File bgInputStream = new File("W:\\javaSpace\\pbms\\pbms-web\\src\\main\\resources\\logo\\qr_me_bg.png");
            BufferedImage bgImg = ImageIO.read(bgInputStream);
            //读取字体文件
//            String fontPath = "W:\\javaSpace\\pbms\\pbms-web\\src\\main\\resources\\pdfconfig\\fonts\\5simsun.ttf";
            String fontPath = FileUtil.checkFilePath("pdfconfig/fonts/", "simsun.ttf");
            fontPath = URLDecoder.decode(fontPath + "simsun.ttf", "utf-8");
            //实例化合成工具对象
            QrCodeUtil qrCodeUtils = new QrCodeUtil();
            //合成图像
            qrCodeUtils.compositePictureOfMigration(bgImg, qrImg, qrCodeUtils.getQr_bg_dev_x(), qrCodeUtils.getQr_bg_dev_y(), meetings.getTopic(), new File(fontPath));
            //处理图像为base64进行输出到客户端
//            ByteArrayOutputStream baos = new ByteArrayOutputStream();//io流

            String fileName = "会议二维码.png";

            response.setContentType("image/png");
            // 设置响应头
            response.setHeader("Content-Disposition", "attachment;filename=" + checkUserAgentSetFileName(fileName, request));
            //禁止图像缓存
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setDateHeader("Expires", 0);
            ImageIO.write(bgImg, "png", response.getOutputStream());//写入流中
//            byte[] bytes = baos.toByteArray();//转换成字节
//            BASE64Encoder encoder = new BASE64Encoder();
//            String png_base64 = encoder.encodeBuffer(bytes).trim();//转换成base64串
//            png_base64 = png_base64.replaceAll("\n", "").replaceAll("\r", "");//删除 \r\n
//            return "data:image/png;base64,".concat(png_base64);

        } catch (IllegalArgumentException e) {
            logger.error("系统错误，合成会议二维码失败");
            logger.error(e.getMessage(), e);
            throw new GeneralException(QR_SIGN_SYNTHESIS);
        } catch (IOException e) {
            logger.error("系统错误，加载会议二维码失败");
            logger.error(e.getMessage(), e);
            throw new GeneralException(QR_SIGN_SYNTHESIS);
        } catch (Exception e) {
            logger.error("系统错误，合成会议二维码失败");
            logger.error(e.getMessage(), e);
            throw new GeneralException(QR_SIGN_SYNTHESIS);
        } finally {
            org.apache.commons.io.IOUtils.closeQuietly(bgInputStream);
            org.apache.commons.io.IOUtils.closeQuietly(qrInput);
        }

    }

    @RequestMapping(value = "/getQrCode", method = RequestMethod.POST)
    @ApiOperation(value = "根据会议id创建会议二维码并获取二维码链接", notes = "根据会议id创建会议二维码并获取二维码链接")
    public String getQrCode(String meetingsId) throws GeneralException {
        //获取会议信息
        Meetings meetings = meetingsSV.getByPrimaryKey(meetingsId);
        //创建二维码并返回二维码链接
        String qrurl = createQrCode(meetings).getUrl();
        qrurl = OnestUtil.subStringUrl(qrurl);
        return qrurl;
    }

    /**
     * @方法名：createQrCode
     * @方法作用：创建会议二维码
     * @方法参数：meetings
     * @返回结果：二维码附件信息
     * @作者：牛文钻
     * @日期：2019/1/15
     */
    private Attachments createQrCode(Meetings meetings) throws GeneralException {
        try {
            DictionaryItems qrMsgPostItem = dictionaryItemsSV.getByCodeAndDictcode("QR_MSG_POST_URL", "QR_PROPS");
            if (null == qrMsgPostItem || StringUtils.isBlank(qrMsgPostItem.getDescription())) {
                logger.error("没有加载到二维码链接，创建会议二维码失败");
                throw new GeneralException(QR_PROP_ERROR);
            }

            //设置二维码详细信息
            Map<String, Object> msgMap = new HashMap<>();
            msgMap.put("meetingType", meetings.getType());

            //创建会议二维码信息
            QrCodeService qrCodeService = new QrCodeService();

            //获取二维码信息ID
            String qrCodeId = UIDUtil.getUID();
            //合成二维码图片解析的内容信息
            String qrContent = qrMsgPostItem.getDescription().concat(qrCodeId);
            //上传到附件服务器保存二维码详细信息
            String qrMsgUrl = qrCodeService.saveQrMsgToOnest(qrCodeId, msgMap);
            //保存二维码详细信息
            saveQrMsg(meetings, qrCodeId, qrMsgUrl);

            InputStream logoInputStream = getClass().getClassLoader().getResourceAsStream(ResourceUtils.CLASSPATH_URL_PREFIX.concat(logo_me_sign));
            Attachments attachments = qrCodeService.createSignQrCode(qrContent, meetings, logoInputStream);
            attachmentsSV.insertSelective(attachments);
            logger.info("保存二维码地址完成，会议标识：".concat(meetings.getId()));
            return attachments;
        } catch (IllegalArgumentException e) {
            logger.error("系统错误，创建会议二维码失败");
            logger.error(e.getMessage(), e);
            throw new GeneralException(QR_SIGN_ERROR);
        } catch (IOException e) {
            logger.error("系统错误，创建会议二维码失败");
            logger.error(e.getMessage(), e);
            throw new GeneralException(QR_SIGN_ERROR);
        } catch (Exception e) {
            logger.error("系统错误，创建会议二维码失败");
            logger.error(e.getMessage(), e);
            throw new GeneralException(QR_SIGN_ERROR);
        }
    }

    private void saveQrMsg(Meetings meetings, String qrCodeId, String qrMsgUrl) {

        //设置二维码信息
        QrCodeMsg qrCodeMsg = new QrCodeMsg();
        //设置二维码ID
        qrCodeMsg.setId(qrCodeId);
        //设置二维码作用
        qrCodeMsg.setQrEffect(PbmsConstants.QR_EFFECT_SIGN);
        //设置内部二维码标示码
        qrCodeMsg.setOrgId(PbmsConstants.QRCODE_ORG_ID);
        //设置二维码对应的数据id
        qrCodeMsg.setQrIdentity(meetings.getId());
        //设置二维码所属功能，功能代码使用常量类
        qrCodeMsg.setQrFunction(PbmsConstants.QR_FUN_MEETING);
        //设置二维码信息附件路径
        qrCodeMsg.setQrMsgUrl(qrMsgUrl);

        qrCodeMsgSV.insert(qrCodeMsg);
    }

    @RequestMapping(value = "/getMeetingsArchiveAttachment", method = RequestMethod.POST)
    @ApiOperation(value = "根据会议id获取会议归档材料", notes = "根据会议id获取会议归档材料")
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public MeetingsArchiveAttachmentDTO getMeetingsArchiveAttachment(String meetingsId) {
        MeetingsArchiveAttachmentDTO archiveAttachmentDTO = new MeetingsArchiveAttachmentDTO();

        // 签到表
        archiveAttachmentDTO.setSignAttachment(attachmentsSV.getListByObjIdType(meetingsId, AttachTypeEnum.ME_SIGN.getType()));
        // 会议照片
        archiveAttachmentDTO.setPictureAttachment(attachmentsSV.getListByObjIdType(meetingsId, AttachTypeEnum.ME_PICTURE.getType()));
        // 会议纪要
        archiveAttachmentDTO.setMinutesAttachment(attachmentsSV.getListByObjIdType(meetingsId, AttachTypeEnum.ME_MINUTES.getType()));

        return archiveAttachmentDTO;
    }

    @RequestMapping(value = "/cancelMeeting", method = RequestMethod.POST)
    @ApiOperation(value = "会议取消", notes = "会议取消")
    @Log(title = "会议管理（取消）",businessType = BusinessType.OTHER)
    public int cancelMeeting(String meetingsId, String reason) throws GeneralException {
        Users user = getUser();
        if (StringUtils.isBlank(reason)) {
            logger.error("取消原因不能为空");
            throw new GeneralException(NEED_CANCEL_REASON);
        }
        Meetings meetings = meetingsSV.getByPrimaryKey(meetingsId);
        if (null == meetings) {
            logger.error(NO_MEETING_ERR_MSG);
            throw new GeneralException(NO_MEETING_ERROR);
        }
        Date sysDate = new Date();
        /*
        由原型得知：待召开，召开中的会议可以取消，所以，取消会议，需要：
        1、结束和该会议相关的所有待办任务
        2、更新会议信息
        3、推送msg
         */
        workTaskSV.doneByObjidAndType(meetingsId, ConvertUtil.meetingTypeToTaskType(meetings.getType()), SimpleDataEnum.DATADONE.getCode(), user.getId(), sysDate, null);
        // 为所有与会者推送msg
        genPushMsgToAllConvertioneer(meetings, user, sysDate);
        return meetingsSV.cancelMeetingsByPrimaryKey(meetingsId, reason, user.getId(), sysDate);
    }

    /**
     * 为所有与会者推送msg
     *
     * @param meetings
     * @param user
     * @param sysDate
     */
    private void genPushMsgToAllConvertioneer(Meetings meetings, Users user, Date sysDate) {
        //判断是否需要查询所有与会者
        Boolean searchAll = MEETING_TYPE_ONLINE.equals(meetings.getChannelType()) && PROCESS_TYPE_MAIN.equals(meetings.getDistributionType());
        // 查询所有与会者
        List<Conventioneer> conventioneerList = conventioneerSV.getConventioneerByMeetingsId(meetings.getId(), searchAll);
        PushMsg pushMsg;
        Map<String, Object> param = new HashMap<>(16);
        param.put("txt_businessType", "会议");
        param.put("txt_businessTheme", meetings.getTopic());
        param.put("txt_action", "取消");
        param.put("txt_businessModule", "三会一课");
        param.put("rspKey", "rspId005");
        Date shouldSendDate = comService.getNoticDate("rspId005");
        for (Conventioneer temp : conventioneerList) {

            pushMsg = new PushMsg();
            pushMsg.setId(UIDUtil.getUID());
            pushMsg.setMsgname("会议取消" + meetings.getTopic());
            pushMsg.setUserid(temp.getUserid());
            pushMsg.setRspId("rspId005");
            pushMsg.setAuthorid(user.getId());
            pushMsg.setTasktype(ConvertUtil.meetingTypeToTaskType(meetings.getType())); // 系统中三会一课模块，任务类型和推送消息系统的业务类型保持一致，后期如有变动，需注意
            pushMsg.setObjid(meetings.getId());
            pushMsg.setMsgtype(MSG_TYPE_IMPORT); // 消息重要程度
            pushMsg.setMsgstatus(MSG_STATUS_UNREAD);
            pushMsg.setIsdeleted(DataIsDeleteEnum.NORMAL.getCode());
            pushMsg.setCreatedby(user.getId());
            pushMsg.setCreateddate(sysDate);
            pushMsg.setShouldSendDate(shouldSendDate);

            pushMsgSV.insertMsg(pushMsg, param);
        }
    }

    @RequestMapping(value = "/updateMeetings", method = RequestMethod.POST)
    @ApiOperation(value = "会议修改（待计划/待通知/待召开）|新增", notes = "会议修改保存（待计划/待通知/待召开）|新增")
    @Log(title = "会议管理（会议修改 |新增 ）",businessType = BusinessType.UPDATE)
    public int updateMeetings(VMeetingsModifyBean meetingsBean, Integer meetingType) throws Exception {
        //meetingType 会议类型 10：支部党员大会，20：支部委员会，30：党小组会，40：党课，50：其他任务，60：视频会议 70：主题党日
        Set<ConstraintViolation<VMeetingsModifyBean>> constraintViolations = validator.validate(meetingsBean);
        // 如果校验不通过，则结果集中会保存每一条校验失败的信息，判断结果集长度即可知道是否通过校验
        if (!constraintViolations.isEmpty()) {
            logger.error(constraintViolations.iterator().next().getMessage());
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR, constraintViolations.iterator().next().getMessage());
        }

        Users user = getUser();
        Date sysDate = new Date();

        //判断有没有粤政易id 没有的不允许操作
        if (0 == meetingsBean.getIsSave() && StringUtils.isBlank( user.getYzyId())) {
            throw new GeneralException("当前用户没有粤政易id,不允许操作！","当前用户没有粤政易id,不允许操作！");
        }
        // 新增/更新
        //根据id查不出来可以认为是新增

        if (StringUtils.isBlank(meetingsBean.getMeetingsId()) || meetingsBean.getIsAdd() == 1) { // 新增
            return addMeeting(meetingsBean, user, meetingType, sysDate);
        } else { // 更新
            Meetings meetings = meetingsSV.getByPrimaryKey(meetingsBean.getMeetingsId());
//            // 参数具体校验
//            if (MeetingTypeEnum.BRANCHLEADER.getCode().equals(meetingType) && StringUtils.isBlank(meetingsBean.getRecorder())) {
//                logger.error("记录员不能为空");
//                throw new GeneralException("PBMS_ME_1034");
//            }


            // 会议组织者
            List<UserRoleForMeDTO> leaderIds = userSV.getUserPMIdsByOrgid(meetings.getOrgid(), RoleTypeEnum.WORKER.getCode());

            if (null == meetings || !meetingType.equals(meetings.getType())) {
                logger.error("请求参数错误");
                throw new GeneralException(MSG_REQUEST_PARAMS_ERROR);
            }
            if (MEETING_TIMELINESS_SUPPLY.equals(meetingsBean.getTimelinessType())
                    && !MeetingStatusEnum.TOBEPLAN.getCode().equals(meetings.getStatus())
                    && !MeetingStatusEnum.TOBENOTIFY.getCode().equals(meetings.getStatus())) {
                logger.error("只有待计划/待通知状态的会议，才可以变更为补录");
                throw new GeneralException(NOT_BE_SUPPLY);
            }

            if (MeetingNewStatusEnum.TOBEPLAN.getCode().equals(meetings.getStatus())) { // 待计划
                // 1、结束该会议"制定计划"的待办任务
                workTaskSV.doneByObjidAndType(meetings.getId(), ConvertUtil.meetingTypeToTaskType(meetingType),
                        SimpleDataEnum.DATADONE.getCode(), user.getId(), sysDate, WorkTaskClassEnum.MAKEPLAN.getCode());

                meetingsSV.updateWorkTask(meetings, leaderIds, sysDate, WorkTaskClassEnum.NOTIFY.getCode(), ConvertUtil.meetingTypeToTaskType(meetingType), user.getId());
                if (Integer.valueOf(1).equals(meetingsBean.getIsSave())){
                    meetings.setStatus(MeetingNewStatusEnum.TOBEPLAN.getCode()); // 保存
                }else {
                    meetings.setStatus(MeetingNewStatusEnum.SCHEMEREVIEW.getCode()); // 进入到‘方案审核’流程
                    // 生成与会者
                    // 通知是为组织下所有人员发送待办。客观情况所有人包含领导和组织者，都有党员角色，所以只发送党员角色。
                    List<Users> users = meetingsSV.getConventioneerListForMe(meetings);
                    conventioneerSV.insertConventioneerInBatch(meetings, users, user.getId(), sysDate, new ArrayList<>(), 0, 0);
                }

                meetings.setIserror((short) SimpleDataEnum.MEETINGERRORYES.getCode().intValue());//解除会议异常状态

            }
            else if (MeetingStatusEnum.TOBECONVENE.getCode().equals(meetings.getStatus())) { // 待召开会议修改时，发送推送消息告知与会者
                if (MeetingTypeEnum.BRANCHLEADER.getCode().equals(meetingType)) {
                    // “委员会”的记录员修改时，删除原任务待办和与会者信息，生成新的任务待办和与会者信息
                    if (!meetingsBean.getRecorder().equals(meetings.getRecorder())) {
                        // 通知时发送的待办，只给【党员】角色发送，该出加此判断
                        List<String> roleIdList = roleSV.getIdsByRoleType(RoleTypeEnum.PARTIER.getCode());
                        if (null == roleIdList || roleIdList.isEmpty() || roleIdList.size() > 1) {
                            logger.error("系统错误：未查询到【党员】角色，请联系管理员");
                            throw new GeneralException(SYS_NO_PARTIER_ROLE);
                        }
                        updateForWYH(meetingsBean, meetingType, WorkTaskClassEnum.ATTENDANCE.getCode(), user, sysDate, meetings, roleIdList.get(0));
                    }
                }
                else {
                    Boolean searchAll = false;
                    if ("02".equals(meetings.getChannelType()) && "01".equals(meetings.getDistributionType())) {
                        searchAll = true;
                    }
                    List<Conventioneer> conventioneerList = conventioneerSV.getConventioneerByMeetingsId(meetings.getId(), searchAll);
                    List<String> userIdList = new ArrayList<>(10);
                    for (Conventioneer temp : conventioneerList) {
                        userIdList.add(temp.getUserid());
                    }
                    if (!CollectionUtils.isEmpty(userIdList)) {
                        // 生成消息推送
                        Map<String, Object> param = new HashMap<>(16);
                        param.put("txt_businessTheme", meetings.getTopic());
                        param.put("txt_action", "发生变化");

                        if (MeetingTypeEnum.STUDY.getCode() == meetings.getType()) {
                            param.put("txt_businessType", "集中学习");
                            param.put("txt_businessModule", "集中学习");
                        } else {
                            param.put("txt_businessType", "会议");
                            param.put("txt_businessModule", "三会一课");
                        }
                        param.put("rspKey", "rspId005");
                        pushMsgSV.insertMsgInBatch(meetings.getId(), userIdList, user.getId(), comService.getNoticDate("rspId005"),
                                "【会议变更】" + meetings.getTopic(), MSG_TYPE_IMPORT, meetingPlanSV_TYPE_MEETING, param);
                    }
                }

            }
            else if (!MeetingStatusEnum.TOBENOTIFY.getCode().equals(meetings.getStatus())) {
                logger.error("会议状态异常：只有【待计划】|【待通知】|【待召开】状态，才可以进行修改处理");
                throw new ValidationException("PBMS_MC_0001");
            }
            //更新分发流程关系,不可以放到参数处理方法后面，参数处理会丢失orgid
            //如果是线上会议并且是主流程，创建分发流程支部关系，并且会议阶段为待召开之前（待召开及之后状态不允许变更分发流程关系）
            if (MEETING_TYPE_ONLINE.equals(meetingsBean.getChannelType()) && PROCESS_TYPE_MAIN.equals(meetingsBean.getDistributionType()) && MeetingStatusEnum.TOBECONVENE.getCode() > meetings.getStatus()) {
                createProcessRelation(meetings.getOrgid(), meetings.getMainProcessId(), meetingsBean.getProcessIds());
            } else {
                //如果不是线上会议则查询是否存在分发流程关系，并删除分发关系
                List<String> processOrgList = processRelationSV.selectIdsByMainProcessId(meetings.getId());

                if (!processOrgList.isEmpty()) {
                    processRelationSV.deleteByCollect(processOrgList);
                }
            }

            // 更新短信提醒设置
            MeetingMsgSetting meetingMsgSetting = meetingMsgSettingSV.getByObjTypeAndObjId(NOTICE_MSG_OBJ_TYPE, meetings.getId());

            VMeetingsNotifyBean notifyBean;
            if (null == meetingsBean.getIsMeetingNotice()
                    && null == meetingsBean.getIsRecordSubmit()
                    && null == meetingsBean.getIsMeetingHold()) {
                notifyBean = null;
            } else {
                notifyBean = new VMeetingsNotifyBean();
                notifyBean.setIsMeetingNotice(meetingsBean.getIsMeetingNotice());
                notifyBean.setMeetingNoticeStart(meetingsBean.getMeetingNoticeStart());
                notifyBean.setMeetingNoticeInterval(meetingsBean.getMeetingNoticeInterval());
                notifyBean.setIsRecordSubmit(meetingsBean.getIsRecordSubmit());
                notifyBean.setSubmitNoticeStart(meetingsBean.getSubmitNoticeStart());
                notifyBean.setSubmitNoticeInterval(meetingsBean.getSubmitNoticeInterval());
                notifyBean.setIsMeetingHold(meetingsBean.getIsMeetingHold());
                notifyBean.setHoldNoticeInterval(meetingsBean.getHoldNoticeInterval());
            }
            createMsgSetting(notifyBean, user, sysDate, meetings, meetingMsgSetting, meetingsBean.getNeedRecord());

            // 更新用户接收提醒短信与否的暂存信息
            genMsgTempinfo(meetingsBean, meetings, user.getId(), sysDate);

            // 参数处理
            dealParamForModifyMeeting(meetings, meetingsBean, user.getId(), sysDate);

            //更新关联的任务
            List<String> taskIds = new ArrayList<>();
            if (StringUtils.isNotBlank(meetingsBean.getTaskIds())) {
                String[] ids = meetingsBean.getTaskIds().split(",");
                taskIds = Arrays.asList(ids);
                relatedTasksSaveAndUpdate(taskIds, meetingsBean.getMeetingsId());
            }

            //更新集中学习类型
            if (meetingsBean.getStudyType() != null) {
                meetings.setExtfld2(meetingsBean.getStudyType());
            }
            //判断如果是支部委员会则需要创建发短信的列席党员
//            if (meetingType == 20 || meetingType == 40) {
//            setAttendances(meetingsBean.getMeetingsId(), meetingsBean.getAttendancesList(), user.getId(), sysDate);
//            }
            meetings.setTheme(meetingsBean.getTheme());

            int result =  meetingsSV.updateMeetingsByPrimaryKey(meetings);
            if (result > 0 &&  0 == meetingsBean.getIsSave()) {
                //生成一个会议材料pdf用来提交
                genteatePdf(meetings.getId());
                submitToApproveProcess(meetings, user, null, ApprovalProcessServiceOAImpl.BUSINESS_TYPE_ACTIVITY_PLANNING);
            }
            return result;
        }
    }

    @RequestMapping(value = "/updateMeetingBasicInfo", method = RequestMethod.POST)
    @ApiOperation(value = "更新会议基本信息", notes = "更新会议基本信息，不涉及状态变更，不限制状态，不更新状态")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @Log(title = "会议管理（基本信息更新）", businessType = BusinessType.UPDATE)
    public int updateMeetingBasicInfo(@Validated VMeetingsBasicUpdateBean basicUpdateBean) throws Exception {
        // 参数校验
        Set<ConstraintViolation<VMeetingsBasicUpdateBean>> constraintViolations = validator.validate(basicUpdateBean);
        if (!constraintViolations.isEmpty()) {
            logger.error(constraintViolations.iterator().next().getMessage());
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR, constraintViolations.iterator().next().getMessage());
        }

        Users user = getUser();
        Date sysDate = new Date();

        // 查询会议信息进行基本校验
        Meetings existingMeeting = meetingsSV.getByPrimaryKey(basicUpdateBean.getMeetingsId());
        if (existingMeeting == null) {
            logger.error("未找到指定的会议信息");
            throw new GeneralException(NO_MEETING_ERROR);
        }

        // 调用服务层方法更新基本信息
        int result = meetingsSV.updateMeetingBasicInfo(
                basicUpdateBean.getMeetingsId(),
                basicUpdateBean,
                user.getId(),
                sysDate
        );

        if (result > 0) {
            logger.info("会议基本信息更新成功，会议ID：{}", basicUpdateBean.getMeetingsId());
        } else {
            logger.warn("会议基本信息更新失败，会议ID：{}", basicUpdateBean.getMeetingsId());
        }

        return result;
    }

    private void submitToApproveProcess(Meetings meetings, Users user, List<Attachments> attachmentList, String businessType)  {
        // 参数校验
        if (meetings == null || meetings.getId() == null) {
            throw new IllegalArgumentException("Meetings or meetings.id cannot be null");
        }

        String businessID = meetings.getId();
        String fileTitle = meetings.getTopic();
        String content = null;
        //当前用户的粤政易id
        String draftUser = getUser().getYzyId();
        Attachments documentFilePath = null;
        List<Attachments> documents = null;
        // 获取文档  方案审批-生成的 ME_PLAN_OA 记录审批-照片 材料 签到表 会议记录
        if (businessType == ApprovalProcessServiceOAImpl.BUSINESS_TYPE_ACTIVITY_PLANNING){
            documents = attachmentsSV.getListByObjIdType(meetings.getId(), AttachTypeEnum.ME_PLAN_OA.getType());
            //
            log.info("#提交会议：submitToApproveProcess meetings.getId() = {} and AttachTypeEnum.ME_MEETING.getType() = {} and documents.size = {}",
                    meetings.getId(), AttachTypeEnum.ME_PLAN_OA.getType(),documents == null?"null":documents.size());
        }else if (businessType == ApprovalProcessServiceOAImpl.BUSINESS_TYPE_ACTIVITY_RECORD){
            //正文 会议记录
            documents = attachmentsSV.getListByObjIdType(meetings.getId(), AttachTypeEnum.ME_MINUTES.getType());

            //附件
            List<Attachments> listByObjId = attachmentsSV.getListByObjId(meetings.getId());
            Set<String> allowedTypes =  new HashSet<>(Arrays.asList(
                    AttachTypeEnum.ME_ACTIVITY.getType(), //会议材料
                    AttachTypeEnum.ME_SIGN.getType(),
                    AttachTypeEnum.ME_PICTURE.getType()
            ));
            attachmentList = listByObjId.stream()
                    .filter(attachment -> allowedTypes.contains(attachment.getAtttype()))
                    .collect(Collectors.toList());
        }

        // 设置文档路径
        if (!CollectionUtils.isEmpty(documents)) {
            Attachments document = documents.get(0);
            documentFilePath = document;
        }



        // 提交审批
        RespOAApproveSubmitDataBean data = approvalProcessServiceOA.submit(
                businessID,
                businessType,
                fileTitle,
                content,
                draftUser,
                documentFilePath,
                attachmentList
        );
        logger.info("Approval process submission result ："+JSON.toJSON(data));
    }

    /**
     * 更新用户接收提醒短信与否的暂存信息
     *
     * @param meetingsBean
     * @param meetings
     * @param currUserId
     * @param sysDate
     * @throws GeneralException
     */
    private void genMsgTempinfo(VMeetingsModifyBean meetingsBean, Meetings meetings, String currUserId, Date sysDate) throws GeneralException {
        List<Users> users;
        if (MeetingTypeEnum.BRANCHLEADER.getCode().equals(meetings.getType())) {
            // 查询党员角色信息
            List<String> roleIds = roleSV.getIdsByRoleType(RoleTypeEnum.PARTIER.getCode());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(roleIds) || roleIds.size() > 1) {
                logger.error("系统无党员角色或党员角色不唯一");
                throw new GeneralException("PBMS_UC_0002");
            }
            String roleIdForPartier = roleIds.get(0);
            users = userSV.getLeaderByOrgCode(null, null, meetings.getOrgcode(), roleIdForPartier, meetings.getOrgid());
        } else {
            List<Integer> roleTypes = new ArrayList<>(10);
            roleTypes.add(RoleTypeEnum.PARTIER.getCode());
            if (MEETING_TYPE_ONLINE.equals(meetings.getChannelType()) && PROCESS_TYPE_MAIN.equals(meetings.getDistributionType())) {
                List<SelectTreeDTO> orgList = organizationSV.getOrgByMainProcessIdHasMain(meetings.getId());
                Set<String> orgcodes;
                if (CollectionUtils.isEmpty(orgList)) {
                    orgcodes = null;
                } else {
                    orgcodes = new HashSet<>(16);
                    for (SelectTreeDTO temp : orgList) {
                        orgcodes.add(temp.getValue());
                    }
                }
                users = userSV.getListByOrgNameAndRoleAndOrgCodes(null, null, roleTypes, orgcodes); // 党员列表
            } else {
                users = userSV.getUserByOrgCodeAndRoleAndUserName(null, null, roleTypes, MEETING_TYPE_ONLINE.equals(meetings.getChannelType()) ? null : meetings.getOrgcode()); // 党员列表
            }
        }

        // 更新用户接收提醒短信与否的暂存信息
        List<String> noMsgUserList = new ArrayList<>(10);
        if (StringUtils.isNotBlank(meetingsBean.getNoMsgUser())) {
            noMsgUserList = Arrays.asList(meetingsBean.getNoMsgUser().trim().split(","));
        }

        List<NoticeMsgTempinfo> oldTempinfoList = noticeMsgTempinfoSV.getListByMeetingId(meetings.getId());
        Map<String, NoticeMsgTempinfo> oldUserMap = new HashMap<>(16);
        for (NoticeMsgTempinfo temp : oldTempinfoList) {
            oldUserMap.put(temp.getUserId(), temp);
        }

        List<NoticeMsgTempinfo> newList = new ArrayList<>(10);

        for (Users temp : users) {
            if (oldUserMap.containsKey(temp.getId())) {
                // 更新
                NoticeMsgTempinfo tempinfo = oldUserMap.get(temp.getId());
                tempinfo.setIsReceiveMsg(noMsgUserList.contains(temp.getId()) ? 1 : 0);
                tempinfo.setModifieddate(sysDate);
                tempinfo.setModifiedby(currUserId);
                noticeMsgTempinfoSV.updateByPrimaryKey(tempinfo);
            } else {
                // 新建
                NoticeMsgTempinfo tempinfo = new NoticeMsgTempinfo();
                tempinfo.setId(UIDUtil.getUID());
                tempinfo.setMeetingId(meetings.getId());
                tempinfo.setUserId(temp.getId());
                tempinfo.setIsReceiveMsg(noMsgUserList.contains(temp.getId()) ? 1 : 0);
                tempinfo.setIsDeleted(0);
                tempinfo.setCreatedby(currUserId);
                tempinfo.setCreateddate(sysDate);
                newList.add(tempinfo);
            }
        }

        if (CollectionUtils.isNotEmpty(newList)) {
            noticeMsgTempinfoSV.insertBatch(newList);
        }
    }

    /**
     * 根据会议ID修改直播开始时间和状态
     *
     * @param meetingsId
     */
    @RequestMapping(value = "/onlineStart", method = RequestMethod.POST)
    @ApiOperation(value = "线上直播开始", notes = "线上直播开始’",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingsId", value = "会议唯一标识", required = true, dataType = "String", paramType = "query")})
    public int onlineStart(String meetingsId) {

        Meetings me = meetingsSV.getByPrimaryKey(meetingsId);

        int startnum = 0;

        Integer meetingStartIng = 300;  //会议召开中状态

        //判断会议是否已经为召开中状态，如果是召开中状态则不进行任何操作，否则修改会议状态并创建相关待办
        if (meetingStartIng > me.getStatus()) {
            Meetings meetings = new Meetings();
            meetings.setId(meetingsId);
            meetings.setMainProcessId(meetingsId);
            meetings.setStatus(meetingStartIng);
            meetings.setActualStartTime(new Date());

            startnum = meetingsSV.updateByPrimaryKeySelective(meetings);

            //结束出勤待办
            overWorkTask(meetingsId, WorkTaskClassEnum.ATTENDANCE.getCode());

            //修改参会人员确认状态
            conventioneerSV.setConventioneerJoinStatusByMainProcessId(meetingsId);

            //创建会议记录待办
            createWorkTask(meetingsId, WorkTaskClassEnum.RECORD.getCode());
        }

        return startnum;
    }

    private void overWorkTask(String mainProcessId, Integer taskType) {
        List<String> meetingIds = meetingsSV.selectMeetingIdsByMainProcessId(mainProcessId);
        Map<String, Object> params = new HashMap<>();
        params.put("type", taskType);
        params.put("objids", meetingIds);
        List<String> taskIds = workTaskSV.selectAgencyIdsByObjId(params);

        Map<String, Object> doneParams = new HashMap<>();
        doneParams.put("taskstatus", 1);
        doneParams.put("modifiedby", getUser().getId());
        doneParams.put("modifieddate", new Date());
        doneParams.put("tasktype", taskType);
        doneParams.put("ids", taskIds);
        workTaskSV.doneByIdsAndType(doneParams);

    }

    private void createWorkTask(String mainProcessId, Integer taskType) {
        List<Meetings> meetingsList = meetingsSV.selectMeetingsByMainProcessId(mainProcessId);
        //获取党员角色ID
        String roleId = roleSV.getIdsByRoleType(RoleTypeEnum.PARTIER.getCode()).get(0);
        Date sysDate = new Date();

        List<WorkTask> workTaskList = new ArrayList<>();
        for (int i = 0; i < meetingsList.size(); i++) {
            Meetings me = meetingsList.get(i);
            List<MeetingConventioneerDTO> conventioneerList = conventioneerSV.getConventioneerListByMeetingId(me.getId());
            for (int j = 0; j < conventioneerList.size(); j++) {

                WorkTask workTask = new WorkTask();
                workTask.setId(UIDUtil.getUID());
                workTask.setObjid(me.getId());
                workTask.setSubobjid(null);
                workTask.setTaskname(me.getTopic());
                workTask.setSummary(me.getDescription());
                workTask.setUserid(conventioneerList.get(j).getUserId());
                workTask.setTasktype(ConvertUtil.meetingTypeToTaskType(me.getType()));
                workTask.setType(taskType);
                workTask.setTaskstatus(TASK_STATUS_TODO);
                workTask.setIsdeleted(DataIsDeleteEnum.NORMAL.getCode());
                workTask.setCreatedby(getUser().getId());
                workTask.setCreateddate(sysDate);
                workTask.setModifiedby(null);
                workTask.setModifieddate(null);
                workTask.setUserRole(roleId);

                workTaskList.add(workTask);
            }

        }

        workTaskSV.insertCollection(workTaskList);

    }

    @RequestMapping(value = "/onlineEnd", method = RequestMethod.POST)
    @ApiOperation(value = "线上直播结束", notes = "线上直播结束’",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingsId", value = "会议唯一标识", required = true, dataType = "String", paramType = "query")})
    public int onlineEnd(String meetingsId) {

        Meetings meetings = new Meetings();
        meetings.setId(meetingsId);
        meetings.setMainProcessId(meetingsId);
        meetings.setStatus(400);
        meetings.setActualEndTime(new Date());

        return meetingsSV.updateByPrimaryKeySelective(meetings);
    }

    private void updateForWYH(VMeetingsModifyBean meetingsBean, Integer meetingType, Integer meetingStatus,
                              Users user, Date sysDate, Meetings meetings, String roleid) {
        WorkTask workTask = workTaskSV.getWorkTaskByObjIdAndTaskTypeAndType(meetings.getId(),
                ConvertUtil.meetingTypeToTaskType(meetingType), meetingStatus, meetings.getRecorder(), roleid);
        Users recorder = userSV.getByPrimaryKey(meetings.getRecorder());
        if (null != workTask) {
            // 删除
            workTaskSV.deleteByIdLogic(workTask.getId());
            // 生成
            workTask.setId(UIDUtil.getUID());
            workTask.setUserid(meetingsBean.getRecorder());
            workTask.setCreatedby(user.getId());
            workTask.setCreateddate(sysDate);
            workTask.setUserRole(roleid);
            workTask.setModifiedby(null);
            workTask.setModifieddate(null);
            workTask.setUserRole(recorder.getCurrentRoleId());
            workTaskSV.insertSelective(workTask);
        }

        Conventioneer conventioneer = conventioneerSV.getConventioneerByUnionId(meetings.getId(), meetings.getRecorder());
        if (null != conventioneer) {
            // 删除
            conventioneerSV.deleteByIdLogic(conventioneer.getId());
            // 生成
            conventioneer.setId(UIDUtil.getUID());
            conventioneer.setUserid(meetingsBean.getRecorder());
            conventioneer.setCreatedby(user.getId());
            conventioneer.setCreateddate(sysDate);
            conventioneer.setModifiedby(null);
            conventioneer.setModifieddate(null);
            conventioneerSV.insertConventioneer(conventioneer);
        }
    }

    @RequestMapping(value = "/notifyMeetings", method = RequestMethod.POST)
    @ApiOperation(value = "会议通知", notes = "会议通知",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingsId", value = "会议ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "noMsgUser", value = "不接收短信的用户ID列表(逗号分隔)", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "timelinessType", value = "会议时效性：01-实时，02-补录", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "needReacord", value = "是否需要参会人员提交会议记录：0-否，1-是", required = false, dataType = "Integer", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int notifyMeetings(String meetingsId, String noMsgUser, String timelinessType, Integer needReacord) throws GeneralException {
        Users user = getUser();
        Date sysDate = new Date();
        // 1、更新会议状态
        Meetings meetings = meetingsSV.getByPrimaryKey(meetingsId);

        // 状态校验
        if (MeetingStatusEnum.TOBENOTIFY.getCode() != meetings.getStatus()) {
            logger.error("只有【待通知】状态的会议/党课，才可以发送通知");
            throw new GeneralException(SHOULD_TO_BE_NOTIFY);
        }

        VMeetingsNotifyBean meetingBean = new VMeetingsNotifyBean();
        meetingBean.setTimelinessType(timelinessType);
        meetingBean.setNeedRecord(needReacord);
        return notify(meetings, user, sysDate, getNoMsgUserList(noMsgUser), meetingBean);
    }

    private List<String> getNoMsgUserList(String noMsgUser) {
        List<String> noMsgUserList = new ArrayList<>(10);
        if (StringUtils.isNotBlank(noMsgUser)) {
            String[] userIdArr = noMsgUser.split(",");
            noMsgUserList = Arrays.asList(userIdArr);
        }
        return noMsgUserList;
    }

    /**
     * 如果是线上会议则为各支部创建分发流程
     */
    private int createDistribute(Meetings meetings, Users user, Date sysDate, List<String> noMsgUserList, Integer isMeetingNotice, Integer isRecordSubmit) throws GeneralException {

        //获取所有党支部，创建分发流程
        List<SelectTreeDTO> organizationList = organizationSV.selectOrgByMainProcessId(meetings.getMainProcessId());
        int successCount = 0;

        String mainOrg = meetings.getOrgid();
        String mainModerator = meetings.getModerator();

        //获取支部党务工作者角色ID
        List<String> roleIdList = roleSV.getIdsByRoleType(RoleTypeEnum.WORKER.getCode());
        List<WorkTask> workTaskCollect = new ArrayList<>();

        //存储需要修改的分发流程会议关系

        Map<String, Object> roleParams = new HashMap<>();
        roleParams.put("roleIds", roleIdList);

        String baseUid = UIDUtil.getUID(8);
        int maxUidIndex = 99999;
        int uidIndex = 0;

        for (SelectTreeDTO organization : organizationList) {
            //获取当前组织的分发流程对象
            ProcessRelation processRelation = processRelationSV.selectByMainProcessIdAndDisOrgid(meetings.getMainProcessId(), organization.getId());
            //设置当前分发流程对象的组织信息
            processRelation.setDisOrgcode(organization.getValue());
            processRelation.setDisOrgname(organization.getName());

            if (!organization.getId().equals(mainOrg)) {
                if (uidIndex >= maxUidIndex) {
                    uidIndex = 0;
                    baseUid = UIDUtil.getUID(8);
                }
                uidIndex++;
                meetings.setId(UIDUtil.setUid(baseUid, uidIndex));
                meetings.setOrgid(organization.getId());
                meetings.setOrgcode(organization.getValue());
                meetings.setOrgname(organization.getName());
                meetings.setDistributionType("02");

                int createDisProcess = meetingsSV.insertSelective(meetings);

                //判断是否存在当前组织的分发流程关系，如果存在则设置相关的修改信息
                if (null != processRelation.getId()) {
                    if (createDisProcess > 0) {
                        //如果分发流程创建成功，设置当前组织的分发流程为已创建状态，并设置分发会议ID，01未创建，02已创建
                        processRelation.setCreated("02");
                        processRelation.setDisProcessId(meetings.getId());
                    } else {
                        //如果分发流程创建失败，设置当前组织的分发流程未创建，并说明原因，01未创建，02已创建
                        processRelation.setCreated("01");
                        processRelation.setRemmark("创建分发会议失败");
                    }
                    //执行分发流程保存
                    processRelationSV.updateByPrimaryKeySelective(processRelation);
                }

                successCount += doNotify(meetings, user, sysDate, noMsgUserList, isMeetingNotice, isRecordSubmit);

                roleParams.put("orgid", meetings.getOrgid());

                //获取当前分发流程需要创建待办的工作者
//                List<UserRole> userRoleList = userRoleSV.getByOrgAndRole(roleId, );
                List<Users> usersList = userSV.selectByRoleAndOrgId(roleParams);
                //循环创建工作者待办
                for (Users users : usersList) {
                    //判断当前人是否主流程负责人，防止重复创建待办
                    if (!mainModerator.equals(users.getId())) {
                        if (uidIndex >= maxUidIndex) {
                            uidIndex = 0;
                            baseUid = UIDUtil.getUID(8);
                        }
                        uidIndex++;
                        // 生成待办任务
                        WorkTask disWorkTask = new WorkTask();
                        disWorkTask.setId(UIDUtil.setUid(baseUid, uidIndex));
                        disWorkTask.setObjid(meetings.getId());
                        disWorkTask.setSubobjid(null);
                        disWorkTask.setTaskname(meetings.getTopic());
                        disWorkTask.setSummary(meetings.getDescription());
                        disWorkTask.setUserid(users.getId());
                        disWorkTask.setTasktype(ConvertUtil.meetingTypeToTaskType(meetings.getType()));
                        disWorkTask.setType(WorkTaskClassEnum.NOTIFY.getCode());
                        disWorkTask.setTaskstatus(TASK_STATUS_TODO);
                        disWorkTask.setIsdeleted(DataIsDeleteEnum.NORMAL.getCode());
                        disWorkTask.setCreatedby(user.getId());
                        disWorkTask.setCreateddate(sysDate);
                        disWorkTask.setModifiedby(null);
                        disWorkTask.setModifieddate(null);
                        disWorkTask.setUserRole(users.getCurrentRoleId());
                        workTaskCollect.add(disWorkTask);
                    }
                }

            } else {
                //判断是否存在当前组织的分发流程关系，如果存在则设置相关的修改信息
                if (null != processRelation.getId()) {
                    //如果分发流程创建成功，设置当前组织的分发流程为已创建状态，并设置分发会议ID，01未创建，02已创建
                    processRelation.setCreated("02");
                    processRelation.setDisProcessId(meetings.getId());
                    //执行分发流程保存
                    processRelationSV.updateByPrimaryKeySelective(processRelation);
                }
            }
        }

        if (!workTaskCollect.isEmpty()) {
            workTaskSV.insertCollection(workTaskCollect);
        }

        return successCount;
    }

    /**
     * 会议通知
     *
     * @param meetings 会议ID
     * @param user
     * @param sysDate
     * @return
     * @throws GeneralException
     */
    private int notify(Meetings meetings, Users user, Date sysDate, List<String> noMsgUserList, VMeetingsNotifyBean meetingBean) throws GeneralException {
        meetings.setStatus((
                null != meetingBean && MEETING_TIMELINESS_SUPPLY.equals(meetingBean.getTimelinessType())) ?
                MeetingStatusEnum.TOBIEHOLD.getCode() : MeetingStatusEnum.TOBECONVENE.getCode());
        meetings.setModifiedby(user.getId());
        meetings.setModifieddate(sysDate);

        // 更新会议异常状态
        if (MEETING_ISPLAN_YES.equals(meetings.getIsplan())) {
            meetings.setIserror((short) SimpleDataEnum.MEETINGERRORYES.getCode().intValue());
        }

        // 更新短信提醒设置
        MeetingMsgSetting meetingMsgSetting = meetingMsgSettingSV.getByObjTypeAndObjId(NOTICE_MSG_OBJ_TYPE, meetings.getId());
        MeetingMsgSetting newMsgSetting = createMsgSetting(meetingBean, user, sysDate, meetings, meetingMsgSetting, (null != meetingBean && null != meetingBean.getNeedRecord()) ? meetingBean.getNeedRecord() : 1);

        // 如果当前会议开展形式为线上，且是主流程时则先创建相应的分发流程
        if (MEETING_TYPE_ONLINE.equals(meetings.getChannelType()) && PROCESS_TYPE_MAIN.equals(meetings.getDistributionType())) {
            // 先执行主流程通知待办
            int success = doNotify(meetings, user, sysDate, noMsgUserList, newMsgSetting.getIsMeetingNotice(), newMsgSetting.getIsRecordSubmit());
            success += createDistribute(meetings, user, sysDate, noMsgUserList, newMsgSetting.getIsMeetingNotice(), newMsgSetting.getIsRecordSubmit());
            return success;
        } else {
            //如果不是线上主流程会议则查询是否有分发流程关系，如果有就删除
            List<String> processOrgList = processRelationSV.selectIdsByMainProcessId(meetings.getId());
            if (!processOrgList.isEmpty()) {
                processRelationSV.deleteByCollect(processOrgList);
            }

            return doNotify(meetings, user, sysDate, noMsgUserList, newMsgSetting.getIsMeetingNotice(), newMsgSetting.getIsRecordSubmit());
        }
    }

    /**
     * 执行通知操作
     *
     * @param meetings
     * @param user
     * @param sysDate
     * @return
     * @throws GeneralException
     */
    private int doNotify(Meetings meetings, Users user, Date sysDate, List<String> noMsgUserList, Integer isMeetingNotice, Integer isRecordSubmit) throws GeneralException {
        // 3、为通知范围内所有个体，生成待办（待确认）
        // 通知是为组织下所有人员发送待办。客观情况所有人包含领导和组织者，都有党员角色，所以只发送党员角色。
        List<Users> users = meetingsSV.getConventioneerListForMe(meetings);
        if (!CollectionUtils.isEmpty(users)) {
            if (MEETING_TIMELINESS_SUPPLY.equals(meetings.getTimelinessType())) {
                // 生成新的待办（提交记录）
                workTaskSV.insertTaskInBatch(meetings, users, user.getId(), sysDate,
                        "【提交记录】" + meetings.getTopic(), meetings.getDescription(), WorkTaskClassEnum.RECORD.getCode());
            } else {
                // 生成新的待办（与会）
                workTaskSV.insertTaskInBatch(meetings, users, user.getId(), sysDate,
                        "【会议出席】" + meetings.getTopic(), meetings.getDescription(), WorkTaskClassEnum.ATTENDANCE.getCode());

            }
            // 生成与会者
            conventioneerSV.insertConventioneerInBatch(meetings, users, user.getId(), sysDate, noMsgUserList, isMeetingNotice, isRecordSubmit);
        }

        //结束会议通知代办
        workTaskSV.doneByObjidAndType(meetings.getId(), ConvertUtil.meetingTypeToTaskType(meetings.getType()), SimpleDataEnum.DATADONE.getCode(), user.getId(), sysDate, WorkTaskClassEnum.NOTIFY.getCode());
        //给党务工作者生成会议结束代办
//        List<UserRoleForMeDTO> leaderIds = userSV.getUserPMIdsByOrgid(meetings.getOrgid(), RoleTypeEnum.WORKER.getCode());
//        meetingsSV.updateWorkTask(meetings, leaderIds, sysDate, WorkTaskClassEnum.CONTROL.getCode(), ConvertUtil.meetingTypeToTaskType(meetings.getType()), user.getId());
        return meetingsSV.updateByPrimaryKeySelective(meetings);
    }

    @RequestMapping(value = "/saveAndNotifyMeetings", method = RequestMethod.POST)
    @ApiOperation(value = "会议修改＋通知", notes = "会议修改＋通知")
    public int saveAndNotifyMeetings(VMeetingsNotifyBean meetingBean) throws GeneralException {
        Set<ConstraintViolation<VMeetingsNotifyBean>> constraintViolations = validator.validate(meetingBean);
        // 如果校验不通过，则结果集中会保存每一条校验失败的信息，判断结果集长度即可知道是否通过校验
        if (!constraintViolations.isEmpty()) {
            logger.error(constraintViolations.iterator().next().getMessage());
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR, constraintViolations.iterator().next().getMessage());
        }

        // 参数具体校验
        if (SimpleDataEnum.ISOPENNOTICE.getCode().equals(meetingBean.getIsMeetingNotice())) {
            if (null == meetingBean.getMeetingNoticeStart()
                    || null == meetingBean.getHoldNoticeInterval()
                    || 0 == meetingBean.getMeetingNoticeStart()
                    || 0 == meetingBean.getMeetingNoticeInterval()) {
                throw new GeneralException(MSG_NOTICE_PARAMS_ERROR);
            }
        }
        if (SimpleDataEnum.ISOPENNOTICE.getCode().equals(meetingBean.getIsRecordSubmit())) {
            if (null == meetingBean.getSubmitNoticeStart()
                    || null == meetingBean.getSubmitNoticeInterval()
                    || 0 == meetingBean.getSubmitNoticeStart()
                    || 0 == meetingBean.getSubmitNoticeInterval()) {
                throw new GeneralException(MSG_SUBMIT_PARAMS_ERROR);
            }
        }
        if (SimpleDataEnum.ISOPENNOTICE.getCode().equals(meetingBean.getIsMeetingHold())) {
            if (null == meetingBean.getHoldNoticeInterval() || 0 == meetingBean.getHoldNoticeInterval()) {
                throw new GeneralException(MSG_HOLD_PARAMS_ERROR);
            }
        }
        if (MEETING_TIMELINESS_SUPPLY.equals(meetingBean.getTimelinessType()) && MEETING_TYPE_ONLINE.equals(meetingBean.getChannelType())) {
            logger.error("线上直播不可以补录");
            throw new GeneralException(NOT_SUPPLY_ONLINE);
        }

        Users user = getUser();
        Date sysDate = new Date();

        // 查询会议
        Meetings meetings = meetingsSV.getByPrimaryKey(meetingBean.getMeetingsId());

        // 状态校验
        if (MeetingStatusEnum.TOBENOTIFY.getCode() != meetings.getStatus()) {
            logger.error("只有【待通知】状态的会议/党课，才可以发送通知");
            throw new GeneralException(SHOULD_TO_BE_NOTIFY);
        }

        if (MeetingTypeEnum.NEWSTUDY.getCode().equals(meetings.getType()) && StringUtils.isBlank(meetingBean.getTeacher())) {
            logger.error("讲师不能是为空或者空字符串");
            throw new GeneralException(TEACHER_NOT_EMPTY);
        }

        meetings.setTopic(meetingBean.getTopic());
        meetings.setStarttime(ConvertUtil.convertStringToDate(meetingBean.getStarttime(), "yyyy-MM-dd HH:mm"));
        meetings.setEndtime(ConvertUtil.convertStringToDate(meetingBean.getEndtime(), "yyyy-MM-dd HH:mm"));
        meetings.setAddress(meetingBean.getAddress());
        meetings.setRecorder(meetingBean.getRecorder());
        meetings.setPrecautions(StringUtils.isBlank(meetingBean.getPrecautions()) ? null : meetingBean.getPrecautions().trim());
        meetings.setPlanstart(ConvertUtil.convertStringToDate(meetingBean.getPlanstart(), "yyyy-MM-dd"));
        meetings.setPlanend(ConvertUtil.convertStringToDate(meetingBean.getPlanend(), "yyyy-MM-dd"));
        meetings.setTeacher(meetingBean.getTeacher()); // 党课-授课人
        meetings.setDescription(meetingBean.getDescription()); // 党课-授课内容

        meetings.setTimelinessType(MEETING_TIMELINESS_SUPPLY.equals(meetingBean.getTimelinessType()) ? MEETING_TIMELINESS_SUPPLY : MEETING_TIMELINESS_REALTIME);
        meetings.setChannelType(meetingBean.getChannelType());
        meetings.setThemePic(meetingBean.getThemePic());
        meetings.setRangeType(meetingBean.getRangeType());
        meetings.setDistributionType(meetingBean.getDistributionType());
        meetings.setMainProcessId(meetingBean.getMainProcessId());
        meetings.setRoomId(meetingBean.getRoomId());
        meetings.setActualStartTime(meetingBean.getActualStartTime());
        meetings.setActualEndTime(meetingBean.getActualEndTime());

        meetings.setNeedRecord(meetingBean.getNeedRecord());
        meetings.setAttendances(meetingBean.getAttendances());

        meetings.setMeHost(meetingBean.getMeHost());
        meetings.setMeHostId(meetingBean.getMeHostId());
        meetings.setMeContent(meetingBean.getMeContent());
//        if (StringUtils.isNotEmpty(meetingBean.getMeContent())) {
//            Organization org = organizationSV.getByPrimaryKey(meetings.getOrgid());
//            InputStream meContentStream = new ByteArrayInputStream(meetingBean.getMeContent().getBytes());
//            Date currTime = new Date();
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
//            String fileName = simpleDateFormat.format(currTime) + ".html";
//            String contentUrl = OnestUtil.storeByStream(meContentStream, fileName, "me", org.getProvince(), org.getId());
//            if (StringUtils.isNotEmpty(meetings.getMeContent())) {
//                OnestUtil.delete(meetings.getMeContent());
//            }
//            meetings.setMeContent(contentUrl);
//        } else {
//            meetings.setMeContent(null);
//        }

        return notify(meetings, user, sysDate, getNoMsgUserList(meetingBean.getNoMsgUser()), meetingBean);
    }

    private MeetingMsgSetting createMsgSetting(VMeetingsNotifyBean meetingBean, Users user, Date sysDate, Meetings meetings, MeetingMsgSetting msgSetting, Integer needRecord) throws SystemFailureException {
        if (null == msgSetting) {
            if (null == meetingBean || (null == meetingBean.getIsMeetingNotice()
                    && null == meetingBean.getIsRecordSubmit()
                    && null == meetingBean.getIsMeetingHold())) {
                //短信提醒默认设置，默认查询线下会议设置
                String msgDefaultItem = "MEETING_MSG_DEFAULT_RULE_UNLINE";
                if (meetings.getType() == MeetingTypeEnum.STUDY.getCode()) {
                    msgDefaultItem = "MEETING_MSG_DEFAULT_RULE_STUDY";
                } else {
                    //判断会议的展开形式是否为线上直播
                    if (MEETING_TYPE_ONLINE.equals(meetings.getChannelType())) {
                        //修改短信提醒设置为线上直播的设置
                        msgDefaultItem = "MEETING_MSG_DEFAULT_RULE_ONLINE";
                    }
                }

                // 前端无该参数返回，创建默认设置
                return meetingMsgSettingSV.addDefauleSetting(meetings.getId(), msgDefaultItem, user.getId(), sysDate, MEETING_TIMELINESS_SUPPLY.equals(meetings.getTimelinessType()), needRecord);
            } else {
                // 前端有该参数返回，依据参数创建设置
                MeetingMsgSetting newMsgSetting = new MeetingMsgSetting();
                newMsgSetting.setId(UIDUtil.getUID());
                newMsgSetting.setObjType(NOTICE_MSG_OBJ_TYPE);
                newMsgSetting.setObjId(meetings.getId());
                newMsgSetting.setIsDeleted(DataIsDeleteEnum.NORMAL.getCode());
                newMsgSetting.setIsMeetingNotice(meetingBean.getIsMeetingNotice());
                newMsgSetting.setMeetingNoticeStart(meetingBean.getMeetingNoticeStart());
                newMsgSetting.setMeetingNoticeInterval(meetingBean.getMeetingNoticeInterval());
                newMsgSetting.setIsRecordSubmit(meetingBean.getIsRecordSubmit());
                newMsgSetting.setSubmitNoticeStart(meetingBean.getSubmitNoticeStart());
                newMsgSetting.setSubmitNoticeInterval(meetingBean.getSubmitNoticeInterval());
                newMsgSetting.setIsMeetingHold(meetingBean.getIsMeetingHold());
                newMsgSetting.setHoldNoticeInterval(meetingBean.getHoldNoticeInterval());
                newMsgSetting.setCreatedBy(user.getId());
                newMsgSetting.setCreatedDate(sysDate);
                meetingMsgSettingSV.insertSelective(newMsgSetting);
                return newMsgSetting;
            }
        } else {
            if (null != meetingBean
                    && null != meetingBean.getIsMeetingNotice()
                    && null != meetingBean.getIsRecordSubmit()
                    && null != meetingBean.getIsMeetingHold()) {
                msgSetting.setIsMeetingNotice(meetingBean.getIsMeetingNotice());
                msgSetting.setMeetingNoticeStart(meetingBean.getMeetingNoticeStart());
                msgSetting.setMeetingNoticeInterval(meetingBean.getMeetingNoticeInterval());
                msgSetting.setIsRecordSubmit(meetingBean.getIsRecordSubmit());
                msgSetting.setSubmitNoticeStart(meetingBean.getSubmitNoticeStart());
                msgSetting.setSubmitNoticeInterval(meetingBean.getSubmitNoticeInterval());
                msgSetting.setIsMeetingHold(meetingBean.getIsMeetingHold());
                msgSetting.setHoldNoticeInterval(meetingBean.getHoldNoticeInterval());
                msgSetting.setModifiedBy(user.getId());
                msgSetting.setModifiedDate(sysDate);
                meetingMsgSettingSV.updateByPrimaryKeySelective(msgSetting);
            }
            if (null != meetingBean && null != meetings.getNeedRecord()) {
                msgSetting.setIsRecordSubmit(meetings.getNeedRecord());
                meetingMsgSettingSV.updateByPrimaryKeySelective(msgSetting);
            }
            return msgSetting;
        }
    }

    @RequestMapping(value = "/getTaskMeetings", method = RequestMethod.POST)
    @ApiOperation(value = "查询所有会议任务信息", notes = "查询所有会议任务信息")
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<WorkTaskDTO> getTaskMeetings(VMeetingsListSearchBean searchBean) throws GeneralException {
        Users currentUser = getUser();

        Set<ConstraintViolation<VMeetingsListSearchBean>> constraintViolations = validator.validate(searchBean);
        // 如果校验不通过，则结果集中会保存每一条校验失败的信息，判断结果集长度即可知道是否通过校验
        if (!constraintViolations.isEmpty()) {
            logger.error(constraintViolations.iterator().next().getMessage());
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR, constraintViolations.iterator().next().getMessage());
        }
        Map<String, Object> params = dealParamForTaskList(searchBean);
        params.put("userid", currentUser.getId());
        //app不再添加角色参数
        if (!"02".equals(currentUser.getAppType()))
            params.put("userRole", currentUser.getCurrentRoleId());
        params.put("isPerson", searchBean.getIsPerson());

        return workTaskSV.getAgencyByType(searchBean.getPage(), searchBean.getLimit(), params);
    }

    @RequestMapping(value = "/getTaskMeetingsWithRole", method = RequestMethod.POST)
    @ApiOperation(value = "查询所有会议任务信息(不再根据待办查询)", notes = "查询所有会议任务信息(不再根据待办查询)")
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<MeetingsListDTO> getTaskMeetingsWithRole(VMeetingsListSearchBean searchBean) throws GeneralException {
        Users currentUser = getUser();

        Set<ConstraintViolation<VMeetingsListSearchBean>> constraintViolations = validator.validate(searchBean);
        // 如果校验不通过，则结果集中会保存每一条校验失败的信息，判断结果集长度即可知道是否通过校验
        if (!constraintViolations.isEmpty()) {
            logger.error(constraintViolations.iterator().next().getMessage());
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR, constraintViolations.iterator().next().getMessage());
        }
        Map<String, Object> params = dealParamForTaskList(searchBean);
        params.put("userid", currentUser.getId());
        params.put("codestr", currentUser.getCurrentRoleOrg().getCodestr());
        params.put("isPerson", searchBean.getIsPerson());
        params.put("assessmentOrgIds",currentUser.getAssessmentOrgIds());

        return workTaskSV.getTaskMeetingsWithRole(searchBean.getPage(), searchBean.getLimit(), params);
    }

    @RequestMapping(value = "/exportMeeting", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出会议及与会信息", notes = "导出会议及与会信息")
    public void exportMeeting(VMeetingsSummaryListSearchBean vMeetingsSummaryListSearchBean, HttpServletResponse response, String orgCode) throws GeneralException, IOException {
        Set<ConstraintViolation<VMeetingsSummaryListSearchBean>> constraintViolations = validator.validate(vMeetingsSummaryListSearchBean);
        // 如果校验不通过，则结果集中会保存每一条校验失败的信息，判断结果集长度即可知道是否通过校验
        if (!constraintViolations.isEmpty()) {
            logger.error(constraintViolations.iterator().next().getMessage());
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR, constraintViolations.iterator().next().getMessage());
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        if (vMeetingsSummaryListSearchBean.getOrgCodeTerm() == null || vMeetingsSummaryListSearchBean.getOrgCodeTerm().isEmpty()) {
            // 数据权限规则
            vMeetingsSummaryListSearchBean.setCurrentOrgCode(StringUtils.isBlank(orgCode) ? null : orgCode);
        } else {
            // 数据权限规则
            vMeetingsSummaryListSearchBean.setCurrentOrgCode(vMeetingsSummaryListSearchBean.getOrgCodeTerm());
        }

        PageInfo<MeetingsDTO> meetingsDTOPageInfo = meetingsSV.selectListByParams(vMeetingsSummaryListSearchBean, "asc");
        if (null == meetingsDTOPageInfo || meetingsDTOPageInfo.getTotal() == 0) {
            logger.error("没有符合条件的数据");
            throw new GeneralException(NO_DATA_FOR_PARAM);
        }

        Set<String> meetingIds = new HashSet<>(16);
        for (MeetingsDTO temp : meetingsDTOPageInfo.getList()) {
            meetingIds.add(temp.getId());
        }
        List<MeConventioneerInfoDTO> meConventList = conventioneerSV.getMeConventInfoByMeetingIds(meetingIds);
        Map<String, MeConventioneerInfoDTO> meConventMap = new HashMap<>(16);
        for (MeConventioneerInfoDTO temp : meConventList) {
            meConventMap.put(temp.getMeetingid(), temp);
        }

        // 声明一个工作簿
        HSSFSheet sheet;
        try (HSSFWorkbook workbook = new HSSFWorkbook()) {
            // 生成一个表格
            sheet = workbook.createSheet("会议参与信息");
            // 设置表格默认列宽度为15个字节
            sheet.setDefaultColumnWidth((short) 15);

            // 生成一个样式
            HSSFCellStyle style = workbook.createCellStyle();
            // 设置这些样式
            style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.YELLOW.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style.setAlignment(HorizontalAlignment.CENTER);

            String[] headers = {"本部及分公司", "参与组织", "会议类型", "会议主题", "会议日期", "应到人员数量", "实到人员数量", "未参会人员数量", "请假人员数量"};

            // 产生表格标题行
            HSSFRow row = sheet.createRow(0);
            for (short i = 0; i < headers.length; i++) {
                HSSFCell cell = row.createCell(i);
                cell.setCellStyle(style);
                HSSFRichTextString text = new HSSFRichTextString(headers[i]);
                cell.setCellValue(text);
            }

            // 产生内容行
            int index = 0;
            for (MeetingsDTO temp : meetingsDTOPageInfo.getList()) {
                index++;
                row = sheet.createRow(index);
                MeConventioneerInfoDTO meConventioneerInfoDTO = meConventMap.get(temp.getId());

                // 分公司
                HSSFCell cell = row.createCell(0);
//                cell.setCellStyle(style);
                cell.setCellValue(temp.getCompanyName());
                // 支部
                cell = row.createCell(1);
                cell.setCellValue(temp.getOrgname());
                // 会议类型
                cell = row.createCell(2);
//                cell.setCellStyle(style);
                cell.setCellValue(ConvertUtil.meetingTypeToDesc(temp.getType()));
                // 会议主题
                cell = row.createCell(3);
//                cell.setCellStyle(style);
                cell.setCellValue(temp.getTopic());
                // 会议召开时间
                cell = row.createCell(4);
//                cell.setCellStyle(style);
                cell.setCellValue((null == temp.getStarttime() ? "" : sdf.format(temp.getStarttime())) + "--" + (null == temp.getEndtime() ? "" : sdf.format(temp.getEndtime())));
                if (null == meConventioneerInfoDTO) {
                    // 应到人员数量
                    cell = row.createCell(5);
//                cell.setCellStyle(style);
                    cell.setCellValue(0);
                    // 实到人员数量
                    cell = row.createCell(6);
//                cell.setCellStyle(style);
                    cell.setCellValue(0);
                    // 未参会人员数量
                    cell = row.createCell(7);
//                cell.setCellStyle(style);
                    cell.setCellValue(0);
                    // 请假人员数量
                    cell = row.createCell(8);
//                cell.setCellStyle(style);
                    cell.setCellValue(0);
                } else {
                    // 应到人员数量
                    cell = row.createCell(5);
//                cell.setCellStyle(style);
                    cell.setCellValue(meConventioneerInfoDTO.getTotalNum());
                    // 实到人员数量
                    cell = row.createCell(6);
//                cell.setCellStyle(style);
                    cell.setCellValue(meConventioneerInfoDTO.getJoinNum());
                    // 未参会人员数量
                    cell = row.createCell(7);
//                cell.setCellStyle(style);
                    cell.setCellValue(meConventioneerInfoDTO.getTotalNum() - meConventioneerInfoDTO.getJoinNum() - meConventioneerInfoDTO.getLeaveNum());
                    // 请假人员数量
                    cell = row.createCell(8);
//                cell.setCellStyle(style);
                    cell.setCellValue(meConventioneerInfoDTO.getLeaveNum());
                }
            }

            String fileName = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xls");
            workbook.write(out);
        }
    }

    @RequestMapping(value = "/deleteCustom", method = RequestMethod.POST)
    @ApiOperation(value = "删除计划外会议", notes = "逻辑删除所有与会议相关的信息",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingId", value = "会议ID", required = true, dataType = "String", paramType = "query")})
    @Log(title = "会议管理（删除会议）",businessType = BusinessType.DELETE)
    public void deleteCustom(String meetingId) throws GeneralException {
        // 查询会议
        Meetings meetings = meetingsSV.getByPrimaryKey(meetingId);
        //判断会议是否有直播间占用情况，如果有则先释放直播间
        if (!StringUtils.isBlank(meetings.getRoomId())) {
            String key = PbmsConstants.LECTURE_LIVE_ROOM_PRE.concat(meetings.getRoomId());
            String roomStatus = CacheServiceUtil.getService().getString(key);
            if (StringUtils.isBlank(roomStatus) || "000".equals(roomStatus)) {
                logger.info("该党课的直播房间已经释放");
            } else {
                // 释放直播间
                Long result = CacheServiceUtil.getService().del(key);

                if (0 == result) {
                    logger.error("系统异常：释放直播房间失败（key=‘" + key + "’");
                    throw new GeneralException(FREE_ROOM_FAILED);
                }
            }

        }
        meetingsSV.deleteCustomMeeting(meetingId);
    }

    @RequestMapping(value = {"/generAuditMeeting", "/holdMeeting"}, method = RequestMethod.POST)
    @ApiOperation(value = "会议归档(提交稽核)", notes = "会议归档(提交稽核)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "meetingsId", value = "会议id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "signState", value = "0：不需要签字 1：待签字 2：拒绝 3：签字", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "meMeetingSignDetailListJson", value = "userId,signType,needToSign", required = false, dataType = "String", paramType = "query")})
    public int holdMeeting(String meetingsId, Integer meetingType, String vcResourceId, String vcSizes, String vcLengths, Integer signState, String meMeetingSignDetailListJson,Integer isSave,String recordContent) throws GeneralException {
        Users currentUser = getUser();
        if (ValidateUtil.isValid(signState))
            throw new GeneralException("PBMS_ME_1068");
        // 查询会议
        Meetings meetings1 = meetingsSV.getByPrimaryKey(meetingsId);
        if (null == meetings1) {
            logger.error("参数信息有误：未查询到会议信息（ID=‘" + meetingsId + "’");
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR);
        }
        //保存还是提交
        if (isSave != null && isSave == 1 ){
            meetings1.setIsSave(isSave);
            meetings1.setRecordContent(recordContent);
            return meetingsSV.updateByPrimaryKeySelective(meetings1);
        }

        int result = doHoldMeeting(meetings1, signState, meMeetingSignDetailListJson, vcResourceId, vcSizes, vcLengths, currentUser, false, recordContent);
        if (result > 0) {
//            submitToApproveProcess(meetings, currentUser);
        }
        return result;
    }

    private int doHoldMeeting(Meetings meetings, Integer signState, String meMeetingSignDetailListJson, String vcResourceId, String vcSizes, String vcLengths, Users currentUser, boolean isAuditResule, String recordContent) throws GeneralException {  //需要签字但是未完成签字
        Date sysDate = new Date();

        // 活动文件
        List<Attachments> attachmentList = new ArrayList<>();

        //线上流程不进行校验归档附件
        if (!MEETING_TYPE_ONLINE.equals(meetings.getChannelType())) {
            // 需审核但未审核通过的，不能归档
            if (Integer.valueOf(1).equals(meetings.getExtfld1()) && !MinutesStatusEnum.REVIEWPASS.getCode().equals(meetings.getMinutesStatus())) {
                logger.error("会议纪要未审核通过，不能归档");
                throw new GeneralException(MINUTES_NOT_REVIEW_PASS);
            }

            // 三会一课中，【签到表】为必须
            List<Attachments> attendanceList = attachmentsSV.getListByObjIdType(meetings.getId(), AttachTypeEnum.ME_SIGN.getType());
            if (CollectionUtils.isEmpty(attendanceList)) {
//                logger.error("请上传签到表");
//                throw new GeneralException(SING_NOT_EMPTY);
            } else {
                attachmentList.addAll(attendanceList);
            }

            // 校验会议照片（支部党员大会、党课必须上传）

//            if (MeetingTypeEnum.BRANCHMASSES.getCode().equals(meetings.getType()) || MeetingTypeEnum.NEWSTUDY.getCode().equals(meetings.getType()) || MeetingTypeEnum.STUDY.getCode().equals(meetings.getType())) {
//                List<Attachments> pictureList = attachmentsSV.getListByObjIdType(meetings.getId(), AttachTypeEnum.ME_PICTURE.getType());
//                if (CollectionUtils.isEmpty(pictureList)) {
//                    logger.error("请上传照片");
//                    throw new GeneralException(PICTURE_NOT_EMPTY);
//                } else {
//                    attachmentList.addAll(pictureList);
//                }
//            }

//            // 校验活动材料，活动材料必填
//            List<Attachments> acitityEnclosure = attachmentsSV.getListByObjIdType(meetings.getId(), AttachTypeEnum.ME_ACTIVITY.getType());
//            if (CollectionUtils.isEmpty(acitityEnclosure)){
//                logger.error("请上传活动材料");
//                throw new GeneralException(ACITITY_NOT_EMPTY);
//            } else {
//                attachmentList.addAll(acitityEnclosure);
//            }

            List<Attachments> summaryList = attachmentsSV.getListByObjIdType(meetings.getId(), AttachTypeEnum.ME_MINUTES.getType());
            if (summaryList != null && !summaryList.isEmpty()) {
                attachmentList.addAll(summaryList);
            }
            // 校验会议纪要（支部党员大会、支部委员会、党小组会必须上传）
//            if (MeetingTypeEnum.BRANCHMASSES.getCode().equals(meetings.getType())
//                    || MeetingTypeEnum.BRANCHLEADER.getCode().equals(meetings.getType())
//                    || MeetingTypeEnum.PARTYGROUP.getCode().equals(meetings.getType())) {
//                List<Attachments> summaryList = attachmentsSV.getListByObjIdType(meetings.getId(), AttachTypeEnum.ME_MINUTES.getType());
//                if (CollectionUtils.isEmpty(summaryList)) {
//                    logger.error("归档前请上传会议纪要");
//                    throw new GeneralException(MINUTES_NOT_EMPTY);
//                }
//            }

            if (null != meetings.getAuditState() && 1 == meetings.getAuditState()) {
                logger.error("已提交稽核但未稽核的，提示正等待稽核");
                throw new GeneralException("PBMS_ME_1064");
            }
        }

        String holdType = HOLD_MEETING_UNDERLINE;
        if (meetings.getType() == MeetingTypeEnum.STUDY.getCode()) {
            holdType = HOLD_MEETING_STUDY;
        } else {
            if (MEETING_TYPE_ONLINE.equals(meetings.getChannelType())) {
                holdType = HOLD_MEETING_ONLINE;
            }
        }

        //当前会议是否是线上会议且是主流程
        Boolean searchAll = false;
        if (MEETING_TYPE_ONLINE.equals(meetings.getChannelType()) && PROCESS_TYPE_MAIN.equals(meetings.getDistributionType())) {
            searchAll = true;
        }

        //判断会议是否需要提交会议记录，如果是，则归档需要校验提交率
//        if (null != meetings.getNeedRecord() && 1 == meetings.getNeedRecord()) {
//            // 查询字典项配置要求完成率
//            String holdRatioStr = dictionaryItemsSV.getTextByCodeAndDictcode(holdType, HOLD_MEETING);
//            //查询会议记录提交量是否大于等于会议应参加人数的80%
//            MeetingConventioneerDTO meetingRatio = conventioneerSV.getSubmitRatio(meetings.getId(), searchAll);
//
//            if (StringUtils.isNotBlank(holdRatioStr) && meetingRatio.getRatio() < Double.valueOf(holdRatioStr)) {
//                if (meetings.getType() == MeetingTypeEnum.STUDY.getCode()) {
//                    logger.error("待召开阶段【实际参会状态】为“参加”的人员，会议记录提交占比应大于80%");
//                    throw new GeneralException("PBMS_ME_1035", "待召开阶段【实际参会状态】为“参加”的人员，学习记录提交占比应大于等于".concat(holdRatioStr).concat("%"));
//                } else if (meetings.getType() == MeetingTypeEnum.NEWSTUDY.getCode()) {
//                    logger.error("待上课阶段【实际参会状态】为“参加”的人员，党课心得提交占比应大于80%");
//                    throw new GeneralException("PBMS_ME_1035", "待上课阶段【实际参会状态】为“参加”的人员，党课心得提交占比应大于等于".concat(holdRatioStr).concat("%"));
//                } else {
//                    logger.error("待召开阶段【实际参会状态】为“参加”的人员，会议记录提交占比应大于80%");
//                    throw new GeneralException("PBMS_ME_1035", "待召开阶段【实际参会状态】为“参加”的人员，会议记录提交占比应大于等于".concat(holdRatioStr).concat("%"));
//                }
//            }
//        }

//        // 检查会议记录/党课心得是否所有参会人员全部上传,必须全部上传才可以进行归档
//        List<MeetingConventioneerDTO> notSubRecordList = conventioneerSV.getNotSubmitRecord(meetingsId);
//        if (!CollectionUtils.isEmpty(notSubRecordList)) {
//            logger.error("归档前需所有参与人员提交记录/心得");
//            throw new GeneralException(RECORD_NOT_EMPTY);
//        }

        //线上直播会议需要归档视频回放链接
        if (MEETING_TYPE_ONLINE.equals(meetings.getChannelType())) {
            //线上直播会议需要校验视频回放链接是否为空（必须有视频回放链接才可归档）
            if (null == vcResourceId || vcResourceId.isEmpty()) {
                logger.error("未获取到视频回放地址，暂无法归档，请稍后再试。");
                throw new GeneralException(HOLED_VC_ADDR);
            }
            String[] sizes = null == vcSizes || vcSizes.isEmpty() ? new String[]{} : vcSizes.split(",");
            String[] lengths = null == vcLengths || vcLengths.isEmpty() ? new String[]{} : vcLengths.split(",");
            String[] resourceIds = null == vcResourceId || vcResourceId.isEmpty() ? new String[]{} : vcResourceId.split(",");
            List<Attachments> vcList = new ArrayList<>();
            Date createDate = new Date();
            for (int i = 0; i < resourceIds.length; i++) {
                //声明附件表对象
                Attachments vcAddr = new Attachments();
                vcAddr.setId(UIDUtil.getUID());
                vcAddr.setAttname(meetings.getTopic().concat("-").concat(String.valueOf(i + 1)));
                vcAddr.setAtttype(AttachTypeEnum.ME_VCURL.getType());
                if (resourceIds.length > i) vcAddr.setUrl(resourceIds[i]);
                if (sizes.length > i) vcAddr.setFilesize(Integer.valueOf(sizes[i]));
                vcAddr.setObjid(meetings.getId());
                vcAddr.setCreatedby(getUser().getId());
                vcAddr.setCreateddate(createDate);
                vcAddr.setIsdeleted(0);
                vcList.add(vcAddr);

                //声明网上学习对象，并保存
                StudySource studySource = new StudySource();
                studySource.setId(UIDUtil.getUID());
                studySource.setOrgid(meetings.getOrgid());
                studySource.setOrgname(meetings.getOrgname());
                studySource.setPublicscope(2);  //1、选择“公开”，则在移动端对全网公开。2、选择“本党组及下级党组”，则在移动端，只对所属党组织党员公开。
                studySource.setFiletype(2); //1、音频。2、视频
                studySource.setProvince(getUser().getProvince());
                studySource.setFilename(meetings.getTopic().concat("-").concat(String.valueOf(i + 1)));
                if (lengths.length > i) studySource.setDuration(lengths[i]);
                if (resourceIds.length > i) studySource.setStorepath(resourceIds[i]);
                studySource.setTitle(meetings.getTopic().concat("-").concat(String.valueOf(i + 1)));
                studySource.setThemepic(meetings.getThemePic());
                studySource.setContenttype("LECTURELIVE");
                studySource.setSsstatus(1);    //0草稿。1已发布
                studySource.setCreatedby(meetings.getCreatedby());
                studySource.setCreateddate(createDate);
                studySource.setIsdeleted(0);
                studySource.setReadcount(0);
                studySource.setIsexcellent(0);
                studySource.setOrderNo(DateUtil.format(studySource.getCreateddate(), "yyyyMMddHHmmss") + "0000");
                studySourceSV.insertSelective(studySource);

            }
            int vcInsert = attachmentsSV.insertCollect(vcList);
            if (1 > vcInsert) {
                logger.error("视频回放地址保存失败，请稍后再试。");
                throw new GeneralException(HOLED_VC_SAVE);
            }
        }

        workTaskSV.doneByObjidAndType(meetings.getId(), null, SimpleDataEnum.DATADONE.getCode(), currentUser.getId(), sysDate, null);
        if (null != signState && 1 == signState && !isAuditResule && (null == meetings.getSignState() || 3 != meetings.getSignState() || ((null != meetings.getAuditState() && 2 == meetings.getAuditState()))))
            return meetingsSV.generSignatureToDo(meetings, signState, JSONArray.parseArray(meMeetingSignDetailListJson), currentUser.getOrgid(), currentUser.getUsername(), currentUser.getId(), new Date());

        // 未提交稽核的 提交稽核
//        if (null == meetings.getAuditState() || 0 == meetings.getAuditState() || 2 == meetings.getAuditState()) {
//            logger.info("未提交稽核的 提交稽核");
//            //0：未提交稽核 1：待稽核 2：稽核未通过 3：稽核通过
//            // 结束所有关于该会议的待办
//            return meetingsSV.generAuditTask(meetings, currentUser, new Date());
//        } else if (3 == meetings.getAuditState()) {
//            //稽核通过
//
//            return doHoldMeetings(meetings, currentUser, sysDate);
//        } else {
//            logger.error("稽核状态异常，请联系管理员");
//            throw new GeneralException("PBMS_ME_1065");
//        }
// 2024 08 07
        //填报记录提交
        //判断状态是否正确
        Assert.isTrue(MeetingNewStatusEnum.FILLIN.getCode().equals(meetings.getStatus()), "活动状态异常，请联系管理员");
        //更改状态到待审核
        //保存记录内容
        //
        Meetings meetings1 = meetingsSV.getByPrimaryKey(meetings.getId());
        //
        meetings1.setRecordContent(recordContent);
        meetingsSV.updateByPrimaryKeySelective(meetings1);
        int result = meetingsSV.commitToExamine(meetings, currentUser, new Date());
        //不是保存才去提交审批
        if (result > 0 && 0 == meetings.getIsSave() ) {
            submitToApproveProcess(meetings, currentUser, null, ApprovalProcessServiceOAImpl.BUSINESS_TYPE_ACTIVITY_RECORD);
        }
        return result;
    }

    @RequestMapping(value = "/auditMeeting", method = RequestMethod.POST)
    @ApiOperation(value = "稽核会议", notes = "稽核会议")
    @ApiResponses({
            @ApiResponse(code = 0, message = "稽核成功", response = Integer.class),
            @ApiResponse(code = 999, message = "稽核失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "meetingsId", value = "会议id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "auditState", value = "0:保存 2：不通过 3：通过", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "auditReason", value = "不通过的原因", required = false, dataType = "String", paramType = "query")})
    public Integer auditMeeting(String meetingsId, Integer auditState, String auditReason) throws GeneralException {
        Users user = getUser();
        ValidateUtil.isNotNull(auditState);
        ValidateUtil.isNotEmpty(meetingsId);

        // 查询会议
        Meetings meetings = meetingsSV.getByPrimaryKey(meetingsId);
        if (null == meetings) {
            logger.error("参数信息有误：未查询到会议信息（ID=‘" + meetingsId + "’");
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR);
        }

        Date sysDate = new Date();
        meetings.setModifiedby(user.getId());
        meetings.setModifieddate(sysDate);
        if (StringUtils.isBlank(auditReason))
            meetings.setAuditReason(" ");
        else
            meetings.setAuditReason(auditReason);

        //如果当前操作为保存，则只对审核信息进行保存，不做其他操作
        if (auditState == 0) {
            return meetingsSV.updateByPrimaryKeySelective(meetings);
        }

        ReviewLog reviewLog = new ReviewLog();
        reviewLog.setId(UIDUtil.getUID());
        reviewLog.setProcessType(ProcessTypeEnum.ME_AUDIT.getCode());
        reviewLog.setObjectId(meetings.getId());
        //5001( 1：提交稽核 2：稽核不通过 3：稽核通过)
        reviewLog.setOperationType(auditState);
        reviewLog.setOperationDesc(2 == auditState ? "稽核不通过" : "稽核通过");
        reviewLog.setOperationUserId(user.getId());
        reviewLog.setOperationUserName(user.getUsername());
        reviewLog.setOperationTime(sysDate);
        reviewLog.setResuseReason(auditReason);
        reviewLog.setRemark("中台" + user.getUsername() + "已" + reviewLog.getOperationDesc());
        reviewLogSV.insertSelective(reviewLog);
        //0：未提交稽核 1：待稽核 2：稽核未通过 3：稽核通过
        meetings.setAuditState(auditState);
        meetings.setAuditExampleTime(sysDate);

        if (3 == auditState) {
            //稽核通过
            doHoldMeetings(meetings, user, sysDate);
            //(String objid, Integer tasktype, Integer taskstatus, String userId, Integer type, String modifiedby, Date modifieddate)
            return workTaskSV.doneByObjidAndTypeAndUserId(meetings.getId(), WorkTaskTypeEnum.AUDIT.getCode(), Integer.valueOf(1), null, WorkTaskTypeEnum.AUDIT.getCode(), user.getId(), sysDate);
        } else if (2 == auditState) {
//            meetings.setStatus(Integer.valueOf(400));
            meetingsSV.updateByPrimaryKeySelective(meetings);

            meetingsSV.sendBackAuditMsg(user, meetingsId, meetings.getTopic(), meetings.getType(), ProcessTypeEnum.ME_AUDIT.getCode(), null);

            return workTaskSV.doneByObjidAndTypeAndUserId(meetings.getId(), WorkTaskTypeEnum.AUDIT.getCode(), Integer.valueOf(1), null, WorkTaskTypeEnum.AUDIT.getCode(), user.getId(), sysDate);
        } else {
            logger.error("稽核状态异常，请联系管理员");
            throw new GeneralException("PBMS_ME_1065");
        }
    }

    @RequestMapping(value = "/getAuditMeetingList", method = RequestMethod.POST)
    @ApiOperation(value = "查询会议稽核列表", notes = "查询会议稽核列表")
    @ApiResponses({
            @ApiResponse(code = 0, message = "查询成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "查询失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "会议类型", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "topic", value = "会议主题", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "auditState", value = "稽核状态(0：未提交稽核 1：待稽核 2：稽核未通过 3：稽核通过)", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orgId", value = "所属组织id", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "auditTodoTimeStart", value = "提交稽核时间开始", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "auditTodoTimeEnd", value = "提交稽核时间结束", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "auditExampleTimeStart", value = "稽核审核时间开始", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "auditExampleTimeEnd", value = "稽核审核时间结束", required = false, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数量", required = true, dataType = "Integer", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<AuditMeetingListDTO> getAuditMeetingList(Integer type, String topic, Integer auditState, String orgId, Date auditTodoTimeStart, Date auditTodoTimeEnd, Date auditExampleTimeStart, Date auditExampleTimeEnd, Integer page, Integer limit) throws GeneralException {

        ValidateUtil.isNotNull(page, limit);
        Users currentUser = getUser();

        return meetingsSV.getAuditMeetingList(currentUser.getId(), currentUser.getCurrentRoleId(), type, topic, auditState, orgId, auditTodoTimeStart, auditTodoTimeEnd, auditExampleTimeStart, auditExampleTimeEnd, page, limit);
    }

    @RequestMapping(value = "/getAuditMeetingDetail", method = RequestMethod.POST)
    @ApiOperation(value = "查询会议稽核列表", notes = "查询会议稽核列表")
    @ApiResponses({
            @ApiResponse(code = 0, message = "查询成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "查询失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "会议id", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public AuditMeetingDetailDTO getAuditMeetingDetail(String id) throws GeneralException {

        ValidateUtil.isNotEmpty(id);
        return meetingsSV.getAuditMeetingDetail(id, getUser().getId());
    }

    private Integer doHoldMeetings(Meetings meetings, Users user, Date sysDate) {
        // 更新会议信息
        meetings.setStatus(MeetingStatusEnum.FINISHED.getCode());
        meetings.setModifiedby(user.getId());
        meetings.setModifieddate(sysDate);
        meetings.setIserror((short) SimpleDataEnum.MEETINGERRORYES.getCode().intValue());
        // 结束所有关于该会议的待办
        workTaskSV.doneByObjidAndType(meetings.getId(), null, SimpleDataEnum.DATADONE.getCode(), user.getId(), sysDate, null);
        // 更新计划任务状态
        if (SimpleDataEnum.MEISPLAN.getCode().equals(Integer.valueOf(meetings.getIsplan()))) {
            planTaskSV.updateStatusByTypeAndObjId(meetings.getType(), meetings.getId(), SimpleDataEnum.PLANTASKDONE.getCode());
        }

        return meetingsSV.updateByPrimaryKeySelective(meetings);
    }

    /**
     * 参数转换处理(会议更新)
     *
     * @param meeting      会议对象
     * @param meetingsBean 参数封装对象
     * @param userId       当前用户ID
     * @throws GeneralException
     */
    private void dealParamForModifyMeeting(Meetings meeting, VMeetingsModifyBean meetingsBean, String userId, Date sysDate) {

        // 处理时间格式参数
        Date starttime = ConvertUtil.convertStringToDate(meetingsBean.getStarttime(), "yyyy-MM-dd HH:mm");
        Date endtime = ConvertUtil.convertStringToDate(meetingsBean.getEndtime(), "yyyy-MM-dd HH:mm");
        // 会议负责人
        if (StringUtils.isBlank(meeting.getModerator())) {
            meeting.setModerator(userId);
        }
        meeting.setTopic(meetingsBean.getTopic());
        meeting.setAddress(meetingsBean.getAddress());
        meeting.setPrecautions(StringUtils.isBlank(meetingsBean.getPrecautions()) ? null : meetingsBean.getPrecautions().trim());
        meeting.setSigntime(meetingsBean.getSigntime());
        meeting.setModifiedby(userId);
        meeting.setModifieddate(sysDate);
        meeting.setPlanstart(ConvertUtil.convertStringToDate(meetingsBean.getPlanstart(), "yyyy-MM-dd"));
        meeting.setPlanend(ConvertUtil.convertStringToDate(meetingsBean.getPlanend(), "yyyy-MM-dd"));

        // 支部委员会-记录员
        meeting.setRecorder(meetingsBean.getRecorder());
        // 党课-授课人
        meeting.setTeacher(meetingsBean.getTeacher());
        // 党课-授课内容
        meeting.setDescription(meetingsBean.getDescription());

        meeting.setThemePic(meetingsBean.getThemePic());
        meeting.setRangeType(meetingsBean.getRangeType());
        meeting.setDistributionType(meetingsBean.getDistributionType());
        meeting.setRoomId(meetingsBean.getRoomId());
        meeting.setActualStartTime(meetingsBean.getActualStartTime());
        meeting.setActualEndTime(meetingsBean.getActualEndTime());
        meeting.setTimelinessType(MEETING_TIMELINESS_SUPPLY.equals(meetingsBean.getTimelinessType()) ? MEETING_TIMELINESS_SUPPLY : MEETING_TIMELINESS_REALTIME);

        meeting.setMeHost(meetingsBean.getMeHost());
        meeting.setMeHostId(meetingsBean.getMeHostId());
        meeting.setMeContent(meetingsBean.getMeContent());
//        if (StringUtils.isNotEmpty(meetingsBean.getMeContent())) {
//            Organization org = organizationSV.getByPrimaryKey(meeting.getOrgid());
//            InputStream meContentStream = new ByteArrayInputStream(meetingsBean.getMeContent().getBytes());
//            Date currTime = new Date();
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
//            String fileName = simpleDateFormat.format(currTime) + ".html";
//            String contentUrl = OnestUtil.storeByStream(meContentStream, fileName, "me", org.getProvince(), org.getId());
//            if (StringUtils.isNotEmpty(meeting.getMeContent())) {
//                OnestUtil.delete(meeting.getMeContent());
//            }
//            meeting.setMeContent(contentUrl);
//        } else {
//            meeting.setMeContent(null);
//        }

        //修改会议计划信息不变更一下信息
        if (null != starttime)
            meeting.setStarttime(starttime);
        if (null != endtime)
            meeting.setEndtime(endtime);
        if (StringUtils.isNotBlank(meetingsBean.getChannelType()))
            meeting.setChannelType(meetingsBean.getChannelType());
        if (StringUtils.isNotBlank(meetingsBean.getMainProcessId()))
            meeting.setMainProcessId(meetingsBean.getMainProcessId());
        if (null != meetingsBean.getNeedRecord())
            meeting.setNeedRecord(meetingsBean.getNeedRecord());
    }

    /**
     * 参数转换处理(待办任务列表)
     *
     * @param searchBean 检索条件对象
     * @return
     * @throws GeneralException
     */
    private Map<String, Object> dealParamForTaskList(VMeetingsListSearchBean searchBean) {
        Map<String, Object> params = new HashMap<>(16);
        params.put("tasktype", searchBean.getType());
        params.put("taskstatus", null); // 0-待办 1-已办 null-所有
        params.put("topic", searchBean.getTopic());
        params.put("starttime", StringUtils.isBlank(searchBean.getStarttime()) ? null : ConvertUtil.convertStringToDate(searchBean.getStarttime().concat(" 00:00:00")));
        if (!StringUtils.isBlank(searchBean.getEndtime())) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(ConvertUtil.convertStringToDate(searchBean.getEndtime(), "yyyy-MM-dd"));
            calendar.add(Calendar.DATE, 1);
            params.put("endtime", calendar.getTime());
        }
        params.put("orgname", searchBean.getOrgname());
        params.put("status", searchBean.getStatus());
        params.put("auditState", searchBean.getAuditState());
        params.put("iserror", searchBean.getIserror());
        return params;
    }

    /**
     * 取操作者所在党支部
     *
     * @param orgId
     * @return
     */
    private Organization getOrganization(String orgId, boolean isGroupFlag) {
        Organization organization = organizationSV.getByPrimaryKey(orgId);
        // 依据：基层组织只有党小组和党支部，如果当前机构不是党支部，则上一级必是
        if (isGroupFlag) {
            if (organization.getIsgroup() == SimpleDataEnum.ISGROUPYES.getCode()) {
                return organization;
            } else {
                return null;
            }
        } else {
            if (organization.getIsgroup() == SimpleDataEnum.ISGROUPYES.getCode()) {
                return organizationSV.getByPrimaryKey(organization.getParentid());
            } else {
                return organization;
            }
        }
    }

    /**
     * 新增会议
     *
     * @param meetingsBean
     * @param user
     * @param meetingType
     * @param sysDate
     * @return
     * @throws GeneralException
     */
    private int addMeeting(VMeetingsModifyBean meetingsBean, Users user, Integer meetingType, Date sysDate) throws Exception {
        Meetings meeting = new Meetings();
        /*
        2019-1-16修改创建会议时会议所属组织的逻辑：
        原：随会议创建者所在的组织和会议类型发生变化（小组、党支部）
        新：以会议创建者所选择角色关联的组织为准
        2024：以所选组织为准
         */
        // 查询组织信息
//        String orgId = user.getCurrentRoleOrg().getId();
        String orgId = meetingsBean.getBranchOrgId();
        Organization organization = organizationSV.getByPrimaryKey(orgId);
        if (null == organization) {
            logger.error("请求参数错误：没有查询到党支部/党小组信息");
            throw new GeneralException(NO_ORG_INFO);
        }
        /*
        1、查询操作者所在党支部(支部党员大会、委员会、党课)
        2、查询操作者所在党小组(党小组会)
        3、小组会时，需要操作者为‘党小组长’
         */
        if (meetingType.equals(MeetingTypeEnum.PARTYGROUP.getCode())) {
            if (!SimpleDataEnum.ISGROUPYES.getCode().equals(organization.getIsgroup())) {
                logger.error("请求参数错误：请切换至党小组关联的角色创建小组会议");
                throw new GeneralException("PBMS_ME_1056");
            }

            List<SysUserPositionDTO> groupLeaderList = userSV.getPositionByOrgId(POST_NAME_DANGXIAOZUZHANG, orgId);
            // 2019-1-16（9460）更新不要求创建者为小组长职务，只要小组内有人为小组长职务即可
            if (CollectionUtils.isEmpty(groupLeaderList)) {
                logger.error("请求参数错误：党小组会只能党小组党务工作者角色创建");
                throw new GeneralException(SHOULD_CREATE_BY_GROUP_LEADER);
            }
        }

        if (meetingType.equals(MeetingTypeEnum.BRANCHLEADER.getCode())) {
            String postCode;
            // 判断会议是总支会议或是支部会议
            if (organization.getOrgtype() == 3) {
                postCode = "ZONGZHIWEIYUAN";
            } else if (organization.getIsBranch() == 1) {
                postCode = "ZHIBUWEIYUAN";
            } else {
                logger.error("请求参数错误：委员会议只能由党总支或党支部创建");
                throw new GeneralException(ERROR_ORG_TYPE);
            }
            List<SysUserPositionDTO> leaderList = userSV.getPositionByOrgId(postCode, orgId);
            if (CollectionUtils.isEmpty(leaderList) || leaderList.size() <= 1) {
                logger.error("请求参数错误：委员会议需要设置至少两个委员才能开展");
                throw new GeneralException(NEED_MORE_LEADER);
            }
        }


        if(meetingsBean.getMeetingsId() != null){
            meeting.setId(meetingsBean.getMeetingsId());
        }else {
            meeting.setId(UIDUtil.getUID());
        }
        //补充拓展属性
        meeting.setActivityCount(meetingsBean.getActivityCount());
        meeting.setActivityYear(meetingsBean.getActivityYear());

        meeting.setTopic(meetingsBean.getTopic());
        meeting.setType(meetingType);
        meeting.setStarttime(ConvertUtil.convertStringToDate(meetingsBean.getStarttime()));
        meeting.setEndtime(ConvertUtil.convertStringToDate(meetingsBean.getEndtime()));
        meeting.setOrgid(organization.getId());
        meeting.setOrgcode(organization.getCodestr());
        meeting.setOrgname(organization.getOrgName());
        meeting.setAddress(meetingsBean.getAddress());
        meeting.setModerator(user.getId());
        meeting.setRecorder(meetingsBean.getRecorder());
        meeting.setTeacher(meetingsBean.getTeacher());
        meeting.setDescription(meetingsBean.getDescription());
        meeting.setPrecautions(StringUtils.isBlank(meetingsBean.getPrecautions()) ? null : meetingsBean.getPrecautions().trim());
        meeting.setSigntime(meetingsBean.getSigntime());
        meeting.setIsplan((short) SimpleDataEnum.MENOTPLAN.getCode().intValue());
        meeting.setExptime(null);
        meeting.setPtaskid(null);
        meeting.setReason(null);
        meeting.setIserror((short) SimpleDataEnum.MEETINGERRORYES.getCode().intValue());
        meeting.setCreatedby(user.getId());
        meeting.setCreateddate(sysDate);
        meeting.setModifiedby(null);
        meeting.setModifieddate(null);
        meeting.setPublicmod(PUBLIC_MOD_NO);
        meeting.setPublicorgcode(null);
        meeting.setIsdeleted(DataIsDeleteEnum.NORMAL.getCode());
        meeting.setBranchOrgId(meetingsBean.getBranchOrgId());
        meeting.setPlanstart(ConvertUtil.convertStringToDate(meetingsBean.getStarttime()));
        meeting.setPlanend(ConvertUtil.convertStringToDate(meetingsBean.getEndtime()));
        meeting.setTimelinessType(MEETING_TIMELINESS_SUPPLY.equals(meetingsBean.getTimelinessType()) ? MEETING_TIMELINESS_SUPPLY : MEETING_TIMELINESS_REALTIME);

        meeting.setChannelType(StringUtils.isBlank(meetingsBean.getChannelType()) ? MEETING_TYPE_UNDERLINE : meetingsBean.getChannelType());
        meeting.setRangeType(meetingsBean.getRangeType());
        meeting.setDistributionType(meetingsBean.getDistributionType());
        meeting.setMainProcessId(meetingsBean.getMainProcessId());
        meeting.setRoomId(meetingsBean.getRoomId());
        meeting.setActualStartTime(meetingsBean.getActualStartTime());
        meeting.setActualEndTime(meetingsBean.getActualEndTime());
        meeting.setExtfld2(meetingsBean.getStudyType());//学习类型

//        meeting.setExtfld3(String.valueOf(meetingsBean.getExpectedNum())); //预计人数
        //预计人数
//        int orgUserCount = organizationSV.getOrgUserCount(organization.getCodestr());
        //参加人员+列席人员
        List<Attendances> byMeetingId = attendancesSV.getByMeetingId(meeting.getId());
        meeting.setExtfld3(String.valueOf(byMeetingId.size()));
        // 设置主流程ID，主流程ID和主会议ID相同
        meeting.setMainProcessId(meeting.getId());

        meeting.setNeedRecord(meetingsBean.getNeedRecord());

        meeting.setMeHost(meetingsBean.getMeHost());
        meeting.setMeHostId(meetingsBean.getMeHostId());
        meeting.setMeContent(meetingsBean.getMeContent());
        meeting.setTheme(meetingsBean.getTheme());
//        if (StringUtils.isNotEmpty(meetingsBean.getMeContent())) {
//            InputStream meContentStream = new ByteArrayInputStream(meetingsBean.getMeContent().getBytes());
//            Date currTime = new Date();
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
//            String fileName = simpleDateFormat.format(currTime) + ".html";
//            String contentUrl = OnestUtil.storeByStream(meContentStream, fileName, "me", organization.getProvince(), organization.getId());
//            meeting.setMeContent(contentUrl);
//        } else {
//            meeting.setMeContent(null);
//        }

        if (Integer.valueOf(1).equals(meetingsBean.getIsSave())){
            meeting.setStatus(MeetingNewStatusEnum.TOBEPLAN.getCode());
        }else {
            meeting.setStatus(MeetingNewStatusEnum.SCHEMEREVIEW.getCode());
            // 生成与会者
            // 通知是为组织下所有人员发送待办。客观情况所有人包含领导和组织者，都有党员角色，所以只发送党员角色。
            List<Users> users = meetingsSV.getConventioneerListForMe(meeting);
            conventioneerSV.insertConventioneerInBatch(meeting, users, user.getId(), sysDate, new ArrayList<>(), 1, 0);
        }

        if (StringUtils.isNotBlank(meetingsBean.getTaskIds())){
            List<String> taskRelationship = Arrays.asList(meetingsBean.getTaskIds().split(","));
            if (CollectionUtils.isNotEmpty(taskRelationship)){
                relatedTasksSaveAndUpdate(taskRelationship, meetingsBean.getMeetingsId());
            }
        }

        //如果是线上会议并且是主流程，创建分发流程支部关系
        if (MEETING_TYPE_ONLINE.equals(meetingsBean.getChannelType()) && PROCESS_TYPE_MAIN.equals(meetingsBean.getDistributionType())) {
            createProcessRelation(meeting.getOrgid(), meeting.getMainProcessId(), meetingsBean.getProcessIds());
        }

        // 新建会议时（支部党员大会），创建会议的工作检查项
        if (MeetingTypeEnum.BRANCHMASSES.getCode().equals(meetingType)) {
            insertCheckList(meeting.getId(), user.getId(), sysDate);
        }

        // 新建会议时，创建会议要求项
        insertMeetingRequire(meeting.getId(), user.getId(), sysDate, meetingType);

        // 为会议负责人生成任务待办
        // 为会议所在组织本级的党务工作者角色生成代办
        List<UserRoleForMeDTO> leaderIds = userSV.getUserPMIdsByOrgid(orgId, RoleTypeEnum.WORKER.getCode());
        meetingsSV.updateWorkTask(meeting, leaderIds, sysDate, WorkTaskClassEnum.NOTIFY.getCode(), ConvertUtil.meetingTypeToTaskType(meetingType), user.getId());

//        return meetingsSV.insertSelective(meeting);
        int result =  meetingsSV.insertSelective(meeting);
        //不是保存
        if (result > 0 && 0 == meetingsBean.getIsSave()) {
            genteateWord(meeting.getId());
            submitToApproveProcess(meeting, user, null, ApprovalProcessServiceOAImpl.BUSINESS_TYPE_ACTIVITY_PLANNING);
        }
        return result;
    }

    private void insertCheckList(String meetingId, String userId, Date sysDate) {
        // 创建check对象并赋初值
        CheckList checkList = new CheckList();
        checkList.setMeetingid(meetingId);
        checkList.setStatus(SimpleDataEnum.CHECKUNFINISHED.getCode());
        checkList.setCreatedby(userId);
        checkList.setCreateddate(sysDate);
        checkList.setModifiedby(null);
        checkList.setModifieddate(null);
        checkList.setIsdeleted(DataIsDeleteEnum.NORMAL.getCode());

        List<SelectBean> dictionaryItemList = dictionaryItemsSV.getListByDictCode("MECHECKLIST");
        for (SelectBean temp : dictionaryItemList) {
            CheckList check = checkList;
            check.setContent(temp.getName());
            check.setId(UIDUtil.getUID());
            checkListSV.insert(check);
        }
    }

    private void createProcessRelation(String mainOrgId, String mainProcessId, String processOrgIds) {
        List<String> processOrgList = processRelationSV.selectIdsByMainProcessId(mainProcessId);

        if (!processOrgList.isEmpty()) {
            processRelationSV.deleteByCollect(processOrgList);
        }

        if (processOrgIds.length() > 0) {
            String[] processOrgs = processOrgIds.split(",");
            List<ProcessRelation> processRelations = new ArrayList<>();

            String baseUid = UIDUtil.getUID(8);

            for (int i = 0; i < processOrgs.length; i++) {
//                if (!processOrgs[i].equals(mainOrgId)) {
                ProcessRelation processRelation = new ProcessRelation();
                processRelation.setId(UIDUtil.setUid(baseUid, i));
                processRelation.setMainProcessId(mainProcessId);
                processRelation.setDisOrgid(processOrgs[i]);
                processRelation.setCreated("01");
                processRelations.add(processRelation);
//                }
            }

            if (!processRelations.isEmpty()) {
                processRelationSV.insertCollect(processRelations);
            }
        }

    }

    private void insertMeetingRequire(String meetingId, String userId, Date sysDate, Integer meetingType) {
        // 创建check对象并赋初值
        MeetingRequire meetingRequire = new MeetingRequire();
        meetingRequire.setObjid(meetingId);
        meetingRequire.setIsselect(SimpleDataEnum.REQUIREUNSELECT.getCode());
        meetingRequire.setIsdeleted(DataIsDeleteEnum.NORMAL.getCode());
        meetingRequire.setCreatedby(userId);
        meetingRequire.setCreateddate(sysDate);
        meetingRequire.setModifiedby(userId);
        meetingRequire.setModifieddate(sysDate);

        List<SelectBean> dictionaryItemList = dictionaryItemsSV.getListByDictCode(ConvertUtil.meetingTypeToDictionariesCode(meetingType));
        for (SelectBean temp : dictionaryItemList) {
            MeetingRequire require = meetingRequire;
            require.setDictionaryitemid(temp.getId());
            require.setId(UIDUtil.getUID());
            meetingRequireSV.insert(require);
        }
    }

    /**
     * 导出会议通知PDF
     *
     * @param request
     * @param response
     * @throws ValidationException
     */
    @RequestMapping(value = "/exportNoticePDF", method = RequestMethod.GET)
    public void exportNoticePDF(HttpServletRequest request, HttpServletResponse response) throws GeneralException {
        String meetingid = request.getParameter("meetingid");

        Meetings meeting = meetingsSV.getByPrimaryKey(meetingid);
        if (meeting == null) {
            logger.error(NO_MEETING_ERR_MSG);
            throw new GeneralException(NO_MEETING_ERROR);
        }

        Map<String, Object> fillData = new HashMap<>(16);
        fillData.put("orgname", meeting.getOrgname());
        fillData.put("noticename", meeting.getTopic() + "通知");

        String startTimeStr = "";
        Date startTime = meeting.getStarttime();
        if (startTime != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy 年 MM 月 dd 日 HH:mm");
            startTimeStr = dateFormat.format(meeting.getStarttime());
        }
        fillData.put("starttime", startTimeStr);

        fillData.put("address", meeting.getAddress());
        fillData.put("topic", meeting.getTopic());
        SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy 年 MM 月 dd 日");
        fillData.put("currenttime", dayFormat.format(new Date()));

        if (meeting.getType() == MeetingTypeEnum.NEWSTUDY.getCode()) {
            if (MEETING_TYPE_ONLINE.equals(meeting.getChannelType())) {
                PdfUtil.exportPDF("me_notice_online", "党课通知", fillData, request, response);
            } else {
                PdfUtil.exportPDF("me_notice_unline", "党课通知", fillData, request, response);
            }
        } else if (meeting.getType() == MeetingTypeEnum.STUDY.getCode()) {
            PdfUtil.exportPDF("me_notice_study", "学习通知", fillData, request, response);
        } else {
            PdfUtil.exportPDF("me_notice", "会议通知", fillData, request, response);
        }

    }

    @RequestMapping(value = "/exportConventionerSummaryPDF", method = RequestMethod.GET)
    public void exportConventionerSummaryPDF(HttpServletRequest request, HttpServletResponse response) throws GeneralException {
        String meetingid = request.getParameter("meetingid");

        Meetings meeting = meetingsSV.getByPrimaryKey(meetingid);
        if (meeting == null) {
            logger.error(NO_MEETING_ERR_MSG);
            throw new GeneralException(NO_MEETING_ERROR);
        }

        List<MeetingConventioneerDTO> convertionerList = conventioneerSV.getConventioneerListByMeetingId(meetingid);

        Map<String, Object> fillData = new HashMap<>(16);
        fillData.put("orgname", meeting.getOrgname());
        fillData.put("noticename", meeting.getTopic() + "党员参与情况");

        String startTimeStr = "";
        Date startTime = meeting.getStarttime();
        if (startTime != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日HH时mm分");
            startTimeStr = dateFormat.format(meeting.getStarttime());
        }
        fillData.put("starttime", startTimeStr);

        String endtimeStr = "";
        if (null != meeting.getEndtime()) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("HH时mm分");
            endtimeStr = dateFormat.format(meeting.getEndtime());
        }
        fillData.put("endtime", endtimeStr);

        // 列席人员
        List<Attendances> attendancesList = attendancesSV.getByMeetingId(meetingid);
        if (CollectionUtils.isNotEmpty(attendancesList)) {
            String attendanceStr = "列席人员：";
            for (Attendances temp : attendancesList) {
                attendanceStr += temp.getPersonName() + "：" + temp.getPersonPhone() + "；";
            }
            fillData.put("attendancesList", attendanceStr);
        }

        fillData.put("address", meeting.getAddress());
        fillData.put("topic", meeting.getTopic());
        List<Map<String, Object>> list = new ArrayList<>(10);
        for (MeetingConventioneerDTO con : convertionerList) {
            Map<String, Object> item = new HashMap<>(16);
            item.put("username", con.getUsername());
            item.put("phoneNumber", con.getPhoneNumber());
            item.put("orgname", con.getOrgname());

            // 处理确定参会时间
            String confirmTimeStr = "";
            Date confirmTime = con.getConfirmtime();
            if (confirmTime != null) {
                SimpleDateFormat confirmDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                confirmTimeStr = confirmDateFormat.format(confirmTime);
            }
            item.put("confirmtime", confirmTimeStr);

//            item.put("staJoin", getIsJoinDescription(con.getStaJoin()));

            // 参与情况
            item.put("isjoin", getIsJoinDescription(con.getIsjoin()));

            //判断如果是请假则填写备注是请假原因，如果是签到则填写备注请假地址
            if (1 == con.getIsjoin()) {
                item.put("remarks", con.getSignAddress());
            } else {
                item.put("remarks", con.getReason());
            }

            list.add(item);
        }
        fillData.put("list", list);

        if (meeting.getType() == MeetingTypeEnum.STUDY.getCode()) {
            PdfUtil.exportPDF("me_conventioner_study_201907301", "党员参与情况汇总表", fillData, request, response);
        } else {
            PdfUtil.exportPDF("me_conventioner201907301", "党员参与情况汇总表", fillData, request, response);
        }

    }


    @RequestMapping(value = "/exportRecordSummaryPDF", method = RequestMethod.GET)
    public void exportRecordSummaryPDF(Integer download, String meetingid, HttpServletRequest request, HttpServletResponse response) throws GeneralException {
        ValidateUtil.isNotNull(meetingid);
        if (null == download || 0 != download)
            download = Integer.valueOf(1);

        Meetings meeting = meetingsSV.getByPrimaryKey(meetingid);
        if (meeting == null) {
            logger.error(NO_MEETING_ERR_MSG);
            throw new GeneralException(NO_MEETING_ERROR);
        }

        List<MeetingConventioneerDTO> convertionerList = conventioneerSV.getSubmitRecordListByMeetingId(meetingid);

        Map<String, Object> fillData = new HashMap<>(16);
        fillData.put("orgname", meeting.getOrgname());

        String startTimeStr = "";
        Date startTime = meeting.getStarttime();
        if (startTime != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy 年 MM 月 dd 日 HH:mm");
            startTimeStr = dateFormat.format(meeting.getStarttime());
        }
        fillData.put("starttime", startTimeStr);

        fillData.put("address", meeting.getAddress());
        fillData.put("topic", meeting.getTopic());
        SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy 年 MM 月 dd 日");
        fillData.put("currenttime", dayFormat.format(new Date()));

        fillData.put("title", meeting.getOrgname() + meeting.getTopic());

        if (meeting.getType() == MeetingTypeEnum.STUDY.getCode()) {
            fillData.put("noticename", meeting.getTopic() + "党员学习记录情况");
            fillData.put("desc", meeting.getOrgname() + "于" + startTimeStr + "在" + meeting.getAddress() + "召开" + meeting.getTopic() + "，学习记录情况如下：");
        } else {
            fillData.put("noticename", meeting.getTopic() + "党员会议记录情况");
            fillData.put("desc", meeting.getOrgname() + "于" + startTimeStr + "在" + meeting.getAddress() + "召开" + meeting.getTopic() + "，会议记录情况如下：");
        }

        List<Map<String, Object>> list = new ArrayList<>(10);
        // 获取当前地址
//        String domain = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + "/";

//        List<String> filePaths = new ArrayList<String>();
        for (MeetingConventioneerDTO con : convertionerList) {
            Map<String, Object> item = new HashMap<>(16);
            item.put("username", con.getUsername());
//            String filePath = checkFilePathFromStorage(request.getSession().getId(), HEADIMG_URL);
//            filePaths.add(filePath);
//            logger.info("2222222222222222222" + filePath);
            item.put("headImg", HEADIMG_URL);
            item.put("phoneNumber", con.getPhoneNumber());
            item.put("orgname", con.getOrgname());

            String recordContent = "";
            if (con.getMinutecontent() != null && !con.getMinutecontent().isEmpty()) {
                recordContent = OnestUtil.getContent(con.getMinutecontent());
                if (null != recordContent) {
                    recordContent = recordContent.replaceAll("&amp;nbsp;", " ");
                    recordContent = recordContent.replaceAll("&nbsp;", " ");
                }
            }
            item.put("recordContent", recordContent);

            // 获取会议记录/心得体会附件
            List<Attachments> attachmentList = attachmentsSV.getListByObjId(con.getId());
            if (attachmentList != null && !attachmentList.isEmpty()) {
                List<String> attlist = new ArrayList<>();
                for (Attachments attachment : attachmentList) {
//                    logger.info("aaaaaaaaaaaaaaaaa" + attachment.getId() + attachment.getPrivateUrl());
//                    filePath = checkFilePathFromStorage(request.getSession().getId(), attachment.getPrivateUrl());
                    String attUrl = attachment.getUrl();
                    if (attUrl == null || attUrl.isEmpty()) attUrl = attachment.getUrl();
                    attlist.add(OnestUtil.replaceONestUrlToCoreUrlForPDF(attUrl));
//                    filePaths.add(filePath);
                }
                item.put("recordImg", attlist);
            }

            // 处理提交时间
            String submitTimeStr = "";
            Date submitTime = con.getSubtime();
            if (submitTime != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                submitTimeStr = dateFormat.format(submitTime);
            }
            item.put("submittime", submitTimeStr);

            // 会议记录/心得提交情况
            item.put("issubmitrecord", con.getStatus() == 300 ? "已提交" : "未提交");

            list.add(item);
        }

//        for (String filePath : filePaths) {
//            File file = new File(filePath);
//            if (file.exists())
//                file.delete();
//        }
        fillData.put("list", list);

        if (meeting.getType() == MeetingTypeEnum.STUDY.getCode()) {
            PdfUtil.exportPDF(download, "me_record_study", "党员学习记录汇总表", fillData, request, response);
        } else {
            PdfUtil.exportPDF(download, "me_record", "党员会议记录汇总表", fillData, request, response);
        }
    }

    @RequestMapping(value = "/exportMeetingSummaryPDF", method = RequestMethod.GET)
    public void exportMeetingSummaryPDF(HttpServletRequest request, HttpServletResponse response) throws GeneralException {
        String meetingid = request.getParameter("meetingid");

        Meetings meeting = meetingsSV.getByPrimaryKey(meetingid);
        if (meeting == null) {
            logger.error(NO_MEETING_ERR_MSG);
            throw new GeneralException(NO_MEETING_ERROR);
        }

        List<MeetingConventioneerDTO> convertionerList = conventioneerSV.getConventioneerListByMeetingId(meetingid);

        Map<String, Object> fillData = new HashMap<>(16);
        fillData.put("orgname", meeting.getOrgname());
        fillData.put("noticename", meeting.getTopic() + "汇总情况");

        String startTimeStr = "";
        Date startTime = meeting.getStarttime();
        if (startTime != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy 年 MM 月 dd 日 HH:mm");
            startTimeStr = dateFormat.format(meeting.getStarttime());
        }
        fillData.put("starttime", startTimeStr);

        fillData.put("address", meeting.getAddress());
        fillData.put("topic", meeting.getTopic());
        SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy 年 MM 月 dd 日");
        fillData.put("currenttime", dayFormat.format(new Date()));

        int joinCount = 0, leaveCount = 0, unconfirmedCount = 0;
        List<Map<String, Object>> list = new ArrayList<>(10);
        for (MeetingConventioneerDTO con : convertionerList) {
            Map<String, Object> item = new HashMap<>(16);
            item.put("username", con.getUsername());
            item.put("phoneNumber", con.getPhoneNumber());
            item.put("orgname", con.getOrgname());

            // 处理确定参会时间
            String confirmTimeStr = "";
            Date confirmTime = con.getConfirmtime();
            if (confirmTime != null) {
                SimpleDateFormat confirmDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                confirmTimeStr = confirmDateFormat.format(confirmTime);
            }
            item.put("confirmtime", confirmTimeStr);

            // 参与情况
            item.put("isjoin", getIsJoinDescription(con.getIsjoin()));

            // 处理提交时间
            String submitTimeStr = "";
            Date submitTime = con.getSubtime();
            if (submitTime != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                submitTimeStr = dateFormat.format(submitTime);
            }
            item.put("submittime", submitTimeStr);

            // 会议记录/心得提交情况
            item.put("issubmitrecord", con.getStatus() == 300 ? "已提交" : "未提交");

            // 根据会议记录/心得体会文件地址读取内容
            if (!StringUtil.isBlank(con.getMinutecontent())) {
                String content = OnestUtil.getContent(con.getMinutecontent());
                item.put("content", content);
            }

            // 获取会议记录/心得体会附件
            List<Attachments> attachmentList = attachmentsSV.getListByObjIdType(con.getId(), AttachTypeEnum.ME_RECORD.getType());
            if (attachmentList != null && attachmentList.isEmpty()) {
                // 获取当前地址
                // String domain = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort();
                List<String> attlist = new ArrayList<>(10);
                for (Attachments attachment : attachmentList) {
                    if (isPicture(attachment.getUrl())) {
                        // attlist.add(domain + attachment.getUrl());
                        attlist.add(OnestUtil.replaceONestUrlToCoreUrlForPDF(attachment.getUrl()));
                    }
                }
                item.put("attachmentlist", attlist);
            }

            // 判断参会人种类
            switch (con.getIsjoin()) {
                case 0:
                    unconfirmedCount++;
                    break;
                case 1:
                    joinCount++;
                    break;
                case 2:
                    leaveCount++;
                    break;
                default:
                    break;
            }

            list.add(item);
        }

        // 参会人分布情况
        fillData.put("total", convertionerList.size());
        fillData.put("unconfirmedcount", unconfirmedCount);
        fillData.put("joincount", joinCount);
        fillData.put("leavecount", leaveCount);

        fillData.put("list", list);

        if (meeting.getType() == MeetingTypeEnum.STUDY.getCode()) {
            PdfUtil.exportPDF("me_summary_study", "集中学习汇总情况", fillData, request, response);
        } else {
            PdfUtil.exportPDF("me_summary", "会议汇总情况", fillData, request, response);
        }

    }

    /**
     * 判断地址是否为图片资源
     *
     * @param url
     * @return
     */
    private boolean isPicture(String url) {
        String[] imageExtensionArr = {"bmp", "dib", "gif", "jfif", "jpe", "jpeg", "jpg", "png", "tif", "tiff", "ico"};

        String extension = url.substring(url.lastIndexOf(".") + 1).toLowerCase();

        return Arrays.asList(imageExtensionArr).contains(extension);
    }

    /**
     * 获取与会情况描述
     *
     * @param isJoin
     * @return
     */
    private String getIsJoinDescription(Short isJoin) {
        String description;
        switch (isJoin) {
            case 0:
                description = "未参加";
                break;
            case 1:
                description = "准时参加";
                break;
            case 2:
                description = "请假";
                break;
            case 3:
                description = "未参加（请假未批准）";
                break;
            default:
                description = "";
                break;
        }
        return description;
    }

    @RequestMapping(value = "/getMeetingsBySummary", method = RequestMethod.POST)
    @ApiOperation(value = "查询全网会议列表", notes = "查询全网会议列表")
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<MeetingsDTO> getMeetingsBySummary(VMeetingsSummaryListSearchBean vMeetingsSummaryListSearchBean, String orgCode) throws GeneralException {
        //校验页面提交的参数是否合法，结果集会保存每一条检验失败的信息
        Set<ConstraintViolation<VMeetingsSummaryListSearchBean>> constraintViolations = validator.validate(vMeetingsSummaryListSearchBean);
        // 如果校验不通过，则结果集中会保存每一条校验失败的信息，判断结果集长度即可知道是否通过校验
        if (!constraintViolations.isEmpty()) {
            throw new ValidationException("PBMS_SYS_0033", constraintViolations.iterator().next().getMessage());
        }

        // 数据权限规则
        vMeetingsSummaryListSearchBean.setCurrentOrgCode(StringUtils.isBlank(orgCode) ? null : orgCode);

        return meetingsSV.selectListByParams(vMeetingsSummaryListSearchBean, "desc");
    }

    @RequestMapping(value = "/submitMinutes", method = RequestMethod.POST)
    @ApiOperation(value = "提交会议纪要", notes = "提交会议纪要",
            consumes = "application/x-www-form-urlencoded",
            produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "meetingId", value = "会议唯一标识", required = true, dataType = "String", paramType = "query")})
    public int submitMinutes(String meetingId) throws GeneralException {
        // 会议信息有效性验证
        Meetings meetings = meetingsSV.getByPrimaryKey(meetingId);
        if (null == meetings) {
            logger.error(NO_MEETING_ERR_MSG);
            throw new GeneralException(NO_MEETING_ERROR);
        }

        // 校验会议纪要（支部党员大会、支部委员会、党小组会必须上传）
        if (meetings.getType().equals(MeetingTypeEnum.BRANCHMASSES.getCode())
                || meetings.getType().equals(MeetingTypeEnum.BRANCHLEADER.getCode())
                || meetings.getType().equals(MeetingTypeEnum.PARTYGROUP.getCode())) {
            List<Attachments> summaryList = attachmentsSV.getListByObjIdType(meetingId, AttachTypeEnum.ME_MINUTES.getType());
            if (CollectionUtils.isEmpty(summaryList)) {
                logger.error("提交审核前请上传会议纪要");
                throw new GeneralException(MINUTES_NOT_SUBMIT);
            }
        } else {
            logger.error("党课不做会议纪要审核");
            throw new GeneralException(DK_NOT_NEED_MINUTES);
        }

        Date currentTime = new Date(); // 系统当前时间
        Users currentUser = getUser(); // 系统当前用户
        meetings.setSubmitTime(currentTime);
        meetings.setMinutesStatus(MinutesStatusEnum.UNREVIEW.getCode());
        meetings.setModifiedby(currentUser.getId());
        meetings.setModifieddate(currentTime);

        // 为领导生成待办和推送消息
        doTaskAndMsgForSubmit(currentTime, currentUser, meetings);

        // 生成操作记录
        ReviewLog log = new ReviewLog();
        log.setId(UIDUtil.getUID());
        log.setProcessType(ProcessTypeEnum.MINUTES_REVIEW.getCode());
        log.setObjectId(meetingId);
        log.setOperationType(1);
        log.setOperationDesc("");
        log.setOperationUserId(currentUser.getId());
        log.setOperationUserName(currentUser.getUsername());
        log.setOperationTime(currentTime);
        log.setResuseReason(null);
        log.setRemark(null);
        log.setUserOrgId(currentUser.getOrgid());
        log.setUserOrgName(organizationSV.getByPrimaryKey(currentUser.getOrgid()).getOrgsname());
        reviewLogSV.insert(log);

        return meetingsSV.updateByPrimaryKeySelective(meetings);
    }

    @RequestMapping(value = "/modifyMinutes", method = RequestMethod.POST)
    @ApiOperation(value = "保存/提交会议纪要", notes = "保存/提交会议纪要",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "meetingid", value = "会议唯一标识", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isSubmit", value = "操作标识：true-提交；false-保存", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "content", value = "会议纪要文本内容", required = true, dataType = "String", paramType = "query")})
    public int modifyMinutes(String meetingid, boolean isSubmit, String content) throws GeneralException {
        Date currentTime = new Date(); // 系统当前时间
        Users currentUser = getUser(); // 系统当前用户

        // 会议信息有效性验证
        Meetings meetings = meetingsSV.getByPrimaryKey(meetingid);
        if (null == meetings) {
            logger.error("未能查询到会议信息");
            throw new GeneralException(NO_MEETING_ERROR);
        }

        if (isSubmit) {
            // 校验会议纪要（支部党员大会、支部委员会、党小组会必须上传）
            if (meetings.getType().equals(MeetingTypeEnum.BRANCHMASSES.getCode())
                    || meetings.getType().equals(MeetingTypeEnum.BRANCHLEADER.getCode())
                    || meetings.getType().equals(MeetingTypeEnum.PARTYGROUP.getCode())) {
                List<Attachments> summaryList = attachmentsSV.getListByObjIdType(meetingid, AttachTypeEnum.ME_MINUTES.getType());
                if (CollectionUtils.isEmpty(summaryList)) {
                    logger.error("提交审核前请上传会议纪要");
                    throw new GeneralException(MINUTES_NOT_SUBMIT);
                }
            } else {
                logger.error("党课不做会议纪要审核");
                throw new GeneralException(DK_NOT_NEED_MINUTES);
            }
        }

        // 会议纪要文字生成文件并上传
        String contentUrl;
        if (StringUtils.isBlank(meetings.getMinutesContent())) {
            contentUrl = saveContentToOnest(currentUser, content, false, null, null, meetingid);
        } else {
            String[] urlArray = meetings.getMinutesContent().split("/");
            StringBuilder oldKey = new StringBuilder(); // key
            for (int i = 2; i < urlArray.length; i++) {
                oldKey.append(urlArray[i]);

                if (i != urlArray.length - 1) {
                    oldKey.append("/");
                }
            }
            // 保存到 ONest
            contentUrl = saveContentToOnest(currentUser, content, true, urlArray[1], oldKey.toString(), meetingid);
        }

        // 会议更新
        meetings.setMinutesContent(contentUrl);
        meetings.setSubmitTime(isSubmit ? currentTime : null);
        meetings.setMinutesStatus(isSubmit ? MinutesStatusEnum.UNREVIEW.getCode() : MinutesStatusEnum.UNSUBMIT.getCode());
        meetings.setModifiedby(currentUser.getId());
        meetings.setModifieddate(currentTime);

        if (isSubmit) {
            // 为领导生成待办和推送消息
            doTaskAndMsgForSubmit(currentTime, currentUser, meetings);
            // 生成操作记录
            ReviewLog log = new ReviewLog();
            log.setId(UIDUtil.getUID());
            log.setProcessType(ProcessTypeEnum.MINUTES_REVIEW.getCode());
            log.setObjectId(meetingid);
            log.setOperationType(1);
            log.setOperationDesc("");
            log.setOperationUserId(currentUser.getId());
            log.setOperationUserName(currentUser.getUsername());
            log.setOperationTime(currentTime);
            log.setResuseReason(null);
            log.setRemark(null);
            log.setUserOrgId(currentUser.getOrgid());
            log.setUserOrgName(organizationSV.getByPrimaryKey(currentUser.getOrgid()).getOrgsname());
            reviewLogSV.insert(log);
        }

        return meetingsSV.updateByPrimaryKeySelective(meetings);
    }

    @RequestMapping(value = "/createSign", method = RequestMethod.POST)
    @ApiOperation(value = "根据会议ID生成签到表", notes = "根据会议ID生成签到表",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "meetingsId", value = "会议唯一标识", required = true, dataType = "String", paramType = "query")})
    public int createSign(String meetingsId) throws GeneralException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy 年 MM 月 dd 日 HH:mm");
        Meetings meetings = meetingsSV.getByPrimaryKey(meetingsId);
        List<MeetingConventioneerDTO> conventioneerDTOS = conventioneerSV.getConventioneerListByMeetingId(meetings.getId());
        //准时参加人数
        int join = 0;
        //请假人数
        int leave = 0;
        //缺勤人数
        int absence = 0;
        int total = conventioneerDTOS.size();

        List<Map<String, Object>> list = new ArrayList<>();

        for (MeetingConventioneerDTO con : conventioneerDTOS) {
            if (con.getIsjoin() == 0) {
                //判断缺勤人员
                absence++;
            } else if (con.getIsjoin() == 1) {
                //判断参加人员
                join++;
            } else if (con.getIsjoin() == 2) {
                //判断请假人员
                leave++;
            }

            if (null != con.getConfirmtime()) {
                con.setConfirmtimeStr(dateFormat.format(con.getConfirmtime()));
            }
            Map<String, Object> item = new HashMap<>(16);
            item.put("username", con.getUsername());
            item.put("phoneNumber", con.getPhoneNumber());
            item.put("orgname", con.getOrgname());

            // 处理确定参会时间
            String confirmTimeStr = "";
            Date confirmTime = con.getConfirmtime();
            if (confirmTime != null) {
                SimpleDateFormat confirmDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                confirmTimeStr = confirmDateFormat.format(confirmTime);
            }
            item.put("confirmtime", confirmTimeStr);

            // 参与情况
            item.put("isjoin", getIsJoinDescription(con.getIsjoin()));

            //判断如果是请假则填写备注是请假原因，如果是签到则填写备注请假地址
            if (1 == con.getIsjoin()) {
                item.put("remarks", con.getSignAddress());
            } else {
                item.put("remarks", con.getReason());
            }

            list.add(item);
        }

        Map<String, Object> signContent = new HashMap<>();
        signContent.put("meeting", meetings);
        signContent.put("topic", meetings.getTopic().concat("签到表"));
        signContent.put("joincount", join);
        signContent.put("leavecount", leave);
        signContent.put("unconfirmedcount", absence);
        signContent.put("total", total);
        signContent.put("list", list);

        InputStream signStream = null;
        try {
            signStream = createSignPDF("me_sign_201908051", signContent);

            String aid = UIDUtil.getUID();

            //调用onest工具上传到附件服务器
            String url = OnestUtil.storeByStream(signStream, aid.concat(".pdf"), AttachTypeEnum.ME_SIGN.getType(), null, null);
            // 插入到附件表
            if (StringUtils.isNotBlank(url)) {
                //20250213 提出只保留一份签到表
                List<Attachments> meSignAttanchments = attachmentsSV.selectListByObjIdType(meetings.getId(), AttachTypeEnum.ME_SIGN.getType());
                List<String> needDelCollect = meSignAttanchments.stream()
                        .filter(f -> "签到表.pdf".equals(f.getAttname()))
                        .map(Attachments::getId)
                        .collect(Collectors.toList());
                needDelCollect.forEach(e->attachmentsSV.deleteByPrimaryKey(e));
                String nurl = OnestUtil.subStringUrl(url);
                Attachments attachments = new Attachments();
                attachments.setId(aid);
                attachments.setObjid(meetings.getId());
                attachments.setAttname("签到表.pdf");
                attachments.setUrl(nurl);
                attachments.setIsdeleted(0);
                attachments.setFilesize(signStream.available());
                attachments.setAtttype(AttachTypeEnum.ME_SIGN.getType());
                attachments.setCreateddate(new Date());
                attachments.setCreatedby("system");
                return attachmentsSV.insertSelective(attachments);
            }
        } catch (com.lowagie.text.DocumentException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("");
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("");
        } finally {
            if (null != signStream) {
                try {
                    signStream.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
        return 0;
    }

    @RequestMapping(value = "/createRecord", method = RequestMethod.POST)
    @ApiOperation(value = "根据会议ID生成会议记录", notes = "根据会议ID生成签到表",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = Integer.class)
    public int createRecord(String meetingsId) throws GeneralException {
        try {
            MeetingsDetailDTO meetingsDetailDTO = meetingsSV.getFullDetailById(meetingsId, getUser());

            // 获取数据
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("meetingName", meetingsDetailDTO.getTopic());
            dataMap.put("meetingTime", meetingsDetailDTO.getStarttime());
            dataMap.put("location", meetingsDetailDTO.getAddress());
            dataMap.put("host", meetingsDetailDTO.getMeHost());
            dataMap.put("recorder", meetingsDetailDTO.getRecorderName());
            dataMap.put("eventTurnout", meetingsDetailDTO.getExpectedNum());
            dataMap.put("checkInCount", meetingsDetailDTO.getAttendanceNum());

            // 获取缺席党员
            VMeetingConventioneerSearchBean vMeetingConventioneerSearchBean = new VMeetingConventioneerSearchBean();
            vMeetingConventioneerSearchBean.setMeetingid(meetingsId);
            vMeetingConventioneerSearchBean.setIsjoin(2);
            vMeetingConventioneerSearchBean.setPage(1);
            vMeetingConventioneerSearchBean.setLimit(9999);
            vMeetingConventioneerSearchBean.setSearchAll(false);
            PageInfo<MeetingConventioneerDTO> conventioneerListByParams = conventioneerSV.getConventioneerListByParams(vMeetingConventioneerSearchBean);
            List<MeetingConventioneerDTO> list = conventioneerListByParams.getList();
            Optional.ofNullable(list)
                .map(l -> l.stream()
                    .map(item -> item.getUsername() + "：" + item.getReason())
                    .collect(Collectors.joining("；")))
                .ifPresent(p -> dataMap.put("reasonsForAbsence", p));

            // 出席人员和职务
            PageInfo<Attendances> byMeetingId = attendancesSV.getByMeetingId(1, 9999, meetingsId, "no", "all", null, 0);
            List<Attendances> byMeetingIdList = byMeetingId.getList();
            Optional.ofNullable(byMeetingIdList)
                    .map(l->
                            l.stream()
                                    .map(item->item.getPersonName()+"("+item.getPersonPosition()+")")
                                    .collect(Collectors.joining("、")))
                    .ifPresent(p-> dataMap.put("attendeesAndTitles", p));

            // 获取列席人员姓名和职务（personType = 1）
            Optional.ofNullable(byMeetingIdList)
                    .map(l -> l.stream()
                            .filter(item -> item.getPersonType() != null && item.getPersonType() == 1) // 过滤列席人员
                            .map(item -> item.getPersonName() + "（" + (item.getPersonPosition() != null ? item.getPersonPosition() : "") + "）")
                            .collect(Collectors.joining("、")))
                    .ifPresent(p -> dataMap.put("position", p));

            dataMap.put("content", meetingsDetailDTO.getRecordContent());

            // 获取模板
            String templatesName = "MeetingReportingAndRecordingTemplate.ftl";
            InputStream templatesIs = this.getClass().getResourceAsStream("/templates/" + templatesName);

            // 处理模板并生成文档内容
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            FreemarkerUtils.processTemplateByStream("templates-" + templatesName, templatesIs, outputStream, dataMap);

            // 上传到ONest
            String aid = UIDUtil.getUID();
            String fileName = aid + ".doc";
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            String url = OnestUtil.storeByStream(inputStream, fileName, AttachTypeEnum.ME_MINUTES.getType(), null, null);

            // 插入到附件表
            Assert.isTrue(StringUtils.isNotBlank(url), "生成文件失败，请联系管理员。");

            //20250213提出 会议记录文件只保留最后一份
            List<Attachments> meSignAttanchments = attachmentsSV.selectListByObjIdType(meetingsId, AttachTypeEnum.ME_MINUTES.getType());
            List<String> needDelCollect = meSignAttanchments.stream()
                    .filter(f -> "会议记录.doc".equals(f.getAttname()))
                    .map(Attachments::getId)
                    .collect(Collectors.toList());
            needDelCollect.forEach(e->attachmentsSV.deleteByPrimaryKey(e));

            String nurl = OnestUtil.subStringUrl(url);
            Attachments attachments = new Attachments();
            attachments.setId(aid);
            attachments.setObjid(meetingsId);
            attachments.setAttname("会议记录.doc");
            attachments.setUrl(nurl);
            attachments.setIsdeleted(0);
            attachments.setFilesize(outputStream.size());
            attachments.setAtttype(AttachTypeEnum.ME_MINUTES.getType());
            attachments.setCreateddate(new Date());
            attachments.setCreatedby("system");
            return attachmentsSV.insertSelective(attachments);

        } catch (Exception e) {
            logger.error("生成会议记录文件失败", e);
            throw new GeneralException("生成会议记录文件失败：" + e.getMessage());
        }
    }

    private InputStream createSignPDF(String templateName, Map<String, Object> fillData) throws GeneralException, IOException, com.lowagie.text.DocumentException {
        String fontPath = FileUtil.checkFilePath("pdfconfig/fonts/", "simhei.ttf");
        fontPath = URLDecoder.decode(fontPath + "simhei.ttf", "utf-8");
        ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();

        // 构造上下文，构造填充数据
        Context context = new Context();
        for (Map.Entry<String, Object> entry : fillData.entrySet()) {
            context.setVariable(entry.getKey(), entry.getValue());
        }

        // 设置模版解析器
        FileTemplateResolver resolver = new FileTemplateResolver();
        // 模板所在目录，path。
        resolver.setPrefix(FileUtil.checkFilePath("pdfconfig/templates/", templateName + ".html"));
        // 模板文件后缀
        resolver.setSuffix(".html");

        // 设置模板引擎
        TemplateEngine templateEngine = new TemplateEngine();
        templateEngine.setTemplateResolver(resolver);

        // 渲染模板
        String htmlContent = templateEngine.process(templateName, context);

        // 生成pdf
        ITextRenderer render = new ITextRenderer();
        ITextFontResolver fontResolver = render.getFontResolver();
        logger.info("fontPath=" + fontPath);
        fontResolver.addFont(fontPath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        render.setDocumentFromString(htmlContent);
        // 解决图片相对路径的问题，若设置的域名，则会追加域名
        if (fillData.containsKey("domain")) {
            render.getSharedContext().setBaseURL(fillData.get("domain").toString());
        }
        render.layout();
        render.createPDF(pdfOutputStream);

        InputStream inputStream = new ByteArrayInputStream(pdfOutputStream.toByteArray());

        return inputStream;
    }

    private void doTaskAndMsgForSubmit(Date currentTime, Users currentUser, Meetings meetings) throws GeneralException {
//        // 为领导生成待办
//        List<Integer> roleTypes = new ArrayList<>(10);
//        roleTypes.add(RoleTypeEnum.LEADER.getCode());
//        // 分公司组织机构编码
//        String orgCode = meetings.getOrgcode();
//        String comOrgCode;
//        if (orgCode.indexOf(".") >= 0) {
//            comOrgCode = meetings.getOrgcode().substring(0, meetings.getOrgcode().indexOf("."));
//        } else {
//            comOrgCode = orgCode;
//        }
//        List<Users> users = userSV.getUserByOrgCodeAndRole(comOrgCode, roleTypes);

        // 任务编号：536 党小组会，由所属党支部党务工作者角色进行审核，其他会议审核则由本级党组领导角色审核
        Integer isBranch = 1; // 党支部标识 1-基层党支部
        String meetingTinyType; // 会议小类型，01-党支部会议，02-党小组会，03-党总支会议
        List<UserRoleForMeDTO> userRoleList;
        if (MeetingTypeEnum.PARTYGROUP.getCode().equals(meetings.getType())) {
            // 查询党小组所在支部的党务工作者角色的人员
            Organization org = getOrganization(meetings.getOrgid(), false);
            userRoleList = userSV.getUserPMIdsByOrgid(org.getId(), RoleTypeEnum.WORKER.getCode());
            meetingTinyType = "02";
        } else {
            userRoleList = userSV.getUserPMIdsByOrgid(meetings.getOrgid(), RoleTypeEnum.LEADER.getCode());
            Organization organization = organizationSV.getByPrimaryKey(meetings.getOrgid());
            if (isBranch.equals(organization.getIsBranch())) {
                meetingTinyType = "01";
            } else {
                meetingTinyType = "03";
            }
        }

        List<Users> userList = new ArrayList<>(10);
        if (CollectionUtils.isNotEmpty(userRoleList)) {
            for (UserRoleForMeDTO temp : userRoleList) {
                Users user = new Users();
                user.setId(temp.getUserid());
                user.setCurrentRoleId(temp.getRoleid());
                user.setTelephones(temp.getTelephones());
                userList.add(user);
            }
        }

        if (!CollectionUtils.isEmpty(userList)) {
            workTaskSV.insertTaskInBatch(meetings, userList, currentUser.getId(), currentTime,
                    "【会议纪要审核】" + meetings.getTopic(), meetings.getDescription(), WorkTaskClassEnum.REVIEW.getCode());

            // 生成推送消息（人员去重）
            Map<String, Object> param = new HashMap<>(16);
            param.put("txt_businessType", "会议");
            param.put("txt_businessTheme", meetings.getTopic());
            param.put("txt_action", "审核");
            param.put("txt_businessModule", "三会一课");
            param.put("rspKey", "rspId003");
            pushMsgSV.insertMsgInBatchWithUserInfo(meetings.getId(), userList, currentUser.getId(), comService.getNoticDate("rspId003"),
                    "【会议纪要审核】" + meetings.getTopic(), MSG_TYPE_IMPORT, meetingPlanSV_TYPE_MEETING, param);
        } else {
            logger.error("请先设置审核会议纪要的人员");
            if ("01".equals(meetingTinyType)) {
                throw new GeneralException("PBMS_ME_1053");
            }
            if ("02".equals(meetingTinyType)) {
                throw new GeneralException("PBMS_ME_1054");
            }
            if ("03".equals(meetingTinyType)) {
                throw new GeneralException("PBMS_ME_1055");
            }
        }
    }

    @RequestMapping(value = "/reviewMinutes", method = RequestMethod.POST)
    @ApiOperation(value = "会议纪要审核", notes = "会议纪要审核",
            consumes = "application/x-www-form-urlencoded",
            produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "objId", value = "会议唯一标识", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "result", value = "审核结果：true-审核通过；false-审核拒绝", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "refuseReason", value = "审核拒绝原因（审核拒绝时，该参数必填）", required = true, dataType = "String", paramType = "query")})
    public int reviewMinutes(String objId, boolean result, String refuseReason) throws GeneralException {
        // 参数校验
        if (StringUtils.isBlank(objId)) {
            logger.error("请求参数错误：错误的会议ID：" + objId);
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR);
        }
        if (!result && StringUtils.isBlank(refuseReason)) {
            logger.error("请求参数错误，拒绝原因不能为空");
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR);
        }
        // 会议信息有效性验证
        Meetings meetings = meetingsSV.getByPrimaryKey(objId);
        if (null == meetings) {
            logger.error(NO_MEETING_ERR_MSG);
            throw new ValidationException(NO_MEETING_ERROR);
        }
        Date currentTime = new Date(); // 系统当前时间
        Users currentUser = getUser(); // 系统当前用户
        // 设置更新属性信息
        meetings.setModifieddate(currentTime);
        meetings.setModifiedby(currentUser.getId());
        if (result) {
            meetings.setMinutesStatus(MinutesStatusEnum.REVIEWPASS.getCode());
            meetings.setRefuseReason("");
        } else {
            meetings.setMinutesStatus(MinutesStatusEnum.REVIEWREFUSE.getCode());
            meetings.setRefuseReason(refuseReason);
        }

        // 为组织者结束待办
        workTaskSV.doneByObjidAndType(meetings.getId(), ConvertUtil.meetingTypeToTaskType(meetings.getType()),
                SimpleDataEnum.DATADONE.getCode(), currentUser.getId(), currentTime, WorkTaskClassEnum.REVIEW.getCode());
        // 推送消息
        List<Integer> roleTypes = new ArrayList<>(10);
        roleTypes.add(RoleTypeEnum.WORKER.getCode());
        List<Users> users = userSV.getUserByOrgCodeAndRole(meetings.getOrgcode(), roleTypes, false);
        if (!CollectionUtils.isEmpty(users)) {
            Map<String, Object> param = new HashMap<>(16);
            param.put("txt_businessType", "会议");
            param.put("txt_businessTheme", meetings.getTopic());
            param.put("txt_action", result ? "审核通过" : "审核拒绝，原因是" + refuseReason);
            param.put("txt_businessModule", "三会一课");
            param.put("rspKey", "rspId005");
            pushMsgSV.insertMsgInBatchWithUserInfo(meetings.getId(), users, currentUser.getId(), comService.getNoticDate("rspId005"),
                    "【会议纪要审核】" + meetings.getTopic(), MSG_TYPE_IMPORT, meetingPlanSV_TYPE_MEETING, param);
        }

        // 生成操作记录
        ReviewLog log = new ReviewLog();
        log.setId(UIDUtil.getUID());
        log.setProcessType(ProcessTypeEnum.MINUTES_REVIEW.getCode());
        log.setObjectId(objId);
        log.setOperationType(result ? 2 : 3);
        log.setOperationDesc("");
        log.setOperationUserId(currentUser.getId());
        log.setOperationUserName(currentUser.getUsername());
        log.setOperationTime(currentTime);
        log.setResuseReason(result ? null : refuseReason);
        log.setRemark(null);
        log.setUserOrgId(currentUser.getOrgid());
        log.setUserOrgName(organizationSV.getByPrimaryKey(currentUser.getOrgid()).getOrgsname());
        reviewLogSV.insert(log);

        return meetingsSV.updateByPrimaryKeySelective(meetings);
    }

    private String saveContentToOnest(Users currentUser, String content, boolean isDeleteOldFile, String bucketName, String oldKey, String meetingid) throws GeneralException {
        InputStream contentInputStream = new ByteArrayInputStream(content.getBytes());
        String fileName = meetingid + new Random().nextInt(10000) + ".html";
        String contentUrl = null;

        try {
            // 存储到oNest并获取存储路径
            contentUrl = OnestUtil.storeByStream(contentInputStream, fileName, "meonrecord", currentUser.getProvince(), currentUser.getOrgid());

            // 删除oNest的旧文件
            if (isDeleteOldFile) {
                ONestUtil.deleteObject(bucketName, oldKey);
            }

            return contentUrl;
        } catch (StorageException e) {
            logger.error(e.getMessage(), e);
            throw new ValidationException("PBMS_MC_0001");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return contentUrl;
        }
    }

    @RequestMapping(value = "/getMinutesList", method = RequestMethod.POST)
    @ApiOperation(value = "待审核会议纪要列表", notes = "待审核会议纪要列表",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = PageInfo.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页数量", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "topic", value = "会议主题", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "starttime", value = "开始时间", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endtime", value = "结束时间", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgid", value = "党支部", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态", required = false, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "minutesStatus", value = "审核状态", required = false, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "会议类型", required = false, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "当前组织编码", required = false, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<Meetings> getMinutesList(Integer page, Integer limit, String topic, String starttime, String endtime, String orgid, Integer minutesStatus, Integer type, Integer status, String orgCode) {
        Users currentUser = getUser(); // 系统当前用户
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("topic", topic);
        paramMap.put("starttime", starttime);
        paramMap.put("endtime", endtime);
        paramMap.put("orgid", orgid);
        paramMap.put("status", status);
        paramMap.put("minutesStatus", minutesStatus);
        paramMap.put("type", type);
        paramMap.put("userid", currentUser.getId());
        paramMap.put("userRole", currentUser.getCurrentRoleId());
        paramMap.put("taskClass", WorkTaskClassEnum.REVIEW.getCode());

        // 数据权限规则
        paramMap.put("currentOrgCode", StringUtils.isBlank(orgCode) ? null : orgCode);

        return meetingsSV.getMinutesLis(page, limit, paramMap);
    }

    @RequestMapping(value = "/exportMinutes", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "导出会议纪要信息", notes = "导出会议纪要信息",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功"),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingid", value = "会议唯一标识", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public void exportMinutes(String meetingid, HttpServletResponse response) throws GeneralException, IOException {
        // 查询会议
        MeMinutesDetailDTO minutesDetail = meetingsSV.getMinutesDetail(meetingid);
        if (null == minutesDetail) {
            logger.error("参数信息有误：未查询到会议信息（ID=‘" + meetingid + "’");
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR);
        }

        genDocForMinutes(minutesDetail, response);
    }

    /**
     * 导出支部党员大会
     *
     * @param minutesDetail
     * @throws IOException
     */
    public void genDocForMinutes(MeMinutesDetailDTO minutesDetail, HttpServletResponse response) throws IOException {
        Date currTime = new Date();
        // Blank Document
        XWPFDocument document = new XWPFDocument();
        // Write the Document in file system

        // 生成标题及时间
        genTitle(document, minutesDetail);

        // 基本信息表格
        XWPFTable infoTable = document.createTable();
        infoTable.setRowBandSize(20);

        // 列宽自动分割
        CTTblWidth infoTableWidth = infoTable.getCTTbl().addNewTblPr().addNewTblW();
        infoTableWidth.setType(STTblWidth.DXA);
        infoTableWidth.setW(BigInteger.valueOf(9072));

        // 表格第一行
        XWPFTableRow infoTableRowOne = infoTable.getRow(0);
        long rowHeightOne = 0;
        Map<String, String> rowOneMap = new LinkedHashMap<>();
        if (MeetingTypeEnum.BRANCHMASSES.getCode().equals(minutesDetail.getType())
                || MeetingTypeEnum.BRANCHLEADER.getCode().equals(minutesDetail.getType())) {
            rowOneMap.put("主持人", "");
            rowOneMap.put("记录人", "");
            rowOneMap.put("地点", minutesDetail.getAddress());
            rowHeightOne = 360 * 2L;
        } else if (MeetingTypeEnum.PARTYGROUP.getCode().equals(minutesDetail.getType())) {
            rowOneMap.put("时间", ConvertUtil.convertDateToString(minutesDetail.getStarttime(), "yyyy年MM月dd日"));
            rowOneMap.put("地点", minutesDetail.getAddress());
            rowOneMap.put("主持人", "");
            rowHeightOne = 360 * 2L;
        } else if (MeetingTypeEnum.NEWSTUDY.getCode().equals(minutesDetail.getType())) {
            rowOneMap.put("主题及内容", minutesDetail.getTopic());
            rowOneMap.put("1", "");
            rowOneMap.put("2", "");
            rowHeightOne = 360 * 6L;
        }
        genRow(rowOneMap, infoTableRowOne, rowHeightOne);

        // 表格第二行
        XWPFTableRow infoTableRowTwo = infoTable.createRow();
        Map<String, String> rowTwoMap = new LinkedHashMap<>();
        long rowHeightTwo = 0;
        if (MeetingTypeEnum.BRANCHMASSES.getCode().equals(minutesDetail.getType())) {
            rowTwoMap.put("上级党委列席人员", "");
            rowTwoMap.put("应到人数", minutesDetail.getTotalNum() + "");
            rowTwoMap.put("实到人数", minutesDetail.getAttendNum() + "");
            rowHeightTwo = 360 * 2L;
        } else if (MeetingTypeEnum.BRANCHLEADER.getCode().equals(minutesDetail.getType())) {
            rowTwoMap.put("出席人", minutesDetail.getAttends());
            rowHeightTwo = 360 * 2L;
        } else if (MeetingTypeEnum.PARTYGROUP.getCode().equals(minutesDetail.getType())) {
            rowTwoMap.put("参加人", minutesDetail.getAddress());
            rowTwoMap.put("", "");
            rowTwoMap.put("缺席人", "");
            rowHeightTwo = 360 * 6L;
        } else if (MeetingTypeEnum.NEWSTUDY.getCode().equals(minutesDetail.getType())) {
            rowTwoMap.put("主讲人姓名及职务", minutesDetail.getTeacher());
            rowTwoMap.put("上级党委列席人员", "");
            rowTwoMap.put("记录人", "");
            rowHeightTwo = 360 * 2L;
        }
        genRow(rowTwoMap, infoTableRowTwo, rowHeightTwo);

        // 表格第三行
        XWPFTableRow infoTableRowThree = infoTable.createRow();
        Map<String, String> rowThreeMap = new LinkedHashMap<>();
        long rowHeightThree = 0;
        if (MeetingTypeEnum.BRANCHMASSES.getCode().equals(minutesDetail.getType())) {
            rowThreeMap.put("缺席人和缺席原因", StringUtils.isBlank(minutesDetail.getAbsents()) ? "空" : minutesDetail.getAbsents());
            rowHeightThree = 360 * 6L;
        } else if (MeetingTypeEnum.BRANCHLEADER.getCode().equals(minutesDetail.getType())) {
            rowThreeMap.put("列席人", minutesDetail.getAttends());
            rowThreeMap.put("缺席人和缺席原因", StringUtils.isBlank(minutesDetail.getAbsents()) ? "空" : minutesDetail.getAbsents());
            rowHeightThree = 360 * 4L;
        } else if (MeetingTypeEnum.PARTYGROUP.getCode().equals(minutesDetail.getType())) {
            rowThreeMap.put("主题", minutesDetail.getTopic());
            rowThreeMap.put("", "");
            rowThreeMap.put("记录人", "");
            rowHeightThree = 360 * 2L;
        } else if (MeetingTypeEnum.NEWSTUDY.getCode().equals(minutesDetail.getType())) {
            rowThreeMap.put("地点", minutesDetail.getAddress());
            rowThreeMap.put("应到人数", minutesDetail.getTotalNum() + "");
            rowThreeMap.put("实到人数", minutesDetail.getAttendNum() + "");
            rowHeightThree = 360 * 2L;
        }
        genRow(rowThreeMap, infoTableRowThree, rowHeightThree);

        // 表格第四行
        XWPFTableRow infoTableRowFour = infoTable.createRow();
        Map<String, String> rowFourMap = new LinkedHashMap<>();
        long rowHeightFour;
        if (MeetingTypeEnum.BRANCHMASSES.getCode().equals(minutesDetail.getType())) {
            rowFourMap.put("会议主题", minutesDetail.getTopic());
            rowHeightFour = 360 * 6L;
            genRow(rowFourMap, infoTableRowFour, rowHeightFour);
        } else if (MeetingTypeEnum.BRANCHLEADER.getCode().equals(minutesDetail.getType())) {
            rowFourMap.put("议题", minutesDetail.getTopic());
            rowHeightFour = 360 * 2L;
            genRow(rowFourMap, infoTableRowFour, rowHeightFour);
        } else if (MeetingTypeEnum.PARTYGROUP.getCode().equals(minutesDetail.getType())) {
            // 设置行高
            CTRow ctRow = infoTableRowFour.getCtRow();
            CTTrPr ctTrPr = ctRow.addNewTrPr();
            CTHeight ctHeight = ctTrPr.addNewTrHeight();
            ctHeight.setVal(BigInteger.valueOf(360 * 18L));
            insertMeContent(minutesDetail.getDescription(), infoTableRowFour);
        } else if (MeetingTypeEnum.NEWSTUDY.getCode().equals(minutesDetail.getType())) {
            rowFourMap.put("缺席人员及原因", StringUtils.isBlank(minutesDetail.getAbsents()) ? "空" : minutesDetail.getAbsents());
            rowHeightFour = 360 * 6L;
            genRow(rowFourMap, infoTableRowFour, rowHeightFour);
        }

        // 表格第五行
        if (!MeetingTypeEnum.PARTYGROUP.getCode().equals(minutesDetail.getType())) {
            XWPFTableRow infoTableRowFive = infoTable.createRow();
            // 设置行高
            CTRow ctRow = infoTableRowFive.getCtRow();
            CTTrPr ctTrPr1 = ctRow.addNewTrPr();
            CTHeight ctHeight1 = ctTrPr1.addNewTrHeight();
            ctHeight1.setVal(BigInteger.valueOf(360 * 18L));

            if (MeetingTypeEnum.BRANCHMASSES.getCode().equals(minutesDetail.getType())) {
                infoTableRowFive.getCell(0).setText("与会党员签名");
            } else if (MeetingTypeEnum.BRANCHLEADER.getCode().equals(minutesDetail.getType())) {
                insertMeContent(minutesDetail.getDescription(), infoTableRowFive);
            } else if (MeetingTypeEnum.NEWSTUDY.getCode().equals(minutesDetail.getType())) {
                infoTableRowFive.getCell(0).setText("到会党员签名");
            }
        }

        // 表格第六行
        XWPFTableRow infoTableRowSix = infoTable.createRow();
        // 设置行高
        CTRow ctRow6 = infoTableRowSix.getCtRow();
        CTTrPr ctTrPr6 = ctRow6.addNewTrPr();
        CTHeight ctHeight6 = ctTrPr6.addNewTrHeight();
        ctHeight6.setVal(BigInteger.valueOf(360 * 30L));
        infoTableRowSix.getCell(0).setText("会议内容：\r\n" + (null == minutesDetail.getContent() ? "" : minutesDetail.getContent()));

        // 设置列宽和单元格对齐方式
        int totalRow = 4;
        if (MeetingTypeEnum.PARTYGROUP.getCode().equals(minutesDetail.getType())) {
            totalRow = 3;
        }
        for (int j = 0; j < totalRow; j++) { // 行
            XWPFTableRow infoTableRow = infoTable.getRow(j);
            for (int i = 0; i < 6; i++) { // 列
                XWPFTableCell cell = infoTableRow.getCell(i);
                if (null != cell) {
                    // 垂直对齐
                    cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                    // 水平对齐
                    setHorizontal(cell);
                    CTTblWidth ctTblWidth1 = cell.getCTTc().addNewTcPr().addNewTcW();
                    ctTblWidth1.setType(STTblWidth.DXA);
                    ctTblWidth1.setW(BigInteger.valueOf(360 * 5L));
                }
            }
        }

        // 合并单元格
        if (MeetingTypeEnum.BRANCHMASSES.getCode().equals(minutesDetail.getType())) {
            mergeCellsHorizontal(infoTable, 2, 1, 5);
            mergeCellsHorizontal(infoTable, 3, 1, 5);
            mergeCellsHorizontal(infoTable, 4, 0, 5);
            mergeCellsHorizontal(infoTable, 5, 0, 5);
        } else if (MeetingTypeEnum.BRANCHLEADER.getCode().equals(minutesDetail.getType())) {
            mergeCellsHorizontal(infoTable, 1, 1, 5);
            mergeCellsHorizontal(infoTable, 2, 3, 5);
            mergeCellsHorizontal(infoTable, 3, 1, 5);
            mergeCellsHorizontal(infoTable, 4, 0, 5);
            mergeCellsHorizontal(infoTable, 5, 0, 5);
        } else if (MeetingTypeEnum.PARTYGROUP.getCode().equals(minutesDetail.getType())) {
            mergeCellsHorizontal(infoTable, 1, 1, 3);
            mergeCellsHorizontal(infoTable, 2, 1, 3);
            mergeCellsHorizontal(infoTable, 3, 0, 5);
            mergeCellsHorizontal(infoTable, 4, 0, 5);
        } else if (MeetingTypeEnum.NEWSTUDY.getCode().equals(minutesDetail.getType())) {
            mergeCellsHorizontal(infoTable, 0, 1, 5);
            mergeCellsHorizontal(infoTable, 3, 1, 5);
            mergeCellsHorizontal(infoTable, 4, 0, 5);
            mergeCellsHorizontal(infoTable, 5, 0, 5);
        }

        String fileName = ConvertUtil.convertDateToString(currTime, "yyyyMMddHHmmss");
        OutputStream out = response.getOutputStream();
        response.setContentType("application/ms-excel;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename="
                .concat(String.valueOf(URLEncoder.encode(fileName + ".docx", "UTF-8"))));
        document.write(out);
    }

    /**
     * 插入会议内容
     *
     * @param content
     * @param infoTableRowFour
     */
    private void insertMeContent(String content, XWPFTableRow infoTableRowFour) {
        infoTableRowFour.getCell(0).setText("会议内容：");
        XWPFParagraph paragraph = infoTableRowFour.getCell(0).addParagraph();
        paragraph.createRun();
        if (null != content) {
            paragraph.createRun().setText(content);
        }
    }

    /**
     * 生成一行记录
     *
     * @param contentMap
     * @param infoTableRow
     * @param rowHeight
     */
    private void genRow(Map<String, String> contentMap, XWPFTableRow infoTableRow, long rowHeight) {
        // 设置行高
        CTRow ctRow = infoTableRow.getCtRow();
        CTTrPr ctTrPr = ctRow.addNewTrPr();
        CTHeight ctHeight = ctTrPr.addNewTrHeight();
        ctHeight.setVal(BigInteger.valueOf(rowHeight));

        int i = 0;
        for (Map.Entry<String, String> temp : contentMap.entrySet()) {
            genCell(infoTableRow, i, temp.getKey());
            i++;
            genCell(infoTableRow, i, temp.getValue());
            i++;
        }
    }

    /**
     * 生成单元格
     *
     * @param infoTableRow
     * @param i
     * @param content
     */
    private void genCell(XWPFTableRow infoTableRow, int i, String content) {
        XWPFTableCell cell = infoTableRow.getCell(i);
        if (null == cell) {
            cell = infoTableRow.addNewTableCell();
        }
        cell.setText(content);
    }

    /**
     * 生成标题及时间
     *
     * @param document
     * @param minutesDetail
     */
    private void genTitle(XWPFDocument document, MeMinutesDetailDTO minutesDetail) {
        // 添加标题
        XWPFParagraph titleParagraph = document.createParagraph();
        // 设置段落居中
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        titleParagraph.setVerticalAlignment(TextAlignment.CENTER);
        XWPFRun titleParagraphRun = titleParagraph.createRun();
        String title = "";
        if (MeetingTypeEnum.BRANCHMASSES.getCode().equals(minutesDetail.getType())) {
            title = "支部党员大会记录";
        } else if (MeetingTypeEnum.BRANCHLEADER.getCode().equals(minutesDetail.getType())) {
            title = "党支部委员会会议记录";
        } else if (MeetingTypeEnum.PARTYGROUP.getCode().equals(minutesDetail.getType())) {
            title = minutesDetail.getTopic() + "党小组会会议记录";
        } else if (MeetingTypeEnum.NEWSTUDY.getCode().equals(minutesDetail.getType())) {
            title = "党课教育活动记录";
        }
        titleParagraphRun.setText(title);
        titleParagraphRun.setColor("000000");
        titleParagraphRun.setFontSize(20);
        titleParagraphRun.setBold(true); // 加粗

        // 换行
        XWPFParagraph paragraph1 = document.createParagraph();
        XWPFRun paragraphRun1 = paragraph1.createRun();
        paragraphRun1.setText("\r");

        // 支部大会|委员会|党课，在标题下打印日期
        if (!MeetingTypeEnum.PARTYGROUP.getCode().equals(minutesDetail.getType())) {
            // 添加日期
            XWPFParagraph paragraphDate = document.createParagraph();
            paragraphDate.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun paragraphRunDate = paragraphDate.createRun();
            paragraphRunDate.setText(ConvertUtil.convertDateToString(minutesDetail.getStarttime(), "yyyy年MM月dd日"));
        }

        // 换行
        XWPFParagraph paragraph2 = document.createParagraph();
        XWPFRun paragraphRun2 = paragraph2.createRun();
        paragraphRun2.setText("\r");
    }

    @RequestMapping(value = "/uploadLiveVideo", method = RequestMethod.POST)
    @ApiOperation(value = "记录会议直播视频", notes = "记录会议直播视频",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingId", value = "会议ID", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "fileList", value = "直播视频文件列表[{'fileName':'666','url':'xx'}]", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int uploadLiveVideo(String meetingId, String fileList) throws GeneralException {
        // 参数校验
        Meetings meetings = meetingsSV.getByPrimaryKey(meetingId);
        if (null == meetings) {
            logger.error("参数信息有误：未查询到会议信息（ID=‘" + meetingId + "’");
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR);
        }
        if (StringUtils.isBlank(fileList)) {
            logger.error("参数信息有误：无效的视频文件列表（urlList=‘" + fileList + "’");
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR);
        }

        Users user = getUser();
        Date sysDate = new Date();

        List<Attachments> attachmentsList = attachmentsSV.getListByObjIdType(meetingId, AttachTypeEnum.ME_VEDIO.getType());
        Set<String> existVideoMap = new HashSet<>(16);
        for (Attachments temp : attachmentsList) {
            existVideoMap.add(temp.getUrl());
        }

        List<VMeetingLiveVedioObj> vedioList;
        try {
            vedioList = JSONArray.parseArray(fileList).toJavaList(VMeetingLiveVedioObj.class);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException(JSON_FORMAT_ERROR);
        }

        int result = 0;
        if (CollectionUtils.isNotEmpty(vedioList)) {
            for (VMeetingLiveVedioObj temp : vedioList) {
                // 去重
                if (!existVideoMap.contains(temp)) {
                    Attachments attachments = new Attachments();
                    attachments.setId(UIDUtil.getUID());
                    attachments.setAttname(temp.getFileName());
                    attachments.setObjid(meetingId);
                    attachments.setAtttype(AttachTypeEnum.ME_VEDIO.getType());
                    attachments.setUrl(temp.getUrl());
                    attachments.setCreatedby(user.getId());
                    attachments.setCreateddate(sysDate);
                    attachments.setFilesize(null);
                    attachments.setIsdeleted(DataIsDeleteEnum.NORMAL.getCode());
                    result += attachmentsSV.insertSelective(attachments);
                }
            }
        }

        return result;
    }

    @RequestMapping(value = "/recordLive", method = RequestMethod.POST)
    @ApiOperation(value = "记录直播ID", notes = "记录直播ID",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = PageInfo.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingId", value = "会议ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "liveId", value = "直播ID", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int recordLive(String meetingId, String liveId) throws GeneralException {
        // 查询会议
        Meetings meeting = meetingsSV.getByPrimaryKey(meetingId);
        if (null == meeting) {
            logger.error("参数信息有误：未查询到会议信息（ID=‘" + meetingId + "’");
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR);
        }

        if (StringUtils.isBlank(meeting.getLiveId())) {
            meeting.setLiveId(liveId);
        } else {
            String oldLiveId = meeting.getLiveId();
            if (Arrays.asList(oldLiveId.split(",")).contains(liveId)) {
                logger.info("直播ID已经和党课关联");
                return 1;
            } else {
                meeting.setLiveId(oldLiveId.concat("," + liveId));
            }
        }
        meeting.setModifiedby("system");
        meeting.setModifieddate(new Date());
        return meetingsSV.updateMeetingsByPrimaryKey(meeting);
    }

    @RequestMapping(value = "/recordReplayTimes", method = RequestMethod.POST)
    @ApiOperation(value = "记录视频回放次数", notes = "记录视频回放次数",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = PageInfo.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingId", value = "会议ID", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int recordReplayTimes(String meetingId) throws GeneralException {
        // 查询会议
        Meetings meeting = meetingsSV.getByPrimaryKey(meetingId);
        if (null == meeting) {
            logger.error("参数信息有误：未查询到会议信息（ID=‘" + meetingId + "’");
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR);
        }

        // 取当前登录用户
        Users user = getUser();

        // 设置更新用户和时间
        meeting.setModifieddate(new Date());
        meeting.setModifiedby(user.getId());

        return meetingsSV.addReplayTimes(meeting);
    }

    @RequestMapping(value = "/switchMeetingRecord", method = RequestMethod.POST)
    @ApiOperation(value = "修改是否需要提交会议记录", notes = "修改是否需要提交会议记录",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = PageInfo.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingId", value = "会议ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "needRecordFlag", value = "是否需要参会人员提交会议记录，false:否，true:是", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "notifyRecordFlag", value = "是否提醒提交会议记录，false:否，true:是", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "submitNoticeStart", value = "记录提交提醒开始时间（推后于结束时间的时长）", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "submitNoticeInterval", value = "记录提交提醒间隔", required = true, dataType = "int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public int switchMeetingRecord(String meetingId,
                                   boolean needRecordFlag,
                                   boolean notifyRecordFlag,
                                   Integer submitNoticeStart,
                                   Integer submitNoticeInterval) throws GeneralException {
        // 查询会议
        Meetings meeting = meetingsSV.getByPrimaryKey(meetingId);
        if (null == meeting) {
            logger.error("参数信息有误：未查询到会议信息（ID=‘" + meetingId + "’");
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR);
        }

        // 取当前登录用户
        Users user = getUser();
        String userId = user.getId();
        Date sysDate = new Date();

        // 设置更新用户和时间
        meeting.setModifieddate(new Date());
        meeting.setModifiedby(user.getId());
        meeting.setNeedRecord(needRecordFlag ? 1 : 0);

        // 更新短信提醒设置
        MeetingMsgSetting msgSetting = meetingMsgSettingSV.getByObjTypeAndObjId(NOTICE_MSG_OBJ_TYPE, meetingId);
        if (null == msgSetting) {
            // 创建默认设置
            //短信提醒默认设置，默认查询线下会议设置
            String msgDefaultItem = "MEETING_MSG_DEFAULT_RULE_UNLINE";
            if (meeting.getType() == MeetingTypeEnum.STUDY.getCode()) {
                msgDefaultItem = "MEETING_MSG_DEFAULT_RULE_STUDY";
            } else {
                //判断会议的展开形式是否为线上直播
                if (MEETING_TYPE_ONLINE.equals(meeting.getChannelType())) {
                    //修改短信提醒设置为线上直播的设置
                    msgDefaultItem = "MEETING_MSG_DEFAULT_RULE_ONLINE";
                }
            }

            meetingMsgSettingSV.addDefauleSetting(meetingId, msgDefaultItem, userId, sysDate, MEETING_TIMELINESS_SUPPLY.equals(meeting.getTimelinessType()), needRecordFlag ? 1 : 0);
        } else {
            msgSetting.setIsRecordSubmit(notifyRecordFlag ? 1 : 0);
            msgSetting.setSubmitNoticeStart(submitNoticeStart);
            msgSetting.setSubmitNoticeInterval(submitNoticeInterval);
            msgSetting.setModifiedBy(userId);
            msgSetting.setModifiedDate(sysDate);
            meetingMsgSettingSV.updateByPrimaryKeySelective(msgSetting);
        }

        // 更新与会者接收短信的设置
//        //判断是否需要查询所有与会者
//        Boolean searchAll = MEETING_TYPE_ONLINE.equals(meeting.getChannelType()) && PROCESS_TYPE_MAIN.equals(meeting.getDistributionType());
//        List<Conventioneer> conventioneerList = conventioneerSV.getConventioneerByMeetingsId(meetingId, searchAll);
//        for (Conventioneer temp : conventioneerList) {
//            temp.setIsReceiveRecordMsg(notifyRecordFlag ? 0 : 1);
//            temp.setModifiedby(userId);
//            temp.setModifieddate(sysDate);
//            conventioneerSV.updateConventioneerById(temp);
//        }
        Boolean searchAll = false;
        if (MEETING_TYPE_ONLINE.equals(meeting.getChannelType()) && PROCESS_TYPE_MAIN.equals(meeting.getDistributionType())) {
            searchAll = true;
        }

        Conventioneer conventioneer = new Conventioneer();
        conventioneer.setModifieddate(sysDate);
        conventioneer.setModifiedby(userId);
        conventioneer.setIsReceiveRecordMsg(notifyRecordFlag ? 0 : 1);
        conventioneer.setMeetingid(meetingId);
        conventioneerSV.updateIsReceiveRecordMsg(conventioneer, searchAll);

        return meetingsSV.updateMeetingsByPrimaryKey(meeting);
    }

    @RequestMapping(value = "/changeMeetingToPlan")
    @ApiOperation(value = "删除计划内会议", notes = "逻辑删除所有与会议相关的信息,并将计划外会议转为计划内会议",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = PageInfo.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "operType", value = "01:计划内会议和计划外会议交换 02删除计划内会议 03将计划外会议处理成计划内会议", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "meetingIdInOfPlan", value = "计划内会议id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "meetingIdOutOfPlan", value = "计划外会议id", required = true, dataType = "String", paramType = "query")})
    public void changeMeetingToPlan(String operType, String meetingIdInOfPlan, String meetingIdOutOfPlan) throws GeneralException {

        Meetings meetingsInPlan = meetingsSV.getByPrimaryKey(meetingIdInOfPlan);
        Meetings meetingsOutOfPlan = meetingsSV.getByPrimaryKey(meetingIdOutOfPlan);

        if ("01".equals(operType)) {
            if (null == meetingsInPlan)
                throw new GeneralException("PBMS_ME_1048");

            if (null == meetingsOutOfPlan)
                throw new GeneralException("PBMS_ME_1049");

            if (!meetingsInPlan.getOrgid().equals(meetingsOutOfPlan.getOrgid()))
                throw new GeneralException("PBMS_ME_1050");

            // 删除计划内会议（计划内会议改成不可见）
            meetingsSV.deleteCustomMeeting(meetingIdInOfPlan);

            //将计划外会议处理成计划内会议
            meetingsSV.exchangeMeeting(meetingsInPlan.getId(), meetingsOutOfPlan);
        }

        if ("02".equals(operType)) {
            if (null == meetingsInPlan)
                throw new GeneralException("PBMS_ME_1048");

            // 删除计划内会议（计划内会议改成不可见）
            meetingsSV.deleteCustomMeeting(meetingIdInOfPlan);

        }
        if ("03".equals(operType)) {
            if (null == meetingsOutOfPlan)
                throw new GeneralException("PBMS_ME_1049");

            //将计划外会议处理成计划内会议
            meetingsSV.exchangeMeeting(meetingIdInOfPlan, meetingsOutOfPlan);

        }
    }

    @RequestMapping(value = "/recoveryMeeting", method = RequestMethod.POST)
    @ApiOperation(value = "恢复已删除会议", notes = "恢复已删除会议",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingId", value = "会议ID", required = true, dataType = "string", paramType = "query")})
    public int recoveryMeeting(String meetingId) throws GeneralException {
        // 取当前登录用户
        Users user = getUser();
        return meetingsSV.recoveryMeetingForDelete(meetingId, user.getId(), new Date());
    }

    @RequestMapping(value = "/getMeetingsDetailByConIdForApp", method = RequestMethod.POST)
    @ApiOperation(value = "根据与会者id获取会议详情", notes = "根据与会者id获取会议详情",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = MeetingsDetailDTO.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = MeetingsDetailDTO.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "conventioneerId", value = "与会者唯一标识", required = true, dataType = "string", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public MeetingsDetailDTO getMeetingsDetailByConIdForApp(String conventioneerId) {
        Users user = getUser();
        // 查询与会者信息
        Conventioneer conventioneer = conventioneerSV.getConventioneerById(conventioneerId);
        // 查询会议信息
        MeetingsDetailDTO meetingsDetailDTO = meetingsSV.getFullDetailById(conventioneer.getMeetingid(), user);

        if (null != meetingsDetailDTO) {
            meetingsDetailDTO.setVcAddress(VC_ADDRESS);
            meetingsDetailDTO.setVcAccount(VC_ACCOUNT);
            meetingsDetailDTO.setVcPlayId(VC_PLAY_ID);
            meetingsDetailDTO.setVcTokenKey(VC_TOKEN_KEY);
            meetingsDetailDTO.setVodBaseUrl(vodBaseUrl);
            meetingsDetailDTO.setVodcid(vodcid);
            meetingsDetailDTO.setVodtokenkey(vodtokenkey);

            List<Attachments> qrList = attachmentsSV.getListByObjIdType(conventioneer.getMeetingid(), PbmsConstants.QR_BUS_ME_SIGN);
            if (!qrList.isEmpty()) {
                String qrurl = qrList.get(0).getUrl();
                qrurl = OnestUtil.subStringUrl(qrurl);
                meetingsDetailDTO.setQrUrl(qrurl);
            }

            if (meetingsDetailDTO.getType() == 20) {
                meetingsDetailDTO.setAttendancesList(attendancesSV.getByMeetingId(meetingsDetailDTO.getId()));
            }
        }
        return meetingsDetailDTO;
    }

    @RequestMapping(value = "/endMeeting", method = RequestMethod.POST)
    @ApiOperation(value = "手动结束会议", notes = "手动结束会议")
    public int endMeeting(String meetingsId) throws GeneralException {
        Users user = getUser();
        if (StringUtils.isBlank(meetingsId)) {
            logger.error("请求参数有误");
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR);
        }
        return meetingsSV.endMeetingsByPrimaryKey(meetingsId, user.getId(), new Date());
    }

    @RequestMapping(value = "/getNotRelatedMeetings", method = RequestMethod.POST)
    @ApiOperation(value = "获取当前用户未关联会议宝信息的会议", notes = "获取当前用户未关联会议宝信息的会议",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = MeetingsDetailDTO.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = MeetingsDetailDTO.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "type", value = "会议类型（10：支部党员大会，20：支部委员会，30：党小组会，40：党课，50：集中学习）", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "topic", value = "标题", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "起始日期", required = false, dataType = "dataTime", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = false, dataType = "dataTime", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public List<MeetingsRelatedDTO> getRelatedMeetings(Integer type, String topic, Date startDate, Date endDate) throws GeneralException {
        Users user = getUser();

        if (null != endDate) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endDate);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            endDate = calendar.getTime();
        }

        return meetingsSV.queryNotRelatedMeetings(type, topic, startDate, endDate, user.getCurrentRoleOrg().getCodestr());
    }

    @RequestMapping(value = "/getAllRelatedMeetings", method = RequestMethod.POST)
    @ApiOperation(value = "获取当前用户可选待关联会议和会议关联次数", notes = "获取当前用户可选待关联会议和会议关联次数",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = MeetingsDetailDTO.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = MeetingsDetailDTO.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "type", value = "会议类型（10：支部党员大会，20：支部委员会，30：党小组会，40：党课，50：集中学习）", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "topic", value = "标题", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "起始日期", required = false, dataType = "dataTime", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = false, dataType = "dataTime", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public List<MeetingsRelatedDTO> getAllRelatedMeetings(Integer type, String topic, Date startDate, Date endDate) throws GeneralException {
        Users user = getUser();

        if (null != endDate) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endDate);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            endDate = calendar.getTime();
        }

        return meetingsSV.queryAllRelatedMeetings(type, topic, startDate, endDate, user.getCurrentRoleOrg().getCodestr());
    }

    @RequestMapping(value = "/getNotRelatedMeetingsForVd", method = RequestMethod.POST)
    @ApiOperation(value = "获取当前用户未关联视频会议的党建会议", notes = "获取当前用户未关联视频会议的党建会议",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = MeetingsDetailDTO.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = MeetingsDetailDTO.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "type", value = "会议类型（10：支部党员大会，20：支部委员会，30：党小组会，40：党课，50：集中学习）", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "topic", value = "标题", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "起始日期", required = false, dataType = "dataTime", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = false, dataType = "dataTime", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public List<MeetingsRelatedDTO> getNotRelatedMeetingsForVd(Integer type, String topic, Date startDate, Date endDate) {
        Users user = getUser();

        // 时间为空，默认取当前月份
        if (null != startDate || null != endDate) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, 0);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            startDate = calendar.getTime();

            Calendar ca = Calendar.getInstance();
            ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
            endDate = ca.getTime();
        }

        return meetingsSV.queryNotRelatedMeetingsForVd(type, topic, startDate, endDate, user.getId());
    }

    @RequestMapping(value = "/cancelBindVdMeeting", method = RequestMethod.POST)
    @ApiOperation(value = "取消绑定视频会议", notes = "取消绑定视频会议",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = Integer.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingId", value = "党建会议ID", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Integer cancelBindMeeting(String meetingId) throws GeneralException {

        ValidateUtil.isNotNull(meetingId);

        Users user = getUser();

        Meetings meeting = meetingsSV.getByPrimaryKey(meetingId);
        if (null == meeting) {
            logger.error("未查询到指定的会议");
            throw new GeneralException("PBMS_ME_1062");
        }
        if (MeetingStatusEnum.FINISHED.getCode().equals(meeting.getStatus())) {
            logger.error("已完成的会议，不能修改绑定关系");
            throw new GeneralException("PBMS_ME_1063");
        }

        return vdMeetingsSV.cancelbindMeeting(meetingId, user.getId(), new Date());
    }

    @RequestMapping(value = "/downSignExcel", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "根据会议类型生成签到表", notes = "根据会议类型生成签到表",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功"),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "meetingType", value = "会议类型", required = true, dataType = "int", paramType = "query")})
    public void downSignExcel(HttpServletResponse response, Integer meetingType) throws GeneralException {
        // 查询组织信息
        String orgId = getUser().getCurrentUserRole().getOrgid();
        Organization organization = organizationSV.getByPrimaryKey(orgId);
        if (null == organization) {
            logger.error("请求参数错误：没有查询到党支部/党小组信息");
            throw new GeneralException(NO_ORG_INFO);
        }
        Meetings meeting = new Meetings();
        meeting.setType(meetingType);
        meeting.setOrgcode(organization.getCodestr());
        meeting.setOrgid(organization.getId());

        try {
            XSSFWorkbook wb = new XSSFWorkbook();

            meetingExportService.exportSignExcel(wb, meeting);

            String fileName = "签到表模板";
            OutputStream out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=".concat(String.valueOf(URLEncoder.encode(fileName + ".xlsx", "UTF-8"))));
            wb.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("PBMS_UC_0003", "文件读取异常");
        }
    }

    @RequestMapping(value = "/createMinutesOfMeetings", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "根据会议Id生成会议纪要", notes = "根据会议Id生成会议纪要",
            consumes = "application/x-www-form-urlencoded", produces = "application/json", httpMethod = "POST", response = Attachments.class)
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作成功", response = Attachments.class),
            @ApiResponse(code = 999, message = "操作失败", response = GeneralException.class)
    })
    @ApiImplicitParams({@ApiImplicitParam(name = "meetingId", value = "会议Id", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Attachments createMinutesOfMeetings(String meetingId) throws GeneralException {
        if (StringUtils.isBlank(meetingId)) {
            logger.error("请求参数有误");
            throw new GeneralException(MSG_REQUEST_PARAMS_ERROR);
        }

        Map<String, Object> ftlParams = meetingsSV.createMinutesByMeetingId(meetingId);

        List<MeVoiceTranslationInfoListDTO> meVoiceTranslationInfo = meVoiceTranslationInfoSV.selectByObjId(meetingId);

        String[] meVodioStrs = null;
        List<String> strings = new ArrayList<>();
        if (null != meVoiceTranslationInfo && !meVoiceTranslationInfo.isEmpty()) {
            MeVoiceTranslationInfoListDTO meVoiceTranslationInfoListDTO = meVoiceTranslationInfo.get(0);
            CloseableHttpClient client = HttpClients.createDefault();
            JSONObject requestBody = new JSONObject();
            requestBody.put("audioId", meVoiceTranslationInfoListDTO.getAudioId());
            JSONObject jsonObject = HttpUtil.callPost(client, animeetHost, animeetGetTextJson, this.convert(meVoiceTranslationInfoListDTO.getUserId()), requestBody.toJSONString());
            if (null != jsonObject && "0".equals(jsonObject.getString("returnCode"))) {
                jsonObject = jsonObject.getJSONObject("object");
                meVodioStrs = jsonObject.getObject("result", String[].class);

                StringBuilder stringBuilder = new StringBuilder();
                if (null != meVodioStrs) {
                    for (String string : meVodioStrs) {
                        stringBuilder.append(string);
                        if (string.endsWith("\n")) {
                            strings.add(stringBuilder.toString());
                            stringBuilder = new StringBuilder();
                        }
                    }
                    strings.add(stringBuilder.toString());
                }
            }
        }

        ftlParams.put("meContent", strings);

        Configuration configuration = new Configuration();
        configuration.setDefaultEncoding("UTF-8");

        String ftlName = "";

        switch (Integer.valueOf(ftlParams.get("meetingType").toString())) {
            case 10:
            case 20:
                ftlName = "partyMinutes.ftl";
                break;
            case 30:
                ftlName = "groupMinutes.ftl";
                break;
        }

        try {
            String ftlPath = FileUtil.checkFilePath("minutesFtl/", ftlName);
            ftlPath = ftlPath.replace(ftlName, "");
            logger.info(ftlPath);
            configuration.setDirectoryForTemplateLoading(new File(ftlPath));
//            String ftlPath = "W:/javaSpace/pbms/pbms-web/src/main/resources/minutesFtl/".concat(ftlName);
//            configuration.setDirectoryForTemplateLoading(new File(ftlPath.substring(0, ftlPath.lastIndexOf(ftlName))));
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("PBMS_FTL_0001", "会议纪要模板加载异常");
        }

        StringWriter writer = new StringWriter();
        try {
            Template template = configuration.getTemplate(ftlName, "UTF-8");
            template.process(ftlParams, writer);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(writer.toString().getBytes("UTF-8"));

            String docxName = String.valueOf(System.currentTimeMillis()).concat(".doc");
            String onestUrl = OnestUtil.storeByStream(inputStream, docxName, AttachTypeEnum.ME_MINUTES_SYS.getType(), null, null);

            List<Attachments> attList = attachmentsSV.selectListByObjIdType(meetingId, AttachTypeEnum.ME_MINUTES_SYS.getType());
            Attachments attachments = attList.size() > 0 ? attList.get(0) : new Attachments();

            attachments.setUrl(OnestUtil.subStringUrl(onestUrl));
            attachments.setObjid(meetingId);
            attachments.setAttname(docxName);
            attachments.setAtttype(AttachTypeEnum.ME_MINUTES_SYS.getType());
            attachments.setIsdeleted(0);

            if (attList.isEmpty()) {
                attachments.setId(UIDUtil.getUID());
                attachments.setCreatedby(getUser().getId());
                attachments.setCreateddate(new Date());
                attachmentsSV.insertSelective(attachments);
            } else {
                attachments.setExtFld1(null);
                attachments.setExtFld3(null);
                attachments.setModifiedby(getUser().getId());
                attachments.setModifieddate(new Date());
                attachmentsSV.updateByPrimaryKey(attachments);
            }

            return attachments;

        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("PBMS_FTL_0002", "会议纪要模板读取异常");
        } catch (TemplateException e) {
            logger.error(e.getMessage(), e);
            throw new GeneralException("PBMS_FTL_0003", "会议纪要文件输出异常");
        } finally {
            try {
                writer.close();
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

    public String convert(String userId) {
        String loginToken = "";
        logger.info("前端上送userId=" + userId);
        if (StringUtils.isNotBlank(userId) && !"null".equals(userId)) {
            try {
                ICacheService cacheService = CacheServiceUtil.getService();
                loginToken = cacheService.getString(PbmsConstants.MeVoiceTranslationInfo_loginToken_key + userId);
                logger.info("缓存loginToken=" + loginToken);
                if (StringUtils.isBlank(loginToken)) {
                    Users users = userSV.getByPrimaryKey(userId);
                    //去会议宝登录获取token
                    CloseableHttpClient client = HttpClients.createDefault();
                    JSONObject requestBody = new JSONObject();
                    requestBody.put("phone", users.getTelephones());
                    requestBody.put("enterpriseKey", animeetKey);

                    JSONObject jsonObject = HttpUtil.callPost(client, animeetHost, animeetLoginUrl, null, requestBody.toJSONString());
                    if (null != jsonObject && "0".equals(jsonObject.getString("returnCode"))) {
                        loginToken = (jsonObject.getJSONObject("object")).getString("loginToken");
                        logger.info("重新获取的loginToken=" + loginToken);
                        cacheService.setex(PbmsConstants.MeVoiceTranslationInfo_loginToken_key + userId, loginToken, 60 * 60 * 20);
                    }
                }
            } catch (GeneralException e) {
                logger.error(e.getMessage(), e);
            }
        }

        return loginToken;
    }

    //关联任务保存
    private void relatedTasksSaveAndUpdate(List<String> amtIds, String meetingId){
        meetingsSV.relatedTasksSaveAndUpdate(amtIds,meetingId);
    }

    //临时方法用于通过审核
    @RequestMapping(value = "/passToExamine", method = {RequestMethod.POST})
    public int passToExamine(String meetingId) throws GeneralException {

        return meetingsSV.passToExamine(meetingId, getUser());
    }

    //归档接口
    @RequestMapping(value = "/complete", method = {RequestMethod.POST})
    public int complete(String meetingId) throws GeneralException {

        return meetingsSV.complete(meetingId, getUser());
    }
    //获取活动期数
    @RequestMapping(value = "/getActivityPeriod", method = {RequestMethod.GET})
    public Integer getActivityPeriod(String branchOrgId, String meetingType,String year,String studyType) throws GeneralException {

        return meetingsSV.getActivityPeriod(branchOrgId,meetingType,year,studyType);
    }

    //生成会议议程pdf文件
    private void genteatePdf(String meetingsId) throws GeneralException, DocumentException, IOException {
        log.info("生成会议议程文件：{}",meetingsId);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy 年 MM 月 dd 日 HH:mm");
        MeetingsDetailDTO meetingsDetailDTO = meetingsSV.getFullDetailById(meetingsId,getUser());
        Map<String, Object> fillData = new HashMap<>();
        fillData.put("year", meetingsDetailDTO.getActivityYear());
        fillData.put("meetingNumber", meetingsDetailDTO.getActivityCount());
        fillData.put("meetingType",meetingsDetailDTO.getTypeValue());
        fillData.put("meetingName", meetingsDetailDTO.getTopic());
        fillData.put("meetingTime", meetingsDetailDTO.getStarttime());
        fillData.put("meetingLocation", meetingsDetailDTO.getAddress());
        //转换主持人和记录人
        fillData.put("host", meetingsDetailDTO.getMeHost());
        fillData.put("recorder", meetingsDetailDTO.getRecorderName());
        //absentReason 缺席党员姓名和原因：
        //获取缺席党员
        VMeetingConventioneerSearchBean vMeetingConventioneerSearchBean = new VMeetingConventioneerSearchBean();
        vMeetingConventioneerSearchBean.setMeetingid(meetingsId);
        vMeetingConventioneerSearchBean.setIsjoin(2);
        vMeetingConventioneerSearchBean.setPage(1);
        vMeetingConventioneerSearchBean.setLimit(9999);
        vMeetingConventioneerSearchBean.setSearchAll(false);
        PageInfo<MeetingConventioneerDTO> conventioneerListByParams = conventioneerSV.getConventioneerListByParams(vMeetingConventioneerSearchBean);
        List<MeetingConventioneerDTO> list = conventioneerListByParams.getList();
        Optional.ofNullable(list)
                .map(l -> l.stream()
                        .map(item -> item.getUsername() + "：" + item.getReason())
                        .collect(Collectors.joining("；")))
                .ifPresent(p -> fillData.put("absentReason", p));
        fillData.put("expectedAttendees", meetingsDetailDTO.getExpectedNum());
        //出席人员和职务
        PageInfo<Attendances> byMeetingId = attendancesSV.getByMeetingId(1, 9999, meetingsId, "no", "all", null, 0);
        List<Attendances> byMeetingIdList = byMeetingId.getList();
        Optional.ofNullable(byMeetingIdList)
                .map(l->
                        l.stream()
                                .map(item->item.getPersonName()+"("+item.getPersonPosition()+")")
                                .collect(Collectors.joining("、")))
                .ifPresent(p-> fillData.put("attendeesAndTitles", p));
        fillData.put("actualAttendees", meetingsDetailDTO.getAttendanceNum());

        fillData.put("content", meetingsDetailDTO.getMeContent());
        InputStream meRecord20241120 = createSignPDF("me_meeting_20250212", fillData);
        String aid = UIDUtil.getUID();
        //调用onest工具上传到附件服务器
        String url = OnestUtil.storeByStream(meRecord20241120, aid.concat(".pdf"),AttachTypeEnum.ME_PLAN_OA.getType(),null, null);
        // 插入到附件表
        Assert.isTrue(StringUtils.isNotBlank(url),"生成文件失败，请联系管理员。");
        String nurl = OnestUtil.subStringUrl(url);
        Attachments attachments = new Attachments();
        attachments.setId(aid);
        attachments.setObjid(meetingsId);
        attachments.setAttname("会议议程.pdf");
        attachments.setUrl(nurl);
        attachments.setIsdeleted(0);
        attachments.setFilesize(meRecord20241120.available());
        attachments.setAtttype(AttachTypeEnum.ME_PLAN_OA.getType());
        attachments.setCreateddate(new Date());
        attachments.setCreatedby("system");
        attachmentsSV.insertSelective(attachments);
    }

    //生成会议议程Word文件
    private void genteateWord(String meetingsId) throws Exception {
        log.info("生成会议议程Word文件：{}", meetingsId);
        MeetingsDetailDTO meetingsDetailDTO = meetingsSV.getFullDetailById(meetingsId, getUser());

        // 获取数据
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("meetingName", meetingsDetailDTO.getTopic());
        dataMap.put("meetingTime", meetingsDetailDTO.getStarttime());
        dataMap.put("location", meetingsDetailDTO.getAddress());
        dataMap.put("host", meetingsDetailDTO.getMeHost());
        dataMap.put("eventTurnout", meetingsDetailDTO.getExpectedNum());

        // 获取参加人员（出席人员，personType = 0）
        PageInfo<Attendances> participantsPage = attendancesSV.getByMeetingId(1, 9999, meetingsId, "no", "all", null, 0);
        List<Attendances> participantsList = participantsPage.getList();
        Optional.ofNullable(participantsList)
                .map(l -> l.stream()
                        .filter(item -> item.getPersonType() != null && item.getPersonType() == 0) // 过滤出席人员
                        .map(item -> item.getPersonName() + "（" + (item.getPersonPosition() != null ? item.getPersonPosition() : "") + "）")
                        .collect(Collectors.joining("、")))
                .ifPresent(p -> dataMap.put("participants", p));

        // 获取列席人员（personType = 1）
        Optional.ofNullable(participantsList)
                .map(l -> l.stream()
                        .filter(item -> item.getPersonType() != null && item.getPersonType() == 1) // 过滤列席人员
                        .map(item -> item.getPersonName() + "（" + (item.getPersonPosition() != null ? item.getPersonPosition() : "") + "）")
                        .collect(Collectors.joining("、")))
                .ifPresent(p -> dataMap.put("observers", p));

        // 获取缺席党员
//        VMeetingConventioneerSearchBean vMeetingConventioneerSearchBean = new VMeetingConventioneerSearchBean();
//        vMeetingConventioneerSearchBean.setMeetingid(meetingsId);
//        vMeetingConventioneerSearchBean.setIsjoin(2);
//        vMeetingConventioneerSearchBean.setPage(1);
//        vMeetingConventioneerSearchBean.setLimit(9999);
//        vMeetingConventioneerSearchBean.setSearchAll(false);
//        PageInfo<MeetingConventioneerDTO> conventioneerListByParams = conventioneerSV.getConventioneerListByParams(vMeetingConventioneerSearchBean);
//        List<MeetingConventioneerDTO> list = conventioneerListByParams.getList();
//        Optional.ofNullable(list)
//                .map(l -> l.stream()
//                        .map(item -> item.getUsername() + "：" + item.getReason())
//                        .collect(Collectors.joining("；")))
//                .ifPresent(p -> dataMap.put("reasonsForAbsence", p));

        dataMap.put("content", meetingsDetailDTO.getMeContent());

        // 获取模板
        String templatesName = "MeetingTailoredPlanTemplate.ftl";
        InputStream templatesIs = this.getClass().getResourceAsStream("/templates/" + templatesName);

        // 处理模板并生成文档内容
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        FreemarkerUtils.processTemplateByStream("templates-" + templatesName, templatesIs, outputStream, dataMap);

        // 上传到ONest
        String aid = UIDUtil.getUID();
        String fileName = aid + ".doc";
        ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        String url = OnestUtil.storeByStream(inputStream, fileName, AttachTypeEnum.ME_PLAN_OA.getType(), null, null);

        // 插入到附件表
        Assert.isTrue(StringUtils.isNotBlank(url), "生成文件失败，请联系管理员。");
        String nurl = OnestUtil.subStringUrl(url);
        Attachments attachments = new Attachments();
        attachments.setId(aid);
        attachments.setObjid(meetingsId);
        attachments.setAttname("会议议程.doc");
        attachments.setUrl(nurl);
        attachments.setIsdeleted(0);
        attachments.setFilesize(outputStream.size());
        attachments.setAtttype(AttachTypeEnum.ME_PLAN_OA.getType());
        attachments.setCreateddate(new Date());
        attachments.setCreatedby("system");
        attachmentsSV.insertSelective(attachments);
    }
}

