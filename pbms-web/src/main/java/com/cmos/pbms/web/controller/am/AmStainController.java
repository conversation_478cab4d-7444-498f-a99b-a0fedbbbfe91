package com.cmos.pbms.web.controller.am;

import com.alibaba.dubbo.config.annotation.Reference;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.utils.ResultFormatter;
import com.cmos.pbms.beans.am.AmStain;
import com.cmos.pbms.beans.dto.AmStainListDTO;
import com.cmos.pbms.iservice.am.IAmStainSV;
import com.cmos.pbms.utils.ValidateUtil;
import com.cmos.pbms.web.common.BaseController;
import com.cmos.pbms.web.config.DateValueFormatter;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@RequestMapping(value = "/amStain")
@Api(description = "负面清单控制器")
public class AmStainController extends BaseController {

    @Reference(group = "pbms")
    private IAmStainSV amStainSV;

    @RequestMapping(value = "/getAmStainListByAmId", method = RequestMethod.POST)
    @ApiOperation(value = "根据考核任务id获取负面清单列表", notes = "根据考核任务id获取负面清单列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "amId", value = "考核任务id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "content", value = "负面清单内容", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "分页 页数", required = true, dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "分页 页大小", required = true, dataType = "Int", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public PageInfo<AmStainListDTO> getAmStainListByAmId(String amId, String content, Integer page, Integer limit) throws GeneralException {
        ValidateUtil.isNotEmpty(amId);
        ValidateUtil.isNotNull(page, limit);
        return amStainSV.getAmStainListByAmId(amId, content, page, limit);
    }

    @RequestMapping(value = "/addAmStain", method = RequestMethod.POST)
    @ApiOperation(value = "新增负面清单", notes = "根据考核任务id获取负面清单列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "amId", value = "考核任务id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "content", value = "负面清单内容", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "title", value = "负面清单内容", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "score", value = "负面清单内容", required = true, dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "sortNo", value = "负面清单内容", required = true, dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "groupConfig", value = "分页 页数", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Integer addAmStain(String amId, String content, String title, Integer score, Integer sortNo, String groupConfig) throws GeneralException {
        ValidateUtil.isNotEmpty(amId, content, groupConfig);
        ValidateUtil.isNotNull(score, sortNo);
        return amStainSV.addAmStain(amId, content, title, score, sortNo, groupConfig, getUser().getId(), new Date());
    }

    @RequestMapping(value = "/deleteAmStainById", method = RequestMethod.POST)
    @ApiOperation(value = "删除负面清单", notes = "根据考核任务id获取负面清单列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "amsId", value = "负面清单id", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Integer deleteAmStainById(String amsId) throws GeneralException {
        ValidateUtil.isNotEmpty(amsId);
        return amStainSV.deleteAmStainById(amsId, getUser().getId(), new Date());
    }

    @RequestMapping(value = "/saveAmStain", method = RequestMethod.POST)
    @ApiOperation(value = "保存负面清单", notes = "根据考核任务id获取负面清单列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "负面清单id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "amId", value = "考核任务id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "content", value = "负面清单内容", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "title", value = "负面清单内容", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "score", value = "负面清单内容", required = true, dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "sortNo", value = "负面清单内容", required = true, dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "groupConfig", value = "分页 页数", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public Integer saveAmStain(String id, String amId, String content, String title, Integer score, Integer sortNo, String groupConfig) throws GeneralException {
        ValidateUtil.isNotEmpty(id, amId, content, groupConfig);
        ValidateUtil.isNotNull(score, sortNo);
        return amStainSV.saveAmStain(id, amId, content, title, score, sortNo, groupConfig, getUser().getId(), new Date());
    }

    @RequestMapping(value = "/getAmStainDetailId", method = RequestMethod.POST)
    @ApiOperation(value = "根据ID查询负面清单详情", notes = "根据考核任务id获取负面清单列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "负面清单id", required = true, dataType = "String", paramType = "query")})
    @ResultFormatter(formatterClass = DateValueFormatter.class)
    public AmStain getAmStainDetailId(String id) throws GeneralException {
        ValidateUtil.isNotEmpty(id);
        return amStainSV.getByPrimaryKey(id);
    }
}
