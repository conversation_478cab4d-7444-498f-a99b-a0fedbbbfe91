package com.cmos.pbms.open.service;

import com.alibaba.fastjson.JSON;
import com.cmos.pbms.open.response.TokenCheckResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;


@Slf4j
@Component
public class TokenValidationService {
    private final RestTemplate restTemplate;

    @Value("${oauth.token-validate-url}")
    private String tokenValidateUrl;

    public TokenValidationService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * 检查token是否有效
     * @param token 需要校验的token
     * @return TokenCheckResponse 校验结果
     */
    public TokenCheckResponse checkToken(String token) {
        String url = tokenValidateUrl;

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // 设置请求参数
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("token", token);

        // 构建请求实体
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);

        try {
            // 发送POST请求
            ResponseEntity<TokenCheckResponse> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    TokenCheckResponse.class
            );

            // 检查响应状态
            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            } else {
                log.error("错误请求: {},  body: {}",
                        response.getStatusCode(),
                        response.getBody() != null ? JSON.toJSONString(response.getBody()) : "null"
                );
                throw new RuntimeException("Token validation failed with status: " + response.getStatusCode());
            }
        } catch (Exception e) {
            throw new RuntimeException("Error validating token: " + e.getMessage(), e);
        }
    }

    /**
     * 简化版检查，只返回token是否有效
     * @param token 需要校验的token
     * @return boolean token是否有效
     */
    public boolean isTokenValid(String token) {
        try {
            TokenCheckResponse response = checkToken(token);
            return response != null && response.isActive();
        } catch (Exception e) {
            log.error("统一登录token验证异常: {}", e.getMessage(), e);
            return false;
        }
    }
}
