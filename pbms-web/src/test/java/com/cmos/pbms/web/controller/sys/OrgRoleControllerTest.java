package com.cmos.pbms.web.controller.sys;

import com.alibaba.dubbo.config.annotation.Reference;
import com.cmos.pbms.iservice.sys.IOrgRoleSV;
import com.cmos.pbms.iservice.sys.IUserRoleSV;
import com.cmos.pbms.web.BaseUnitTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

public class OrgRoleControllerTest extends BaseUnitTest {

    @Reference(group = "pbms")
    private IOrgRoleSV orgRoleSV;

    @Reference(group = "pbms")
    private IUserRoleSV userRoleSV;

    @Test
    public void testGrantRoleToOrg() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("subOrgId", "1");
        params.add("rolesId", "");

        try {
            MvcResult result = mockMvc.perform(
                    MockMvcRequestBuilders.post("/orgRole/grantRoleToOrg")
                            .characterEncoding("UTF-8")
                            .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                            .params(params))
                    .andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            System.out.println(result.getResponse().getContentAsString());
        } finally {
            orgRoleSV.deleteByOrg("1");
        }
    }

    @Test
    public void testRelativeUsers() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("orgid", "1");
        params.add("orgroleid", "11");
        params.add("roleid", "11");
        params.add("userids", "11");

        try {
            MvcResult result = mockMvc.perform(
                    MockMvcRequestBuilders.post("/orgRole/relativeUsers")
                            .characterEncoding("UTF-8")
                            .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                            .params(params))
                    .andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            System.out.println(result.getResponse().getContentAsString());
        } finally {
            userRoleSV.deleteByRoleAndOrg("11", "1");
        }
    }

}