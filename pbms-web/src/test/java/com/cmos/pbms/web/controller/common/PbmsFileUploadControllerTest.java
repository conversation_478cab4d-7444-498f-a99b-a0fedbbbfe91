package com.cmos.pbms.web.controller.common;

import com.cmos.pbms.web.BaseUnitTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMultipartHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

public class PbmsFileUploadControllerTest extends BaseUnitTest {
    @Test
    public void testHandleFileUpload() throws Exception {
        //模拟文件
        String textContents = "hello mock test";
        String contentType = "multipart/form-data; boundary=----WebKitFormBoundaryDJap6FSiIQ1eHco5 ";
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("objid", "test-mock");
        params.add("atttype", "1");
        MockMultipartHttpServletRequestBuilder builder = MockMvcRequestBuilders.fileUpload("/pbmsfiles/upload", "");
        builder.file(new MockMultipartFile("fileupload", "test_mock.txt", "text/plain",
                textContents.getBytes()));
        builder.contentType(contentType);
        builder.params(params);
        MvcResult result = mockMvc.perform(builder).andReturn();
        System.out.println(result.getResponse().getContentAsString());
    }

    @Test
    public void testDelete() throws Exception {
        MvcResult result = mockMvc.perform(
                MockMvcRequestBuilders.post("/pbmsfiles/delete")
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
                        .param("url", "11.11"))
                .andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
        System.out.println(result.getResponse().getContentAsString());
    }

}