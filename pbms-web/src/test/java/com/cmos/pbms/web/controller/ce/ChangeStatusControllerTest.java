package com.cmos.pbms.web.controller.ce;

import com.cmos.pbms.web.BaseUnitTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

public class ChangeStatusControllerTest extends BaseUnitTest {

    @Test
    public void testChange() throws Exception {
        MvcResult result = mockMvc.perform(
                MockMvcRequestBuilders.post("/changestatus/change")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                        .header("sn", "111"))
                .andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
        System.out.println(result.getResponse().getContentAsString());
    }

    @Test
    public void testChangepl() throws Exception {
        MvcResult result = mockMvc.perform(
                MockMvcRequestBuilders.post("/changestatus/changepl")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                        .header("sn", "111"))
                .andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
        System.out.println(result.getResponse().getContentAsString());
    }

}