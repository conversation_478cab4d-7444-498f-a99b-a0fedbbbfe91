package com.cmos.pbms.web;

import com.alibaba.fastjson.JSON;
import com.cmos.pbms.open.bean.CMOSResult;
import com.cmos.pbms.open.response.RespOAApproveSubmitDataBean;
import com.cmos.pbms.open.service.ApprovalProcessServiceOAImpl;
import com.cmos.pbms.open.utils.OAAuthUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.File;
import java.nio.charset.Charset;

/**
 * 导入用户组织信息测试
 */
public class UsersImportTest {

    private static final Logger logger = LoggerFactory.getLogger(ApprovalProcessServiceOAImpl.class);

    private static final Charset utf8 = Charset.forName("UTF-8");

    public static void main(String[] args) {

        String url = "http://localhost:8088/pbms/user/diffUserAndOrgFile2";

        HttpPost httpPost = new HttpPost(url);

        // 0、鉴权
        httpPost.setHeader("cookie","SESSION=5a388925-672a-4de3-adbd-483b99830276; isHd=0");

        // 1、参数
        MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
        multipartEntityBuilder.setCharset(utf8);
        multipartEntityBuilder.addTextBody("isGoOnImport", "true");

        String filePath = "C:\\Users\\<USER>\\Downloads\\党建系统组织用户信息导入模板.xlsx";
        File documentFile = new File(filePath);
        if (documentFile.exists()) {
            multipartEntityBuilder.addBinaryBody("excelFile", documentFile);
        }
        HttpEntity httpEntity = multipartEntityBuilder.build();
        httpPost.setEntity(httpEntity);

        sendHttpRequst(httpPost);

    }
    private static RespOAApproveSubmitDataBean sendHttpRequst(HttpPost httpPost){

        RespOAApproveSubmitDataBean resp = null;
        CloseableHttpClient client = HttpClients.createDefault();
        HttpResponse httpResponse = null;
        byte[] body = null;
        StatusLine sL = null;
        try {
            httpResponse = client.execute(httpPost);
            HttpEntity entity = httpResponse.getEntity();
            body = EntityUtils.toByteArray(entity);
            sL = httpResponse.getStatusLine();
            String json = new String(body, utf8);
            logger.info("#sendHttpRequst statusCode = " + sL.getStatusCode() + " json = " + json);

//            Type type = new TypeReference<Result<Object>>() {}.getType();
//            CMOSResult result = JSON.parseObject(json, CMOSResult.class);
//            resp = result.getBean().getResult();
//            Result.Bean<Object> bean = result.getBean();
//            resp = JSON.parseObject(JSON.toJSONString(bean), RespOAApproveSubmitDataBean.class);
//            resp = bean.get();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return resp;
    }

}
