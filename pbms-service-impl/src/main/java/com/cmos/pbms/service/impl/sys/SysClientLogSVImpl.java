package com.cmos.pbms.service.impl.sys;

import com.alibaba.dubbo.config.annotation.Service;
import com.cmos.pbms.beans.sys.SysClientLog;
import com.cmos.pbms.dao.sys.SysClientLogDAO;
import com.cmos.pbms.iservice.sys.ISysClientLogSV;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service(group = "pbms", retries = -1)
public class SysClientLogSVImpl implements ISysClientLogSV {

    @Autowired
    SysClientLogDAO sysClientLogDAO;

    @Override
    public int deleteByPrimaryKey(String id) {
        return sysClientLogDAO.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(SysClientLog sysClientLog) {
        return sysClientLogDAO.insert(sysClientLog);
    }

    @Override
    public int insertSelective(SysClientLog sysClientLog) {
        return sysClientLogDAO.insertSelective(sysClientLog);
    }

    @Override
    public SysClientLog getByPrimaryKey(String id) {
        return sysClientLogDAO.selectByPrimaryKey(id);
    }

    @Override
    public List<SysClientLog> getByUserFlg(String userFlg) {
        return sysClientLogDAO.selectByUserFlg(userFlg);
    }

    @Override
    public int updateByPrimaryKeySelective(SysClientLog sysClientLog) {
        return sysClientLogDAO.updateByPrimaryKeySelective(sysClientLog);
    }

    @Override
    public int updateByPrimaryKey(SysClientLog sysClientLog) {
        return sysClientLogDAO.updateByPrimaryKey(sysClientLog);
    }

    @Override
    public int insertBatch(List<SysClientLog> sysClientLogs) {
        return sysClientLogDAO.insertBatch(sysClientLogs);
    }
}
