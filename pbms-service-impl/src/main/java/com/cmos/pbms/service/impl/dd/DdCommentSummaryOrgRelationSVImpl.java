package com.cmos.pbms.service.impl.dd;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.cmos.common.exception.SystemFailureException;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.common.PushMsg;
import com.cmos.pbms.beans.dd.DdCommentSummary;
import com.cmos.pbms.beans.dd.DdCommentSummaryOrgRelation;
import com.cmos.pbms.beans.dto.DdCommentSummaryOrgRelationDetailDTO;
import com.cmos.pbms.beans.dto.DdCommentSummaryOrgRelationListDTO;
import com.cmos.pbms.beans.dto.PmUserDTO;
import com.cmos.pbms.beans.enums.DataIsDeleteEnum;
import com.cmos.pbms.beans.enums.ProcessTypeEnum;
import com.cmos.pbms.beans.enums.RoleTypeEnum;
import com.cmos.pbms.beans.enums.WorkTaskTypeEnum;
import com.cmos.pbms.beans.sys.ReviewLog;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.dao.common.PushMsgDAO;
import com.cmos.pbms.dao.dd.DdCommentSummaryDAO;
import com.cmos.pbms.dao.dd.DdCommentSummaryOrgRelationDAO;
import com.cmos.pbms.dao.sys.ReviewLogDAO;
import com.cmos.pbms.dao.sys.UsersDAO;
import com.cmos.pbms.iservice.dd.IDdCommentSummaryOrgRelationSV;
import com.cmos.pbms.utils.DateUtil;
import com.cmos.pbms.utils.UIDUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@Service(group = "pbms", retries = -1)
public class DdCommentSummaryOrgRelationSVImpl implements IDdCommentSummaryOrgRelationSV {

    private static final Logger logger = LoggerFactory.getLogger(DdCommentSummaryOrgRelationSVImpl.class);

    @Autowired
    private DdCommentSummaryOrgRelationDAO ddCommentSummaryOrgRelationDAO;

    @Autowired
    private DdCommentSummaryDAO ddCommentSummaryDAO;

    @Autowired
    private PushMsgDAO pushMsgDAO;

    @Autowired
    private UsersDAO usersDAO;

    @Autowired
    private ReviewLogDAO reviewLogDAO;

    @Override
    public Integer insertSelective(DdCommentSummaryOrgRelation ddCommentSummaryOrgRelation) {
        return ddCommentSummaryOrgRelationDAO.insertSelective(ddCommentSummaryOrgRelation);
    }

    @Override
    public DdCommentSummaryOrgRelation getByPrimaryKey(String id) {
        return ddCommentSummaryOrgRelationDAO.selectByPrimaryKey(id);
    }

    @Override
    public Integer updateByPrimaryKeySelective(DdCommentSummaryOrgRelation ddCommentSummaryOrgRelation) {
        return ddCommentSummaryOrgRelationDAO.updateByPrimaryKeySelective(ddCommentSummaryOrgRelation);
    }

    @Override
    public PageInfo<DdCommentSummaryOrgRelationListDTO> getByIdOrgStatus(String summaryId, String sumDtlId, Integer taskStatus, String orgId, Integer page, Integer limit) {
        PageHelper.startPage(page, limit);

        return new PageInfo<>(ddCommentSummaryOrgRelationDAO.selectListDTOBySummaryId(summaryId, sumDtlId, taskStatus, orgId));
    }

    @Override
    public Integer notifyNotDone(String userId, String summaryId, String sumDtlId) {

        DdCommentSummary ddCommentSummary = ddCommentSummaryDAO.selectByPrimaryKey(summaryId);
        if (null == ddCommentSummary)
            return -1;
        List<DdCommentSummaryOrgRelationListDTO> ddCommentSummaryOrgRelationListDTOSs = ddCommentSummaryOrgRelationDAO.selectListDTOBySummaryId(ddCommentSummary.getId(), sumDtlId, null, null);
        if (null == ddCommentSummaryOrgRelationListDTOSs || ddCommentSummaryOrgRelationListDTOSs.isEmpty())
            return -1;

        Map<String, DdCommentSummaryOrgRelationListDTO> orgId_ddRId = new HashMap<>();
        List<String> orgIds = new ArrayList<>();
        for (DdCommentSummaryOrgRelationListDTO ddCommentSummaryOrgRelationListDTOS : ddCommentSummaryOrgRelationListDTOSs)
            if (4 != ddCommentSummaryOrgRelationListDTOS.getTaskStatus()) {
                orgIds.add(ddCommentSummaryOrgRelationListDTOS.getOrgId());
                orgId_ddRId.put(ddCommentSummaryOrgRelationListDTOS.getOrgId(), ddCommentSummaryOrgRelationListDTOS);
            }

        //查询党务工作者
        List<PmUserDTO> pmUserDTOS = usersDAO.selectPMByOrgIdsAndRoleType(orgIds, Integer.valueOf(1));

        Date currentDate = new Date();
        String baseId = UIDUtil.getUID(8);
        int i = 0;
        // 给未完成任务的党组织的党务工作者发催办短信
        List<PushMsg> pushMsgs = new ArrayList<>();
        for (PmUserDTO pmUserDTO : pmUserDTOS) {

            DdCommentSummaryOrgRelationListDTO ddCommentSummaryOrgRelationListDTO = orgId_ddRId.get(pmUserDTO.getOrgId());

            Map<String, Object> param = new HashMap<>();
            param.put("rspKey", "rspId017");
            //加载短信静默时间配置

            param.put("txt_businessTheme", ddCommentSummary.getTopic());
            param.put("txt_startTime", DateUtil.format(ddCommentSummary.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
            param.put("txt_endTime", DateUtil.format(ddCommentSummary.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
            param.put("phoneNum", pmUserDTO.getTelephones());
            param.put("orgName",ddCommentSummaryOrgRelationListDTO.getOrgName());

            PushMsg pushMsg = new PushMsg();
            pushMsg.setId(UIDUtil.setUid(baseId, i++));
            pushMsg.setMsgname("评议结果待办");
            pushMsg.setContent(JSONUtils.toJSONString(param));
            pushMsg.setUserid(pmUserDTO.getUserid());
            pushMsg.setAuthorid("system");
            pushMsg.setTasktype(WorkTaskTypeEnum.DDSUMMARY.getCode());
            pushMsg.setObjid(ddCommentSummaryOrgRelationListDTO.getId());
            pushMsg.setMsgstatus(0);
            pushMsg.setSenddate(null);
            pushMsg.setIsdeleted(DataIsDeleteEnum.NORMAL.getCode());
            pushMsg.setCreatedby("system");
            pushMsg.setCreateddate(currentDate);
            pushMsg.setShouldSendDate(currentDate);
            pushMsg.setRspId("rspId017");

            pushMsgs.add(pushMsg);
        }
        return pushMsgDAO.insertBatch(pushMsgs);
    }

    @Override
    public Integer handleTask(String id, Integer taskStatus, String remark, Users users) throws SystemFailureException {
        DdCommentSummaryOrgRelation ddCommentSummaryOrgRelation = ddCommentSummaryOrgRelationDAO.selectByPrimaryKey(id);

        String operationDesc = "";
        boolean can = false;
        switch (taskStatus) {
            case 1:
                operationDesc = "领取任务";
                if (0 == ddCommentSummaryOrgRelation.getTaskStatus())
                    can = true;
                break;
            case 2:
                operationDesc = "提交审核";
                if (1 == ddCommentSummaryOrgRelation.getTaskStatus() || 3 == ddCommentSummaryOrgRelation.getTaskStatus())
                    can = true;
                break;
            case 3:
                operationDesc = "审核退回";
                if (2 == ddCommentSummaryOrgRelation.getTaskStatus())
                    can = true;
                break;
            case 4:
                operationDesc = "审核通过";
                if (2 == ddCommentSummaryOrgRelation.getTaskStatus())
                    can = true;
                break;
        }
        if (!can)
            throw new SystemFailureException("任务当前状态不允许处理");

        Date date = new Date();

        ReviewLog reviewLog = new ReviewLog();
        reviewLog.setId(UIDUtil.getUID());
        reviewLog.setProcessType(ProcessTypeEnum.DD_COMMENT.getCode());
        reviewLog.setObjectId(ddCommentSummaryOrgRelation.getId());
        reviewLog.setOperationType(taskStatus);
        reviewLog.setOperationDesc(operationDesc);
        reviewLog.setOperationUserId(users.getId());
        reviewLog.setOperationUserName(users.getUsername());
        reviewLog.setResuseReason(remark);
        reviewLog.setRemark(remark);
        reviewLog.setOperationTime(date);
        reviewLogDAO.insertSelective(reviewLog);

        if (1 == taskStatus || 2 == taskStatus) {
            ddCommentSummaryOrgRelation.setCurrentUserId(users.getId());
            ddCommentSummaryOrgRelation.setProcessingTime(date);
        }
        ddCommentSummaryOrgRelation.setTaskStatus(taskStatus);
        ddCommentSummaryOrgRelation.setRemark(remark);
        ddCommentSummaryOrgRelation.setModifiedBy(users.getId());
        ddCommentSummaryOrgRelation.setModifiedDate(date);

        return ddCommentSummaryOrgRelationDAO.updateByPrimaryKeySelective(ddCommentSummaryOrgRelation);
    }

    @Override
    public PageInfo<DdCommentSummaryOrgRelationListDTO> getCurrentUserList(String orgId, String topic, String commentYear, Integer taskStatus, Integer page, Integer limit) {
        PageHelper.startPage(page, limit);

        return new PageInfo<>(ddCommentSummaryOrgRelationDAO.selectListDTOByOrgId(orgId, topic, commentYear, taskStatus));
    }

    @Override
    public PageInfo<DdCommentSummaryOrgRelationListDTO> getCurrentUserListToReview(String orgId, String topic, String commentYear, Integer taskStatus, Integer page, Integer limit) {
        PageHelper.startPage(page, limit);

        return new PageInfo<>(ddCommentSummaryOrgRelationDAO.selectListDTOByOrgIdToReview(orgId, topic, commentYear, taskStatus));
    }

    @Override
    public DdCommentSummaryOrgRelationDetailDTO getDdCommentSummaryOrgRelationDetail(String sumDtlId) {
        return ddCommentSummaryOrgRelationDAO.selectDdCommentSummaryOrgRelationDetail(sumDtlId);
    }

    @Override
    public Map<String, List<Users>> getWorkerAndLeaderUsers(String sumDtlId) {
        DdCommentSummaryOrgRelation ddCommentSummaryOrgRelation = ddCommentSummaryOrgRelationDAO.selectByPrimaryKey(sumDtlId);
        if (null == ddCommentSummaryOrgRelation)
            return null;
        Map<String, List<Users>> result = new HashMap<>();
        //查询党务工作者
        result.put("worker", usersDAO.selectUsersByOrgidAndRoleType(ddCommentSummaryOrgRelation.getOrgId(), RoleTypeEnum.WORKER.getCode()));
        //领导
        result.put("leader", usersDAO.selectUsersByOrgidAndRoleType(ddCommentSummaryOrgRelation.getOrgId(), RoleTypeEnum.LEADER.getCode()));
        return result;
    }
}
