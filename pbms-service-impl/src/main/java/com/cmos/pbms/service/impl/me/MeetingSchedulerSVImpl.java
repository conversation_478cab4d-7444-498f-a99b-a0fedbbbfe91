package com.cmos.pbms.service.impl.me;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;
import com.cmos.pbms.beans.common.PushMsg;
import com.cmos.pbms.beans.dto.UserRoleForMeDTO;
import com.cmos.pbms.beans.enums.MeetingCheckTypeEnum;
import com.cmos.pbms.beans.enums.MeetingStatusEnum;
import com.cmos.pbms.beans.enums.MeetingTypeEnum;
import com.cmos.pbms.beans.enums.RoleTypeEnum;
import com.cmos.pbms.beans.me.Meetings;
import com.cmos.pbms.beans.pm.Organization;
import com.cmos.pbms.beans.sys.DictionaryItems;
import com.cmos.pbms.beans.sys.Param;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.dao.common.PushMsgDAO;
import com.cmos.pbms.dao.me.MeetingsDAO;
import com.cmos.pbms.dao.pm.OrganizationDAO;
import com.cmos.pbms.dao.sys.DictionaryItemsDAO;
import com.cmos.pbms.dao.sys.ParamDao;
import com.cmos.pbms.dao.sys.UsersDAO;
import com.cmos.pbms.iservice.me.IMeetingSchedulerSV;
import com.cmos.pbms.service.common.ComUtils;
import com.cmos.pbms.utils.ConvertUtil;
import com.cmos.pbms.utils.NgTaskUtil;
import com.cmos.pbms.utils.UIDUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import scala.collection.mutable.StringBuilder;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 三会一课调度任务
 */
@Service(group = "pbms", retries = -1)
public class MeetingSchedulerSVImpl implements IMeetingSchedulerSV {
    @Autowired
    private ParamDao paramDao;

    @Autowired
    private MeetingsDAO meetingsDAO;

    @Autowired
    private UsersDAO usersDAO;

    @Autowired
    private PushMsgDAO pushMsgDAO;

    @Autowired
    private OrganizationDAO organizationDAO;

    @Autowired
    private DictionaryItemsDAO dictionaryItemsDAO;

    @Autowired
    private ComUtils comUtils;
    /**
     * 季度性预警通知执行器
     */
    private static Thread quarterlyWarningNoticeExecutor = null;

    /**
     * 季度性预警通知服务参数名称
     */
    private static final String QUARTERLY_WARNING_NOTICE_SERVICE_NAME = "quarterlyWarningNotice";

    /**
     * 季度性超时执行器
     */
    private static Thread quarterlyTimeoutExecutor = null;

    /**
     * 季度性超时服务参数名称
     */
    private static final String QUARTERLY_TIMEOUT_SERVICE_NAME = "handleQuarterlyTimeout";

    /**
     * 月度性预警通知执行器
     */
    private static Thread monthlyWarningNoticeExecutor = null;

    /**
     * 月度性预警通知服务参数名称
     */
    private static final String MONTHLY_WARNING_NOTICE_SERVICE_NAME = "monthlyWarningNotice";

    /**
     * 月度性超时执行器
     */
    private static Thread monthlyTimeoutExecutor = null;

    /**
     * 月度性超时服务参数名称
     */
    private static final String MONTHLY_TIMEOUT_SERVICE_NAME = "handleMonthlyTimeout";

    private static final String SYS_USER = "system";

    /**
     * 判断当前是否为季度对应的检查日
     *
     * @param checkType
     * @return
     */
    public boolean isQuarterlyCheckDate(MeetingCheckTypeEnum checkType) {
        // 获取当前时间
        Calendar currentTime = Calendar.getInstance();
        int currentYear = currentTime.get(Calendar.YEAR);
        int currentMonth = currentTime.get(Calendar.MONTH) + 1;
        int currentDay = currentTime.get(Calendar.DAY_OF_MONTH);

        // 根据检查类型获取字典对应值
        DictionaryItems dictionaryItem = dictionaryItemsDAO.selectByCodeAndDictCode(checkType.getItemcode(), checkType.getDictcode());

        // 对字典值进行解析处理
        String itemText = dictionaryItem.getDescription();
        String[] configArray = itemText.split("\\|");
        if (configArray.length == 0) {
            return false;
        }
        List<String> checkDateList = new ArrayList();
        for (String config : configArray) {
            if (config.indexOf("~") != -1) {
                // 分割字符串
                String[] dateArray = config.split("~");
                String[] beginTimeArray = dateArray[0].split("\\.");
                String[] endTimeArray = dateArray[1].split("\\.");
                // 将日期范围加入到集合中
                Calendar beginTime = Calendar.getInstance();
                beginTime.set(currentYear, Integer.parseInt(beginTimeArray[0]) - 1, Integer.parseInt(beginTimeArray[1]));
                Calendar endTime = Calendar.getInstance();
                endTime.set(currentYear, Integer.parseInt(endTimeArray[0]) - 1, Integer.parseInt(endTimeArray[1]));
                while (endTime.compareTo(beginTime) >= 0) {
                    checkDateList.add((beginTime.get(Calendar.MONTH) + 1) + "." + beginTime.get(Calendar.DAY_OF_MONTH));
                    beginTime.add(Calendar.DATE, 1);
                }
            } else {
                checkDateList.add(config);
            }
        }

        // 判断当前时间是否为对应检查日
        String currentDayStr = currentMonth + "." + currentDay;
        boolean isCheckDay = checkDateList.contains(currentDayStr);

        return isCheckDay;
    }

    /**
     * 发送季度性预警通知
     *
     * @param sn        调度标识号
     * @param checkType 检查日类型
     * @return 执行结果
     */
    @Override
    public String sendQuarterlyWarningNotice(final String sn, final MeetingCheckTypeEnum checkType) {
        JSONObject result = new JSONObject();

        // 未召开检查 和 未完成检查 的，需要防止当天重复执行
        boolean isFilterDuplicate = checkType != MeetingCheckTypeEnum.QUARTERLY_CHECKERROR;
        // 判断服务是否正在执行或已执行（减少被重复调度概率）
        if ((quarterlyWarningNoticeExecutor != null && quarterlyWarningNoticeExecutor.isAlive())
                || !tryExecuteService(QUARTERLY_WARNING_NOTICE_SERVICE_NAME, isFilterDuplicate)) {
            result.put("rtnCode", "0");
            result.put("rtnMsg", "【三会一课季度性预警】作业已被执行。");
            return result.toString();
        }

        // 服务执行线程
        quarterlyWarningNoticeExecutor = new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    getWarningMeetingAndSendNotice(checkType);

                    NgTaskUtil.updateNgTaskLog(sn, "已完成【三会一课季度性预警】作业。");
                } finally {
                    finishService(QUARTERLY_WARNING_NOTICE_SERVICE_NAME);
                }
            }
        });
        quarterlyWarningNoticeExecutor.start();

        result.put("rtnCode", "0");
        result.put("rtnMsg", "已成功启动【三会一课季度性预警】作业。");
        return result.toString();
    }

    /**
     * 处理季度性超时
     *
     * @param sn 调度标识号
     * @return
     */
    @Override
    public String handleQuarterlyTimeout(final String sn) {
        JSONObject result = new JSONObject();

        // 判断是否为季度性超时处理日
        if (!isQuarterlyCheckDate(MeetingCheckTypeEnum.QUARTERLY_CHECKOVERTIME)) {
            result.put("rtnCode", "0");
            result.put("rtnMsg", "执行成功，【三会一课处理季度性超时】作业无需在非调度日执行。");
            return result.toString();
        }

        // 判断服务是否正在执行或已执行（减少被重复调度概率）
        if ((quarterlyTimeoutExecutor != null && quarterlyTimeoutExecutor.isAlive())
                || !tryExecuteService(QUARTERLY_TIMEOUT_SERVICE_NAME, true)) {
            result.put("rtnCode", "0");
            result.put("rtnMsg", "【三会一课处理季度性超时】作业已被执行。");
            return result.toString();
        }

        // 服务执行线程
        quarterlyTimeoutExecutor = new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Map<String, Object> params = new HashMap<>(16);

                    // 季度性会议（包括支部党员大会、党课）
                    StringBuilder types = new StringBuilder();
                    types.append(MeetingTypeEnum.BRANCHMASSES.getCode()).append(",").append(MeetingTypeEnum.NEWSTUDY.getCode());
                    params.put("types", types);

                    // “待计划/待通知/待召开”的会议
                    StringBuilder status = new StringBuilder();
                    status.append(MeetingStatusEnum.TOBEPLAN.getCode()).append(",")
                            .append(MeetingStatusEnum.TOBENOTIFY.getCode()).append(",")
                            .append(MeetingStatusEnum.TOBECONVENE.getCode());
                    params.put("status", status);

                    // 超时计划内会议
                    params.put("isovertime", 1);
                    SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    params.put("currenttime", dayFormat.format(new Date()));

                    // 获取“待计划/待通知/待召开”的超时会议（包括党支部大会、党课）
                    List<Meetings> meetingsList = meetingsDAO.selectWarningOrTimeoutPlanMeetingList(params);

                    // 将会议设置为“超期未完成”
                    for (Meetings meeting : meetingsList) {
                        meeting.setIserror((short) 1);
                        // meeting.setStatus(MeetingStatusEnum.OVERTIME.getCode());
                        Meetings meetings1=meetingsDAO.selectByPrimaryKey(meeting.getId());
                        //
                        if(StringUtils.isBlank(meeting.getModerator())){
                            meeting.setModerator(meetings1.getModerator());
                        }
                        if(meeting.getActivityCount()==null){
                            meeting.setActivityCount(meetings1.getActivityCount());
                        }
                        if(StringUtils.isBlank(meeting.getAddress())){
                            meeting.setAddress(meetings1.getAddress());
                        }
                        if(StringUtils.isBlank(meeting.getRecordContent())){
                            meeting.setRecordContent(meetings1.getRecordContent());
                        }
                        meetingsDAO.updateByPrimaryKey(meeting);
                    }

                    NgTaskUtil.updateNgTaskLog(sn, "已完成【三会一课处理季度性超时】作业。");
                } finally {
                    finishService(QUARTERLY_TIMEOUT_SERVICE_NAME);
                }
            }
        });

        // 启动服务并返回结果
        quarterlyTimeoutExecutor.start();
        result.put("rtnCode", "0");
        result.put("rtnMsg", "已成功启动【三会课-处理季度性超时】作业。");

        return result.toString();
    }

    /**
     * 判断是否为月度检查日
     *
     * @param checkType
     * @return
     */
    public boolean isMonthlyCheckDate(MeetingCheckTypeEnum checkType) {
        // 获取当前时间
        Calendar currentTime = Calendar.getInstance();
        int currentDay = currentTime.get(Calendar.DAY_OF_MONTH);

        // 根据检查类型获取字典对应值
        DictionaryItems dictionaryItem = dictionaryItemsDAO.selectByCodeAndDictCode(checkType.getItemcode(), checkType.getDictcode());

        // 对字典值进行解析处理
        String itemText = dictionaryItem.getDescription();
        String[] configArray = itemText.split("\\|");
        if (configArray.length == 0) {
            return false;
        }
        List<Integer> checkDateList = new ArrayList();
        for (String config : configArray) {
            if (config.indexOf("~") != -1) {
                // 分割字符串
                String[] dateArray = config.split("~");
                int beginDay = Integer.parseInt(dateArray[0]);
                int endDay = Integer.parseInt(dateArray[1]);

                // 将日期范围加入到集合中
                while (endDay >= beginDay) {
                    checkDateList.add(beginDay);
                    beginDay++;
                }
            } else {
                checkDateList.add(Integer.parseInt(config));
            }
        }

        // 判断当前时间是否为对应检查日
        boolean isCheckDay = checkDateList.contains(currentDay);

        return isCheckDay;
    }

    /**
     * 发送月度性预警通知
     *
     * @param sn        调度标识号
     * @param checkType
     * @return
     */
    @Override
    public String sendMonthlyWarningNotice(final String sn, final MeetingCheckTypeEnum checkType) {
        JSONObject result = new JSONObject();

        // 未召开检查 和 未完成检查 的，需要防止当天重复执行
        boolean isFilterDuplicate = checkType != MeetingCheckTypeEnum.MONTHLY_CHECKERROR;
        // 判断服务是否正在执行或已执行（减少被重复调度概率）
        if ((monthlyWarningNoticeExecutor != null && monthlyWarningNoticeExecutor.isAlive())
                || !tryExecuteService(MONTHLY_WARNING_NOTICE_SERVICE_NAME, isFilterDuplicate)) {
            result.put("rtnCode", "0");
            result.put("rtnMsg", "【三会一课月度性预警】作业已被执行。");
            return result.toString();
        }

        // 服务执行线程
        monthlyWarningNoticeExecutor = new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    getWarningMeetingAndSendNotice(checkType);

                    NgTaskUtil.updateNgTaskLog(sn, "已完成【三会一课月度性预警】作业。");
                } finally {
                    finishService(MONTHLY_WARNING_NOTICE_SERVICE_NAME);
                }
            }
        });

        monthlyWarningNoticeExecutor.start();
        result.put("rtnCode", "0");
        result.put("rtnMsg", "已成功启动【三会一课月度性预警】作业。");

        return result.toString();
    }

    /**
     * 处理月度性超时
     *
     * @param sn 调度标识号
     * @return
     */
    @Override
    public String handleMonthlyTimeout(final String sn) {
        JSONObject result = new JSONObject();

        // 判断是否为月度超时预警日
        if (!isMonthlyCheckDate(MeetingCheckTypeEnum.MONTHLY_CHECKOVERTIME)) {
            result.put("rtnCode", "0");
            result.put("rtnMsg", "【三会一课月度性超时】作业无需在非调度日执行。");
            return result.toString();
        }

        // 判断服务是否正在执行或已执行（减少被重复调度概率）
        if ((monthlyTimeoutExecutor != null && monthlyTimeoutExecutor.isAlive())
                || !tryExecuteService(MONTHLY_TIMEOUT_SERVICE_NAME, true)) {
            result.put("rtnCode", "0");
            result.put("rtnMsg", "【三会一课月度性超时】作业已被执行。");
            return result.toString();
        }

        // 服务执行线程
        monthlyTimeoutExecutor = new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Map<String, Object> params = new HashMap<>(16);

                    // 月度性会议（包括支部委员会、党小组会）
                    StringBuilder types = new StringBuilder();
                    types.append(MeetingTypeEnum.BRANCHLEADER.getCode()).append(",").append(MeetingTypeEnum.PARTYGROUP.getCode());
                    params.put("types", types);

                    // “待计划/待通知/待召开”的会议
                    StringBuilder status = new StringBuilder();
                    status.append(MeetingStatusEnum.TOBEPLAN.getCode()).append(",")
                            .append(MeetingStatusEnum.TOBENOTIFY.getCode()).append(",")
                            .append(MeetingStatusEnum.TOBECONVENE.getCode());
                    params.put("status", status);

                    // 超时计划内会议
                    params.put("isovertime", 1);
                    SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    params.put("currenttime", dayFormat.format(new Date()));

                    // 获取“待计划/待通知/待召开”的会议（包括支部委员会、党小组会）
                    List<Meetings> meetingsList = meetingsDAO.selectWarningOrTimeoutPlanMeetingList(params);

                    // 将会议设置为“超期未完成”
                    for (Meetings meeting : meetingsList) {
                        meeting.setIserror((short) 1);
                        // meeting.setStatus(MeetingStatusEnum.OVERTIME.getCode());
                        Meetings meetings1=meetingsDAO.selectByPrimaryKey(meeting.getId());
                        //
                        if(StringUtils.isBlank(meeting.getModerator())){
                            meeting.setModerator(meetings1.getModerator());
                        }
                        if(meeting.getActivityCount()==null){
                            meeting.setActivityCount(meetings1.getActivityCount());
                        }
                        if(StringUtils.isBlank(meeting.getAddress())){
                            meeting.setAddress(meetings1.getAddress());
                        }
                        if(StringUtils.isBlank(meeting.getRecordContent())){
                            meeting.setRecordContent(meetings1.getRecordContent());
                        }
                        meetingsDAO.updateByPrimaryKey(meeting);
                    }

                    NgTaskUtil.updateNgTaskLog(sn, "已完成【三会一课处理月度性超时】作业。");
                } finally {
                    finishService(MONTHLY_TIMEOUT_SERVICE_NAME);
                }
            }
        });

        // 启动服务并返回结果
        monthlyTimeoutExecutor.start();
        result.put("rtnCode", "0");
        result.put("rtnMsg", "已成功启动【三会一课处理月度性超时】作业。");

        return result.toString();
    }

    /**
     * 获取异常会议并推送通知（同时标识为异常）
     *
     * @param checkType
     */
    private void getWarningMeetingAndSendNotice(MeetingCheckTypeEnum checkType) {
        Map<String, Object> params = new HashMap<>(16);

        Date currTime = new Date();

        Boolean isQuarter = false;

        StringBuilder types = new StringBuilder();
        if (checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKOVERTIME
                || checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKUNFINISHED
                || checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKUNCONVOKE
                || checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKERROR) {
            isQuarter = true;
            // 季度通知查询“支部党员大会或党课”
            types.append(MeetingTypeEnum.BRANCHMASSES.getCode()).append(",").append(MeetingTypeEnum.NEWSTUDY.getCode());
        } else if (checkType == MeetingCheckTypeEnum.MONTHLY_CHECKOVERTIME
                || checkType == MeetingCheckTypeEnum.MONTHLY_CHECKUNFINISHED
                || checkType == MeetingCheckTypeEnum.MONTHLY_CHECKUNCONVOKE
                || checkType == MeetingCheckTypeEnum.MONTHLY_CHECKERROR) {
            isQuarter = false;
            // 月度通知查询“支部委员会或党小组会”
            types.append(MeetingTypeEnum.BRANCHLEADER.getCode()).append(",").append(MeetingTypeEnum.PARTYGROUP.getCode());
        }
        params.put("types", types);

        StringBuilder status = new StringBuilder();
        if (checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKERROR
                || checkType == MeetingCheckTypeEnum.MONTHLY_CHECKERROR) {
            // 季度性或月度性获取未开展会议（“待计划/待通知”）
            status.append(MeetingStatusEnum.TOBEPLAN.getCode()).append(",").append(MeetingStatusEnum.TOBENOTIFY.getCode());
        } else if (checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKUNCONVOKE
                || checkType == MeetingCheckTypeEnum.MONTHLY_CHECKUNCONVOKE) {
            // 季度性或月度性获取未开展会议（“待计划/待通知”）
            status.append(MeetingStatusEnum.TOBEPLAN.getCode()).append(",")
                    .append(MeetingStatusEnum.TOBENOTIFY.getCode()).append(",")
                    .append(MeetingStatusEnum.TOBECONVENE.getCode());
        } else if (checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKUNFINISHED
                || checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKOVERTIME
                || checkType == MeetingCheckTypeEnum.MONTHLY_CHECKUNFINISHED
                || checkType == MeetingCheckTypeEnum.MONTHLY_CHECKOVERTIME) {
            // 季度性或月度性获取未完成会议（包括待计划、待通知、待召开、召开中、待归档）
            status.append(MeetingStatusEnum.TOBEPLAN.getCode()).append(",")
                    .append(MeetingStatusEnum.TOBENOTIFY.getCode()).append(",")
                    .append(MeetingStatusEnum.TOBECONVENE.getCode()).append(",")
                    .append(MeetingStatusEnum.CONVENING.getCode()).append(",")
                    .append(MeetingStatusEnum.TOBIEHOLD.getCode());
        }
        params.put("status", status);

        String checkStartDate;
        // 非具体预警通知日，需要过滤iserror=1的会议
        if (checkType != MeetingCheckTypeEnum.QUARTERLY_CHECKOVERTIME && checkType != MeetingCheckTypeEnum.MONTHLY_CHECKOVERTIME) {
            params.put("isfiltererror", "filter");
            checkStartDate = setCheckErrStartTime(isQuarter, currTime);
        } else {
            params.put("isovertime", 1);
            params.put("currenttime", currTime);
            checkStartDate = setCheckOverStartTime(isQuarter, currTime);
        }

        if (null != checkStartDate && !checkStartDate.isEmpty()) {
            params.put("checkDate", checkStartDate);
        }

        // 获取会议
        List<Meetings> meetingsList = meetingsDAO.selectWarningOrTimeoutPlanMeetingList(params);

        for (Meetings meeting : meetingsList) {
            // 根据类型初始化通知内容
            NoticeMessage superiorNoticeMessage = generateNoticeMessage(meeting, checkType, true);
            NoticeMessage moderatorNoticeMessage = generateNoticeMessage(meeting, checkType, false);

            // 获取当前会议组织机构
            Organization organization = organizationDAO.selectByPrimaryKey(meeting.getOrgid());
            // 获取会议上级党组织的“业务管理员”
            // List<String> superiorIds = usersDAO.selectUserIdsByOrgIdAndRoleName(organization.getParentid(), "业务管理员");
            List<UserRoleForMeDTO> leaderIds = usersDAO.selectUserPMIdsByOrgid(organization.getParentid(), RoleTypeEnum.WORKER.getCode());
            // 发送预警通知
            sendMessage(superiorNoticeMessage, leaderIds, meeting);

            // 获取会议的负责人
//            List<String> moderatorIds = new ArrayList();
            List<UserRoleForMeDTO> moderatorIds = new ArrayList(10);
            if (StringUtils.isBlank(meeting.getModerator())) {
                // 若计划未制定，则获取会议党组织的“党组领导”
//                moderatorIds = usersDAO.selectLeaderIdsByOrgid(meeting.getOrgid());
                moderatorIds = usersDAO.selectUserPMIdsByOrgid(meeting.getOrgid(), RoleTypeEnum.WORKER.getCode());
            } else {
                // 若计划已指定，则通知会议对应的负责人
                Users moderator = usersDAO.selectNormalByPrimaryKey(meeting.getModerator());
                if (null != moderator) {
                    UserRoleForMeDTO userDTO = new UserRoleForMeDTO();
                    userDTO.setUserid(meeting.getModerator());
                    userDTO.setUserid(moderator.getTelephones());
                    moderatorIds.add(userDTO);
                }
            }

            // 将会议/党课标识为异常
            if (checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKOVERTIME || checkType == MeetingCheckTypeEnum.MONTHLY_CHECKOVERTIME) {
                meeting.setIserror((short) 2);
            } else {
                meeting.setIserror((short) 1);
            }

            meeting.setModifiedby(SYS_USER);
            meeting.setModifieddate(currTime);
            Meetings meetings1=this.meetingsDAO.selectByPrimaryKey(meeting.getId());
            //
            if(StringUtils.isBlank(meeting.getModerator())){
                meeting.setModerator(meetings1.getModerator());
            }
            if(meeting.getActivityCount()==null){
                meeting.setActivityCount(meetings1.getActivityCount());
            }
            if(StringUtils.isBlank(meeting.getAddress())){
                meeting.setAddress(meetings1.getAddress());
            }
            if(StringUtils.isBlank(meeting.getRecordContent())){
                meeting.setRecordContent(meetings1.getRecordContent());
            }
            meetingsDAO.updateByPrimaryKeySelective(meeting);

            // 发送预警通知
            sendMessage(moderatorNoticeMessage, moderatorIds, meeting);
        }
    }

    private String setCheckErrStartTime(boolean isQ, Date currTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currTime);
        int m = calendar.get(Calendar.MONTH) + 1;
        String startTime = "";
        if (isQ) {
            switch (m) {
                case 1:
                case 2:
                case 3:
                    startTime = calendar.get(Calendar.YEAR) + "-01-01 00:00:00";
                    break;
                case 4:
                case 5:
                case 6:
                    startTime = calendar.get(Calendar.YEAR) + "-04-01 00:00:00";
                    break;
                case 7:
                case 8:
                case 9:
                    startTime = calendar.get(Calendar.YEAR) + "-07-01 00:00:00";
                    break;
                case 10:
                case 11:
                case 12:
                    startTime = calendar.get(Calendar.YEAR) + "-10-01 00:00:00";
                    break;
            }
        } else {
            startTime = calendar.get(Calendar.YEAR) + "-" + m + "-01 00:00:00";
        }

        return startTime;
    }

    private String setCheckOverStartTime(boolean isQ, Date currTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currTime);
        int m = calendar.get(Calendar.MONTH) + 1;
        String startTime = "";
        if (isQ) {
            switch (m) {
                case 1:
                case 2:
                case 3:
                    startTime = (calendar.get(Calendar.YEAR) - 1) + "-09-01 00:00:00";
                    break;
                case 4:
                case 5:
                case 6:
                    startTime = calendar.get(Calendar.YEAR) + "-01-01 00:00:00";
                    break;
                case 7:
                case 8:
                case 9:
                    startTime = calendar.get(Calendar.YEAR) + "-04-01 00:00:00";
                    break;
                case 10:
                case 11:
                case 12:
                    startTime = calendar.get(Calendar.YEAR) + "-07-01 00:00:00";
                    break;
            }
        } else {
            switch (m) {
                case 1:
                    startTime = (calendar.get(Calendar.YEAR) - 1) + "-12-01 00:00:00";
                    break;
                default:
                    startTime = calendar.get(Calendar.YEAR) + "-" + (m - 1) + "-01 00:00:00";
                    break;
            }
        }

        return startTime;
    }

    /**
     * 通知消息
     */
    class NoticeMessage {
        private String title;

        private Map<String, Object> param;

        private Date senDate;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public Map<String, Object> getParam() {
            return param;
        }

        public void setParam(Map<String, Object> param) {
            this.param = param;
        }

        public Date getSenDate() {
            return senDate;
        }

        public void setSenDate(Date senDate) {
            this.senDate = senDate;
        }
    }

    /**
     * 生成通知
     *
     * @param meeting          会议
     * @param checkType        检查类型
     * @param isSuperiorNotice 是否为上级组织通知
     * @return
     */
    private NoticeMessage generateNoticeMessage(Meetings meeting, MeetingCheckTypeEnum checkType, boolean isSuperiorNotice) {
        NoticeMessage message = new NoticeMessage();

        String meetingType = "unknow";
        if (meeting.getType() == MeetingTypeEnum.BRANCHMASSES.getCode()) {
            meetingType = MeetingTypeEnum.BRANCHMASSES.getDesc();
        } else if (meeting.getType() == MeetingTypeEnum.BRANCHLEADER.getCode()) {
            meetingType = MeetingTypeEnum.BRANCHLEADER.getDesc();
        } else if (meeting.getType() == MeetingTypeEnum.PARTYGROUP.getCode()) {
            meetingType = MeetingTypeEnum.PARTYGROUP.getDesc();
        } else if (meeting.getType() == MeetingTypeEnum.NEWSTUDY.getCode()) {
            meetingType = MeetingTypeEnum.NEWSTUDY.getDesc();
        }
        message.setTitle("【预警通知】" + meeting.getTopic());

        // 组织短信内容
        Map<String, Object> param = new HashMap<>();
        param.put("txt_businessType", meetingType);

        if (checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKUNCONVOKE
                || checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKERROR
                || checkType == MeetingCheckTypeEnum.MONTHLY_CHECKUNCONVOKE
                || checkType == MeetingCheckTypeEnum.MONTHLY_CHECKERROR) {
            if (isSuperiorNotice) {
                param.put("txt_orgName", meeting.getOrgname());
                param.put("txt_action2", "请监督下级支部开展");
            } else {
                param.put("txt_orgName", "");
                param.put("txt_action2", "请及时开展");
            }
            param.put("txt_action1", "执行");
            param.put("rspKey", "rspId009");
            message.setSenDate(comUtils.getNoticDate("rspId009"));
            if (checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKUNCONVOKE
                    || checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKERROR) {
                param.put("txt_dateUnit", "季度");
            } else {
                param.put("txt_dateUnit", "月度");
            }
        } else if (checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKUNFINISHED || checkType == MeetingCheckTypeEnum.MONTHLY_CHECKUNFINISHED) {
            if (isSuperiorNotice) {
                param.put("txt_orgName", meeting.getOrgname());
                param.put("txt_action2", "请监督下级支部处理");
            } else {
                param.put("txt_orgName", "");
                param.put("txt_action2", "请及时处理");
            }
            param.put("txt_action1", "完成");
            param.put("rspKey", "rspId009");
            message.setSenDate(comUtils.getNoticDate("rspId009"));
            if (checkType == MeetingCheckTypeEnum.QUARTERLY_CHECKUNFINISHED) {
                param.put("txt_dateUnit", "季度");
            } else {
                param.put("txt_dateUnit", "月度");
            }
        }
        message.setParam(param);

        return message;
    }

    /**
     * 推送通知
     *
     * @param message         通知消息
     * @param recipientIdList 接收者id集合
     */
    private void sendMessage(NoticeMessage message, List<UserRoleForMeDTO> recipientIdList, Meetings meetings) {
        Date currentTime = new Date();
        Map<String, Object> param = message.getParam();

        for (UserRoleForMeDTO user : recipientIdList) {
            PushMsg pushMsg = new PushMsg();
            pushMsg.setId(UIDUtil.getUID());
            pushMsg.setMsgname(message.getTitle());
            pushMsg.setUserid(user.getUserid());
            pushMsg.setAuthorid("system");
            pushMsg.setTasktype(ConvertUtil.meetingTypeToTaskType(meetings.getType()));
            pushMsg.setObjid(meetings.getId());
            pushMsg.setMsgtype(1);
            pushMsg.setMsgstatus(0);
            pushMsg.setIsdeleted(0);
            pushMsg.setCreatedby("system");
            pushMsg.setCreateddate(currentTime);
            pushMsg.setShouldSendDate(message.getSenDate());
            param.put("phoneNum", user.getTelephones());
            pushMsg.setContent(JSONUtils.toJSONString(param));
            pushMsg.setRspId(String.valueOf(param.get("rspKey")));
            pushMsgDAO.insertSelective(pushMsg);
        }
    }

    /**
     * 尝试执行
     *
     * @param serviceName       状态参数名称
     * @param isFilterDuplicate 是否过滤当天重复执行
     * @return 是否可执行
     */
    private boolean tryExecuteService(String serviceName, boolean isFilterDuplicate) {
        boolean result = false;
        String statusParamName = serviceName + "Status";
        String executeTimeParamName = serviceName + "Time";

        // 需要过滤当天是否重复执行的
        if (isFilterDuplicate) {
            // 日期判断
            Param executeTimeParam = paramDao.getByParamName(executeTimeParamName);
            if (executeTimeParam != null) {
                //Date executeTime = new Date(Long.parseLong(executeTimeParam.getParamValue()));
                Calendar lastExecuteTime = Calendar.getInstance();
                lastExecuteTime.setTimeInMillis(Long.parseLong(executeTimeParam.getParamValue()));
                Calendar currentTime = Calendar.getInstance();

                // 若当天已执行过，则不能再执行
                if (lastExecuteTime.get(Calendar.YEAR) == currentTime.get(Calendar.YEAR)
                        && lastExecuteTime.get(Calendar.MONTH) == currentTime.get(Calendar.MONTH)
                        && lastExecuteTime.get(Calendar.DAY_OF_MONTH) == currentTime.get(Calendar.DAY_OF_MONTH)) {
                    return false;
                }
            }
        }

        // 状态判断
        Param executeStatus = paramDao.getByParamName(statusParamName);
        if (executeStatus == null) {
            executeStatus = new Param();
            executeStatus.setId(UIDUtil.getUID());
            executeStatus.setParamName(statusParamName);
            executeStatus.setParamValue("1");
            executeStatus.setParamDesc("标识当前是否有线程正在进行处理，在每次启动作业时被Update为1，作业完成时被Update为0");
            paramDao.insert(executeStatus);

            result = true;
        } else if ("0".equals(executeStatus.getParamValue())) {
            executeStatus.setParamValue("1");
            paramDao.updateByPrimaryKey(executeStatus);

            result = true;
        }

        return result;
    }

    /**
     * 完成服务执行
     *
     * @param serviceName
     */
    private void finishService(String serviceName) {
        String statusParamName = serviceName + "Status";
        String executeTimeParamName = serviceName + "Time";

        // 状态更新
        Param statusParam = paramDao.getByParamName(statusParamName);
        if (statusParam != null) {
            statusParam.setParamValue("0");
            paramDao.updateByPrimaryKey(statusParam);
        }

        // 更新最后一次执行时间
        Param executeTimeParam = paramDao.getByParamName(executeTimeParamName);
        if (executeTimeParam == null) {
            executeTimeParam = new Param();
            executeTimeParam.setId(UIDUtil.getUID());
            executeTimeParam.setParamName(executeTimeParamName);
            executeTimeParam.setParamValue(String.valueOf((new Date()).getTime()));
            executeTimeParam.setParamDesc("上次同步时间，在每次开始同步时被获取，在成功完成同步调度后被更新。");
            paramDao.insert(executeTimeParam);
        } else {
            executeTimeParam.setParamValue(String.valueOf((new Date()).getTime()));
            paramDao.updateByPrimaryKey(executeTimeParam);
        }
    }
}
