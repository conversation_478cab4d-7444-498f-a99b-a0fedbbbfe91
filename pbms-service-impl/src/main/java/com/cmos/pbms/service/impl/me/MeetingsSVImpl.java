package com.cmos.pbms.service.impl.me;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.exception.SystemFailureException;
import com.cmos.common.validator.me.VMeetingsBasicUpdateBean;
import com.cmos.common.validator.me.VMeetingsSummaryListSearchBean;
import com.cmos.common.web.upload.exception.StorageException;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.an.AnActivityTaskRelationship;
import com.cmos.pbms.beans.common.Attachments;
import com.cmos.pbms.beans.common.PushMsg;
import com.cmos.pbms.beans.common.WorkTask;
import com.cmos.pbms.beans.dto.*;
import com.cmos.pbms.beans.enums.*;
import com.cmos.pbms.beans.me.*;
import com.cmos.pbms.beans.msg.Msg;
import com.cmos.pbms.beans.pageHome.OrgLifeStatistics;
import com.cmos.pbms.beans.pm.Organization;
import com.cmos.pbms.beans.pm.Userposition;
import com.cmos.pbms.beans.sys.DictionaryItems;
import com.cmos.pbms.beans.sys.ReviewLog;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.beans.vd.VdMeetings;
import com.cmos.pbms.dao.an.AnActivityTaskRelationshipMapper;
import com.cmos.pbms.dao.common.AttachmentsDAO;
import com.cmos.pbms.dao.common.PushMsgDAO;
import com.cmos.pbms.dao.common.WorkTaskDAO;
import com.cmos.pbms.dao.me.*;
import com.cmos.pbms.dao.msg.MsgDao;
import com.cmos.pbms.dao.pm.OrganizationDAO;
import com.cmos.pbms.dao.pm.UserpositionDAO;
import com.cmos.pbms.dao.sys.*;
import com.cmos.pbms.dao.vd.VdMeetingsDAO;
import com.cmos.pbms.iservice.an.AnTaskSV;
import com.cmos.pbms.iservice.me.IMeetingsSV;
import com.cmos.pbms.service.common.ComUtils;
import com.cmos.pbms.utils.*;
import com.cmos.pbms.utils.constants.PbmsConstants;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.helper.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会议服务实现类
 * 使用了Dubbo服务注解,自动注册为Dubbo服务
 *
 * <AUTHOR>
 */
@Service(group = "pbms", retries = -1)
@Slf4j
public class MeetingsSVImpl implements IMeetingsSV {

    private static final Logger logger = LoggerFactory.getLogger(MeetingsSVImpl.class);

    private static final String MEETING_TYPE_UNDERLINE = "01";  //会议类型，线下会议类型

    private static final String MEETING_TYPE_ONLINE = "02"; //会议类型，线上会议类型

    private static final String PROCESS_TYPE_MAIN = "01";   //流程类型，主流程

    private static final String PROCESS_TYPE_DISTRIB = "02";    //流程类型，分发流程

    private static final String HOLD_MEETING = "HOLD_MEETING"; //归档标准字典项类型代码

    private static final String HOLD_MEETING_ONLINE = "ONLINE"; //归档标准，线上会议类型代码

    private static final String HOLD_MEETING_UNDERLINE = "UNDERLINE"; //归档标准，线下会议类型代码

    private static final String DICT_CODE_POST = "PMORGPOST"; // 字典码编号-职务

    private static final Integer NEED_RECORD_YES = 1; // 是否需要参会人员提交会议记录，0:否，1：是
    @Autowired
    private MsgDao msgDao;

    @Autowired
    private PlanTaskDAO planTaskDAO;

    @Autowired
    private MeetingsDAO meetingsDAO;

    @Autowired
    private MeMeetingSignDetailDAO meMeetingSignDetailDAO;

    @Autowired
    private DictionaryItemsDAO dictionaryItemsDAO;

    @Autowired
    private DictionariesDAO dictionariesDAO;

    @Autowired
    private UsersDAO usersDAO;

    @Autowired
    private OrganizationDAO orgDAO;

    @Autowired
    private RoleDAO roleDAO;

    @Autowired
    private AttachmentsDAO attachmentsDAO;

    @Autowired
    private ConventioneerDAO conventioneerDAO;

    @Autowired
    private CheckListDAO checkListDAO;

    @Autowired
    private MeasureDAO measureDAO;

    @Autowired
    private TodoTaskDAO todoTaskDAO;

    @Autowired
    private MeetingRequireDAO meetingRequireDAO;

    @Autowired
    private WorkTaskDAO workTaskDAO;

    @Autowired
    private ReviewLogDAO reviewLogDAO;

    @Autowired
    private MeetingMsgSettingDAO meetingMsgSettingDAO;

    @Autowired
    private ComUtils comUtils;

    @Autowired
    private PushMsgDAO pushMsgDAO;

    @Autowired
    private VdMeetingsDAO vdMeetingsDAO;

    @Autowired
    private MeVoiceTranslationInfoDAO meVoiceTranslationInfoDAO;

    @Autowired
    private UserpositionDAO userpositionDAO;

    @Autowired
    private AbsenceInfoDAO absenceInfoDAO;
    @Resource
    private AnTaskSV taskSV;
    @Resource
    private AttendancesDAO attendancesDAO;

    @Resource
    private AnActivityTaskRelationshipMapper anActivityTaskRelationshipMapper;
    @Override
    public int insertSelective(Meetings meetings) {
        return meetingsDAO.insertSelective(meetings);
    }

    @Override
    public Meetings getByPrimaryKey(String id) {
        return meetingsDAO.selectByPrimaryKey(id);
    }

    @Override
    public Meetings getByPrimaryKeyAndDeleteStatus(String id, Integer isDelete) {
        return meetingsDAO.selectInvalidByPrimaryKey(id, isDelete);
    }

    @Override
    public MeetingsDetailDTO getDetailById(String id, String userId) {
        return meetingsDAO.selectDetailById(id, userId);
    }

    @Override
    public int updateMeetingsByPrimaryKey(Meetings meetings) {
        if ("02".equals(meetings.getChannelType()) && "01".equals(meetings.getDistributionType())) {
            List<String> meetingIds = meetingsDAO.selectMeetingIdsByMainProcessId(meetings.getId());
            Map<String, Object> params = new HashMap<>();
            params.put("meetings", meetings);
            params.put("meetingIds", meetingIds);
            return meetingsDAO.updateByMainProcessSelective(params);
        } else {
            Meetings meetings1=this.meetingsDAO.selectByPrimaryKey(meetings.getId());
            //
            if(StringUtils.isBlank(meetings.getModerator())){
                meetings.setModerator(meetings1.getModerator());
            }
            if(meetings.getActivityCount()==null){
                meetings.setActivityCount(meetings1.getActivityCount());
            }
            if(StringUtils.isBlank(meetings.getAddress())){
                meetings.setAddress(meetings1.getAddress());
            }
            if(StringUtils.isBlank(meetings.getRecordContent())){
                meetings.setRecordContent(meetings1.getRecordContent());
            }
            return meetingsDAO.updateByPrimaryKey(meetings);
        }
    }

    @Override
    public int updateByPrimaryKeySelective(Meetings meetings) {
        Meetings mainMeeting = meetingsDAO.selectByPrimaryKey(meetings.getId());
        if ("02".equals(mainMeeting.getChannelType()) && "01".equals(mainMeeting.getDistributionType())) {
            List<String> meetingIds = meetingsDAO.selectMeetingIdsByMainProcessId(meetings.getId());
            Map<String, Object> params = new HashMap<>();

            //
            Meetings meetings1=this.meetingsDAO.selectByPrimaryKey(meetings.getId());
            //
            if(StringUtils.isBlank(meetings.getModerator())){
                meetings.setModerator(meetings1.getModerator());
            }
            if(meetings.getActivityCount()==null){
                meetings.setActivityCount(meetings1.getActivityCount());
            }
            if(StringUtils.isBlank(meetings.getAddress())){
                meetings.setAddress(meetings1.getAddress());
            }
            if(StringUtils.isBlank(meetings.getRecordContent())){
                meetings.setRecordContent(meetings1.getRecordContent());
            }
            //
            params.put("meetings", meetings);
            params.put("meetingIds", meetingIds);
            //
            params.put("moderator", meetings.getModerator());
            params.put("activityCount", meetings.getActivityCount());
            params.put("address", meetings.getAddress());
            params.put("recordContent", meetings.getRecordContent());
            return meetingsDAO.updateByMainProcessSelective(params);
        } else {
            /**
             *  @Encryption(field = {
             *             "moderator",
             *             "activityCount",
             *             "address",
             *             "recordContent"
             *     })
             */
            Meetings meetings1=this.meetingsDAO.selectByPrimaryKey(meetings.getId());
            if(meetings1!=null){
                if(StringUtils.isBlank(meetings.getModerator())){
                    meetings.setModerator(meetings1.getModerator());
                }
                if(meetings.getActivityCount()==null){
                    meetings.setActivityCount(meetings1.getActivityCount());
                }
                if(StringUtils.isBlank(meetings.getAddress())){
                    meetings.setAddress(meetings1.getAddress());
                }
                if(StringUtils.isBlank(meetings.getRecordContent())){
                    meetings.setRecordContent(meetings1.getRecordContent());
                }
            }
            return meetingsDAO.updateByPrimaryKeySelective(meetings);
        }
    }

    @Override
    public int updateMeetingBasicInfo(String meetingId, VMeetingsBasicUpdateBean basicInfo, String userId, Date modifiedDate) {
        // 参数校验
        if (StringUtils.isBlank(meetingId) || basicInfo == null) {
            throw new IllegalArgumentException("会议ID和基本信息不能为空");
        }

        // 查询现有会议信息
        Meetings existingMeeting = meetingsDAO.selectByPrimaryKey(meetingId);
        if (existingMeeting == null) {
            throw new IllegalArgumentException("未找到指定的会议信息");
        }

        // 创建更新对象，只更新基本信息字段
        Meetings updateMeeting = new Meetings();
        updateMeeting.setId(meetingId);
        updateMeeting.setModifiedby(userId);
        updateMeeting.setModifieddate(modifiedDate);

        // 设置基本信息字段（只有非空值才更新）
        if (StringUtils.isNotBlank(basicInfo.getTopic())) {
            updateMeeting.setTopic(basicInfo.getTopic());
        }
        if (StringUtils.isNotBlank(basicInfo.getTheme())) {
            updateMeeting.setTheme(basicInfo.getTheme());
        }
        if (StringUtils.isNotBlank(basicInfo.getStarttime())) {
            updateMeeting.setStarttime(ConvertUtil.convertStringToDate(basicInfo.getStarttime()));
        }
        if (StringUtils.isNotBlank(basicInfo.getEndtime())) {
            updateMeeting.setEndtime(ConvertUtil.convertStringToDate(basicInfo.getEndtime()));
        }
        if (StringUtils.isNotBlank(basicInfo.getAddress())) {
            updateMeeting.setAddress(basicInfo.getAddress());
        }
        if (basicInfo.getSigntime() != null) {
            updateMeeting.setSigntime(basicInfo.getSigntime());
        }
        if (StringUtils.isNotBlank(basicInfo.getRecorder())) {
            updateMeeting.setRecorder(basicInfo.getRecorder());
        }
        if (StringUtils.isNotBlank(basicInfo.getTeacher())) {
            updateMeeting.setTeacher(basicInfo.getTeacher());
        }
        if (StringUtils.isNotBlank(basicInfo.getDescription())) {
            updateMeeting.setDescription(basicInfo.getDescription());
        }
        if (StringUtils.isNotBlank(basicInfo.getPrecautions())) {
            updateMeeting.setPrecautions(basicInfo.getPrecautions());
        }
        if (basicInfo.getNeedRecord() != null) {
            updateMeeting.setNeedRecord(basicInfo.getNeedRecord());
        }
        if (StringUtils.isNotBlank(basicInfo.getChannelType())) {
            updateMeeting.setChannelType(basicInfo.getChannelType());
        }
        if (StringUtils.isNotBlank(basicInfo.getRangeType())) {
            updateMeeting.setRangeType(basicInfo.getRangeType());
        }
        if (StringUtils.isNotBlank(basicInfo.getDistributionType())) {
            updateMeeting.setDistributionType(basicInfo.getDistributionType());
        }
        if (StringUtils.isNotBlank(basicInfo.getRoomId())) {
            updateMeeting.setRoomId(basicInfo.getRoomId());
        }
        if (basicInfo.getActualStartTime() != null) {
            updateMeeting.setActualStartTime(basicInfo.getActualStartTime());
        }
        if (basicInfo.getActualEndTime() != null) {
            updateMeeting.setActualEndTime(basicInfo.getActualEndTime());
        }
        if (StringUtils.isNotBlank(basicInfo.getProcessIds())) {
            updateMeeting.setProcessIds(basicInfo.getProcessIds());
        }
        if (StringUtils.isNotBlank(basicInfo.getTimelinessType())) {
            updateMeeting.setTimelinessType(basicInfo.getTimelinessType());
        }
        if (StringUtils.isNotBlank(basicInfo.getMeHost())) {
            updateMeeting.setMeHost(basicInfo.getMeHost());
        }
        if (StringUtils.isNotBlank(basicInfo.getMeHostId())) {
            updateMeeting.setMeHostId(basicInfo.getMeHostId());
        }
        if (StringUtils.isNotBlank(basicInfo.getMeContent())) {
            updateMeeting.setMeContent(basicInfo.getMeContent());
        }
        if (StringUtils.isNotBlank(basicInfo.getActivityYear())) {
            updateMeeting.setActivityYear(basicInfo.getActivityYear());
        }
        if (basicInfo.getActivityCount() != null) {
            updateMeeting.setActivityCount(basicInfo.getActivityCount());
        }
        if (StringUtils.isNotBlank(basicInfo.getStudyType())) {
            updateMeeting.setExtfld2(basicInfo.getStudyType());
        }
        if (StringUtils.isNotBlank(basicInfo.getBranchOrgId())) {
            updateMeeting.setBranchOrgId(basicInfo.getBranchOrgId());
        }

        // 处理关联任务
        if (StringUtils.isNotBlank(basicInfo.getTaskIds())) {
            List<String> taskIds = Arrays.asList(basicInfo.getTaskIds().split(","));
            relatedTasksSaveAndUpdate(taskIds, meetingId);
        }

        // 保持加密字段的完整性
        if (StringUtils.isBlank(updateMeeting.getModerator())) {
            updateMeeting.setModerator(existingMeeting.getModerator());
        }
        if (updateMeeting.getActivityCount() == null) {
            updateMeeting.setActivityCount(existingMeeting.getActivityCount());
        }
        if (StringUtils.isBlank(updateMeeting.getAddress())) {
            updateMeeting.setAddress(existingMeeting.getAddress());
        }
        if (StringUtils.isBlank(updateMeeting.getRecordContent())) {
            updateMeeting.setRecordContent(existingMeeting.getRecordContent());
        }

        // 执行更新操作
        return meetingsDAO.updateByPrimaryKeySelective(updateMeeting);
    }

    @Override
    public List<String> selectMeetingIdsByMainProcessId(String mainProcessId) {
        return meetingsDAO.selectMeetingIdsByMainProcessId(mainProcessId);
    }

    @Override
    public int cancelMeetingsByPrimaryKey(String id, String reason, String userId, Date modifieddate) {
        Meetings meetings = meetingsDAO.selectByPrimaryKey(id);
        Map<String, Object> params = new HashMap<>(16);
        params.put("reason", reason);
        params.put("modifiedby", userId);
        params.put("modifieddate", modifieddate);
        if ("02".equals(meetings.getChannelType()) && "01".equals(meetings.getDistributionType())) {
            List<String> meetingIds = meetingsDAO.selectMeetingIdsByMainProcessId(meetings.getId());
            params.put("meetingIds", meetingIds);
            return meetingsDAO.cancelByProcessIds(params);
        } else {
            //计划内会议取消后，重新生成逻辑（ 2019 03 11 需求要求不再生成）
//            if (null != meetings.getIsplan() && Short.valueOf("1").equals(meetings.getIsplan())) {
//                PlanTask planTask = planTaskDAO.selectByObjidAndType(meetings.getId(), meetings.getType());
//                if (null != planTask) {
//
//                    PlanDetail planDetail = planDetailDAO.selectByPrimaryKey(planTask.getDetailid());
//                    if (null != planDetail) {
//                        //会议计划状态重置
//                        planDetail.setIsexeced(Short.valueOf("0"));
//                        planDetail.setModifiedby(PbmsConstants.CANCELMEETING_USERID);
//                        planDetail.setModifieddate(modifieddate);
//                        planDetailDAO.updateByPrimaryKey(planDetail);
//                    }
//                    //会议任务状态重置
//                    planTask.setIsdeleted(Integer.valueOf(1));
//                    planTaskDAO.updateByPrimaryKey(planTask);
//                }
//            }
            params.put("id", id);
            return meetingsDAO.cancelByPrimaryKey(params);
        }
    }

    @Override
    public String turnMeetingToConvening(String sn) {
        return MeetingUtils.updateMeetingToConvening(sn).toString();
    }

    /**
     * 获取当前季度月份
     *
     * @return
     */
    private List<Integer> getCurrentQuarterMonthList() {
        Calendar currentTime = Calendar.getInstance();
        int currentMonth = currentTime.get(Calendar.MONTH) + 1;

        List<Integer> quarterMonthList;
        if (currentMonth == 1 || currentMonth == 2 || currentMonth == 3) {
            quarterMonthList = new ArrayList<>(Arrays.asList(1, 2, 3));
        } else if (currentMonth == 4 || currentMonth == 5 || currentMonth == 6) {
            quarterMonthList = new ArrayList<>(Arrays.asList(4, 5, 6));
        } else if (currentMonth == 7 || currentMonth == 8 || currentMonth == 9) {
            quarterMonthList = new ArrayList<>(Arrays.asList(7, 8, 9));
        } else {
            quarterMonthList = new ArrayList<>(Arrays.asList(10, 11, 12));
        }

        return quarterMonthList;
    }

    @Override
    public boolean isCanRemoveError(Meetings meeting) {
        boolean flag = false;
        Calendar currentTime = Calendar.getInstance();
        int currentYear = currentTime.get(Calendar.YEAR);
        int currentDay = currentTime.get(Calendar.DAY_OF_MONTH);

        // 判断会议类型是季度性（支部党员大会/党课）还是月度性（支部委员会/党小组会）
        if (meeting.getType().equals(MeetingTypeEnum.BRANCHMASSES.getCode())
                || meeting.getType().equals(MeetingTypeEnum.NEWSTUDY.getCode())) {
            List<Integer> quarterMonthArray = getCurrentQuarterMonthList();

            // 获取季度未完成检查日期配置
            DictionaryItems dictionaryItem = dictionaryItemsDAO.selectByCodeAndDictCode(MeetingCheckTypeEnum.QUARTERLY_CHECKUNFINISHED.getItemcode(), MeetingCheckTypeEnum.QUARTERLY_CHECKUNFINISHED.getDictcode());

            // 对字典值进行解析处理
            String itemDescription = dictionaryItem.getDescription();
            String[] configArray = itemDescription.split("\\|");
            if (configArray.length > 0) {
                List<Date> checkDayList = new ArrayList<>();

                for (String config : configArray) {
                    if (config.indexOf("~") != -1) {
                        // 分割字符串
                        String[] dateArray = config.split("~");
                        String[] beginTimeArray = dateArray[0].split("\\.");

                        if (quarterMonthArray.contains(Integer.parseInt(beginTimeArray[0]))) {
                            Calendar beginTime = Calendar.getInstance();
                            beginTime.set(currentYear, Integer.parseInt(beginTimeArray[0]) - 1, Integer.parseInt(beginTimeArray[1]));
                            checkDayList.add(beginTime.getTime());
                        }
                    } else {
                        String[] timeArray = config.split("\\.");
                        if (quarterMonthArray.contains(Integer.parseInt(timeArray[0]))) {
                            Calendar configTime = Calendar.getInstance();
                            configTime.set(currentYear, Integer.parseInt(timeArray[0]) - 1, Integer.parseInt(timeArray[1]));
                            configTime.set(Calendar.HOUR_OF_DAY, 0);
                            configTime.set(Calendar.MINUTE, 0);
                            configTime.set(Calendar.SECOND, 0);
                            configTime.set(Calendar.MILLISECOND, 0);
                            checkDayList.add(configTime.getTime());
                        }
                    }
                }
                Date checkUnfinishDay = Collections.min(checkDayList);
                // 将时分秒,毫秒域清零
                currentTime.set(Calendar.HOUR_OF_DAY, 0);
                currentTime.set(Calendar.HOUR, 0);
                currentTime.set(Calendar.MINUTE, 0);
                currentTime.set(Calendar.SECOND, 0);
                currentTime.set(Calendar.MILLISECOND, 0);

                if (currentTime.getTime().compareTo(checkUnfinishDay) < 0) {
                    // 当前日期若小于“未完成检查日期”，若会议状态为“待计划/待通知”则不能解除，其他则可以解除
                    flag = !(meeting.getStatus().equals(MeetingStatusEnum.TOBEPLAN.getCode())
                            || meeting.getStatus().equals(MeetingStatusEnum.TOBENOTIFY.getCode()));
                } else {
                    // 当前日期若大于“未完成检查日期”，若会议状态为“已完成”则可以解除，其他则不可解除
                    flag = meeting.getStatus().equals(MeetingStatusEnum.FINISHED.getCode());
                }
            }
        } else if (meeting.getType().equals(MeetingTypeEnum.BRANCHLEADER.getCode())
                || meeting.getType().equals(MeetingTypeEnum.PARTYGROUP.getCode())) {
            // 获取月度未完成检查日期配置
            DictionaryItems dictionaryItem = dictionaryItemsDAO.selectByCodeAndDictCode(MeetingCheckTypeEnum.MONTHLY_CHECKUNFINISHED.getItemcode(), MeetingCheckTypeEnum.MONTHLY_CHECKUNFINISHED.getDictcode());
            String itemDescription = dictionaryItem.getDescription();
            String[] configArray = itemDescription.split("\\|");
            if (configArray.length > 0) {
                List<Integer> checkDayList = new ArrayList<>();

                for (String config : configArray) {
                    if (config.indexOf("~") != -1) {
                        // 分割字符串
                        String[] dayArray = config.split("~");
                        checkDayList.add(Integer.parseInt(dayArray[0]));
                    } else {
                        checkDayList.add(Integer.parseInt(config));
                    }
                }
                Integer checkUnfinishDay = Collections.min(checkDayList);

                if (currentDay < checkUnfinishDay) {
                    // 当前日期若小于“未完成检查日期”，若会议状态为“待计划/待通知”则不能解除，其他则可以解除
                    flag = !(meeting.getStatus().equals(MeetingStatusEnum.TOBEPLAN.getCode())
                            || meeting.getStatus().equals(MeetingStatusEnum.TOBENOTIFY.getCode()));
                } else {
                    // 当前日期若大于“未完成检查日期”，若会议状态为“已完成”则可以解除，其他则不可解除
                    flag = meeting.getStatus().equals(MeetingStatusEnum.FINISHED.getCode());
                }
            }
        }

        return flag;
    }

    @Override
    public MeetingSumInfoDTO getMeetingSumInfo(String statusSet, String typeSet) {
        return meetingsDAO.selectTaskAndErrorInfo(statusSet, typeSet);
    }

    @Override
    public MeetingsDetailDTO getFullDetailById(String id, Users user) {
        // 会议信息
        MeetingsDetailDTO meetDTO = meetingsDAO.selectDetailById(id, user.getId());
        if (meetDTO == null) {
            return null;
        }
        if (StringUtils.isNotBlank(meetDTO.getMeHostId())) {
            meetDTO.setMeHostObj(usersDAO.selectByPrimaryKey(meetDTO.getMeHostId()));
        } else {
            //设置默认主持人为党组织书记 或党小组长
            String postCode = "DANGWEISHUJI";
            if (meetDTO.getType() == MeetingTypeEnum.PARTYGROUP.getCode()) {
                postCode = "DANGXIAOZUZHANG";
            }
            DictionaryItems dict = dictionaryItemsDAO.selectByCodeAndDictCode(postCode, "PMORGPOST");
            List<Userposition> userpositionList = userpositionDAO.selectByOrgIdAndPostId(meetDTO.getOrgid(), dict.getId());

            if (!userpositionList.isEmpty()) {
                meetDTO.setMeHostObj(usersDAO.selectByPrimaryKey(userpositionList.get(0).getUserid()));
            }
        }
//        if (StringUtils.isNotEmpty(meetDTO.getMeContent())) {
//            String content = OnestUtil.getContent(meetDTO.getMeContent());
//            meetDTO.setMeContent(content);
//        }
        //设置集中学习类型
        if (Objects.equals(meetDTO.getType(), MeetingTypeEnum.STUDY.getCode())){
            meetDTO.setStudyType(meetDTO.getExtfld2());
        }
        //设置预计人数
        meetDTO.setExpectedNum(meetDTO.getExtfld3());
        // 设置负责人/组织者标识
        setModeratorAndPlannerFlag(user, meetDTO);

        //获取所属党组织 党支部
        Optional.ofNullable(meetDTO.getBranchOrgId())
                .map(orgDAO::selectByPrimaryKey)
                .map(Organization::getOrgName)
                .ifPresent(meetDTO::setBranchOrgName);
        // 取操作者所在党支部
        meetDTO.setOrganization(getOrganization(user.getOrgid()));

        //短信提醒默认设置，默认查询线下会议设置

        String msgDefaultItem = "MEETING_MSG_DEFAULT_RULE_UNLINE";
        String searchID = id;
        if (meetDTO.getType() == MeetingTypeEnum.STUDY.getCode()) {
            msgDefaultItem = "MEETING_MSG_DEFAULT_RULE_STUDY";
        } else {
            //判断会议的展开形式是否为线上直播
            if (MEETING_TYPE_ONLINE.equals(meetDTO.getChannelType())) {
                searchID = meetDTO.getMainProcessId();
                //修改短信提醒设置为线上直播的设置
                msgDefaultItem = "MEETING_MSG_DEFAULT_RULE_ONLINE";
            }
        }

        // 附件信息 活动方案
        List<Attachments> attachmentsList = attachmentsDAO.selectListByObjIdType(searchID, AttachTypeEnum.ME_MEETING.getType());
        meetDTO.setAttachmentsList(attachmentsList);

        // 短信息提醒设置
        String mainId;
        if (MEETING_TYPE_ONLINE.equals(meetDTO.getChannelType()) && PROCESS_TYPE_DISTRIB.equals(meetDTO.getDistributionType())) {
            mainId = meetDTO.getMainProcessId();
        } else {
            mainId = meetDTO.getId();
        }
        meetDTO.setMeetingMsgSetting(meetingMsgSettingDAO.selectByObjTypeAndObjId("01", mainId));
        // 短信息提醒默认设置
        List<SelectBean> dictItemList = dictionaryItemsDAO.selectByDictCode(msgDefaultItem);
        Map<String, Object> itemValueMap = new HashMap<>(16);
        for (SelectBean temp : dictItemList) {
            itemValueMap.put(temp.getValue(), temp.getName());
        }
        meetDTO.setDefaultMsgSetting(itemValueMap);

        // 参与情况
        Map<String, Object> conventParam = new HashedMap();
        if (MEETING_TYPE_ONLINE.equals(meetDTO.getChannelType()) && user.getId().equals(meetDTO.getModerator())) {
            List<String> meetingIds = meetingsDAO.selectMeetingIdsByMainProcessId(meetDTO.getMainProcessId());
            conventParam.put("meetingids", meetingIds);
            meetDTO.setSelectTreeDTOList(orgDAO.selectOrgByMainProcessId(id));
        } else {
            conventParam.put("meetingid", id);
        }
        List<Conventioneer> conventioneerList = conventioneerDAO.selectByMeetingid(conventParam);
        // 与会者分组
        List<Conventioneer> attendanceList = new ArrayList<>();
        List<Conventioneer> absenceList = new ArrayList<>();
        List<Conventioneer> unResponseList = new ArrayList<>();
        for (Conventioneer temp : conventioneerList) {
            if (temp.getIsjoin().equals(MeetingJoinStatusEnum.ATTENDANCE.getCode())) {
                attendanceList.add(temp);
            } else if (temp.getIsjoin().equals(MeetingJoinStatusEnum.ABSENCE.getCode())) {
                absenceList.add(temp);
            } else {
                unResponseList.add(temp);
            }
        }

        Map<String, List<Conventioneer>> convertioneerMap = new HashMap<>();
        convertioneerMap.put("attendanceList", attendanceList);
        convertioneerMap.put("absenceList", absenceList);
        convertioneerMap.put("unResponseList", unResponseList);
        meetDTO.setConvertioneerMap(convertioneerMap);
        //把数量设置进去
        //实到人数 = 参加人数 - （请假+未参加）人数
        int attendanceNum = Integer.parseInt(meetDTO.getExtfld3()) - (absenceList.size() + unResponseList.size());
        meetDTO.setAttendanceNum(attendanceNum);
        meetDTO.setAbsenceNum(absenceList.size());
        meetDTO.setUnResponseNum(unResponseList.size());
        // 会议负责人
        if (!StringUtils.isBlank(meetDTO.getModerator())) {
            Users charger = usersDAO.selectByPrimaryKey(meetDTO.getModerator());
            meetDTO.setCharger(charger);
        }

        // 工作事项
        meetDTO.setChecklistList(checkListDAO.selectCheckInfoByMeetingAndAuthor(searchID, user.getId()));

        // 会议要求
        meetDTO.setMeetingRequireList(meetingRequireDAO.selectByObjId(searchID));

        // 决议意见&方案
        PageHelper.startPage(SimpleDataEnum.PAGENUM.getCode(), SimpleDataEnum.PAGESIZE.getCode());
        List<Measure> measureListInfo = measureDAO.selectMeasureListByMeetingId(searchID);
        meetDTO.setMeasureListInfo(measureListInfo);

        // web端与会人员列表 单独调接口查询

        // 主题图
        meetDTO.setThemepicAttachement(attachmentsDAO.selectListByObjIdType(searchID, AttachTypeEnum.ME_THEMEPIC.getType()));
        // 签到表
        meetDTO.setSignAttachment(attachmentsDAO.selectListByObjIdType(searchID, AttachTypeEnum.ME_SIGN.getType()));
        // 会议照片
        meetDTO.setPictureAttachment(attachmentsDAO.selectListByObjIdType(searchID, AttachTypeEnum.ME_PICTURE.getType()));
        // 会议纪要
        meetDTO.setMinutesAttachment(attachmentsDAO.selectListByObjIdType(searchID, AttachTypeEnum.ME_MINUTES.getType()));
        // 活动材料
        meetDTO.setActivityAttachments(attachmentsDAO.selectListByObjIdType(searchID,AttachTypeEnum.ME_ACTIVITY.getType()));
        // oa回传文件
        meetDTO.setOaReturnFiles(attachmentsDAO.selectListByObjIdType(searchID,AttachTypeEnum.ME_MEETING_OA.getType()));
        // 落实情况
        Map<String, Object> params = new HashMap<>(16);
        params.put("meetingid", id);
        PageHelper.startPage(SimpleDataEnum.PAGENUM.getCode(), SimpleDataEnum.PAGESIZE.getCode());
        meetDTO.setTodoTaskList(todoTaskDAO.selectTodoTaskListByParams(params));

        {
            // 记录者
            String recorderName = "";
            if (!StringUtils.isBlank(meetDTO.getRecorder())) {
                Users recorder = usersDAO.selectByPrimaryKey(meetDTO.getRecorder());
                if (null == recorder) {
                    return null;
                }
                recorderName = recorder.getUsername();
                meetDTO.setRecorderObj(recorder);
            }
            meetDTO.setRecorderName(recorderName);
        }

        if (meetDTO.getType() == MeetingTypeEnum.BRANCHLEADER.getCode()) { // 支部委员会相关处理
            // 委员会成员，排除记录员
            StringBuilder members = new StringBuilder();
            for (int i = 0; i < conventioneerList.size(); i++) {
                if (conventioneerList.get(i).getId().equals(meetDTO.getRecorder())) {
                    continue;
                }
                members.append(conventioneerList.get(i).getUsername());
                if (i != (conventioneerList.size() - 1)) {
                    members.append("、");
                }
            }
            meetDTO.setCommitteeMember(members.toString());
        } else if (meetDTO.getType() == MeetingTypeEnum.PARTYGROUP.getCode()) { // 党小组会相关处理
            // 小组参会者
            StringBuilder members = new StringBuilder();
            for (int i = 0; i < conventioneerList.size(); i++) {
                members.append(conventioneerList.get(i).getUsername());
                if (i != (conventioneerList.size() - 1)) {
                    members.append("、");
                }
            }
            meetDTO.setGroupMember(members.toString());
        }

        // 会议审核情况
        List<ReviewLog> logList = reviewLogDAO.selectByTypeAndObjId(ProcessTypeEnum.MINUTES_REVIEW.getCode(), meetDTO.getId());
        if (CollectionUtils.isNotEmpty(logList)) {
            for (ReviewLog temp : logList) {
                if (1 == temp.getOperationType()) {
                    temp.setOperationDetail("【" + temp.getOperationUserName() + "】提交会议记要至【" + (30 == meetDTO.getType() ? "党支部党务工作者角色" : "党支部领导角色") + "】进行审核");
                }
                if (2 == temp.getOperationType()) {
                    temp.setOperationDetail("【" + temp.getOperationUserName() + "】审核通过，审核完成");
                }
                if (3 == temp.getOperationType()) {
                    temp.setOperationDetail("【" + temp.getOperationUserName() + "】审核未通过，需重新调整；审核未通过原因：" + temp.getResuseReason());
                }
            }
        }
        meetDTO.setReviewLogList(logList);
        // 会议签名审核情况
        meetDTO.setReviewLogList_sign(reviewLogDAO.selectByTypeAndObjId(ProcessTypeEnum.ME_SIGN.getCode(), meetDTO.getId()));

        //前端改为调用日志流转信息公共接口
//        meetDTO.setAuditReviewLogList(reviewLogDAO.selectByTypeAndObjId(ProcessTypeEnum.ME_AUDIT.getCode(), meetDTO.getId()));

        // 查询关联的视频会议
        VdMeetings vdMeetings = vdMeetingsDAO.selectByMeetingId(id);
        if (null != vdMeetings) {
            meetDTO.setVdMeetingId(vdMeetings.getId());
            meetDTO.setVdMeetingTopic(vdMeetings.getTopic());
            meetDTO.setVdMeetingVideoId(vdMeetings.getVideoId());
            meetDTO.setVdMeetingVideoUrl(vdMeetings.getVideoUrl());
        }

        //会议类型
        meetDTO.setTypeValue(Arrays.stream(MeetingTypeEnum.values())
                .filter(item -> item.getCode().equals(meetDTO.getType()))
                .findFirst()
                .map(MeetingTypeEnum::getDesc)
                .orElse(null));
        meetDTO.setMeVoiceTranslationInfos(meVoiceTranslationInfoDAO.selectByObjId(meetDTO.getId()));
        meetDTO.setSignDetail(meMeetingSignDetailDAO.selectSignDetailDTOByMeetingId(meetDTO.getId()));
        return meetDTO;
    }

    /**
     * 设置负责人/组织者标识
     *
     * @param user    当前用户
     * @param meetDTO 会议对象
     */
    private void setModeratorAndPlannerFlag(Users user, MeetingsDetailDTO meetDTO) {
        if (user.getId().equals(meetDTO.getModerator())) {
            meetDTO.setIsModerator(1);
            meetDTO.setIsPlanner(0);
        } else {
            meetDTO.setIsModerator(0);
            List<String> leaderIdList = usersDAO.selectLeaderIdsByOrgid(meetDTO.getOrgid());
            Integer isPlanner = 0;
            for (String temp : leaderIdList) {
                if (temp.equals(user.getId())) {
                    isPlanner = 1;
                    break;
                }
            }
            meetDTO.setIsPlanner(isPlanner);
        }
    }

    /**
     * 取操作者所在党支部
     *
     * @param orgId
     * @return
     */
    private Organization getOrganization(String orgId) {
        Organization organization = orgDAO.selectByPrimaryKey(orgId);
        if (organization.getIsgroup() == SimpleDataEnum.ISGROUPYES.getCode()) {// 依据：基层组织只有党小组和党支部，如果当前机构不是党支部，则上一级必是
            organization = orgDAO.selectByPrimaryKey(organization.getParentid());
        }
        return organization;
    }

    @Override
    public PageInfo<MeetingsDTO> selectListByParams(VMeetingsSummaryListSearchBean vMeetingsSummaryListSearchBean, String sortRule) {
        PageHelper.startPage(vMeetingsSummaryListSearchBean.getPage(), vMeetingsSummaryListSearchBean.getLimit());

        Map<String, Object> params = new HashMap<>(16);
        params.put("type", vMeetingsSummaryListSearchBean.getType());
        params.put("starttime", vMeetingsSummaryListSearchBean.getStarttime());
        params.put("endtime", vMeetingsSummaryListSearchBean.getEndtime());
        params.put("orgname", vMeetingsSummaryListSearchBean.getOrgname());
        params.put("topic", vMeetingsSummaryListSearchBean.getTopic());
        params.put("isDeleted", vMeetingsSummaryListSearchBean.getIsDeleted());
        params.put("status", vMeetingsSummaryListSearchBean.getStatus());
        params.put("auditState", vMeetingsSummaryListSearchBean.getAuditState());
        params.put("iserror", vMeetingsSummaryListSearchBean.getIserror());
        params.put("sortRule", sortRule);
        if (StringUtils.isNotBlank(vMeetingsSummaryListSearchBean.getJoiner())) params.put("joiner", vMeetingsSummaryListSearchBean.getJoiner());
        if (StringUtils.isNotBlank(vMeetingsSummaryListSearchBean.getModerator())) params.put("moderator", vMeetingsSummaryListSearchBean.getModerator());
        if (StringUtils.isNotBlank(vMeetingsSummaryListSearchBean.getMeHost())) params.put("meHost", vMeetingsSummaryListSearchBean.getMeHost());
        if (StringUtils.isNotBlank(vMeetingsSummaryListSearchBean.getTeacher())) params.put("teacher", vMeetingsSummaryListSearchBean.getTeacher());
        if (null != vMeetingsSummaryListSearchBean.getIsplan() && vMeetingsSummaryListSearchBean.getIsplan() > -1) {
            params.put("isplan", vMeetingsSummaryListSearchBean.getIsplan());
        }
        if (null != vMeetingsSummaryListSearchBean.getOrgCodeTerm())
            params.put("orgCodeTerm", vMeetingsSummaryListSearchBean.getOrgCodeTerm());
        //数据规则
        if (null != vMeetingsSummaryListSearchBean.getCurrentOrgCode())
            params.put("currentOrgCode", vMeetingsSummaryListSearchBean.getCurrentOrgCode());

        List<MeetingsDTO> result = meetingsDAO.selectListByParams(params);
        return new PageInfo<>(result);
    }

    @Override
    public List<MeStistDTO> selectListByParams(VMeetingsSummaryListSearchBean vMeetingsSummaryListSearchBean) {
        Map<String, Object> params = new HashMap<>(16);
        params.put("type", vMeetingsSummaryListSearchBean.getType());
        params.put("starttime", vMeetingsSummaryListSearchBean.getStarttime());
        params.put("endtime", vMeetingsSummaryListSearchBean.getEndtime());
        params.put("orgname", vMeetingsSummaryListSearchBean.getOrgname());
        params.put("topic", vMeetingsSummaryListSearchBean.getTopic());
        params.put("isDeleted", vMeetingsSummaryListSearchBean.getIsDeleted());
        params.put("status", vMeetingsSummaryListSearchBean.getStatus());
        params.put("auditState", vMeetingsSummaryListSearchBean.getAuditState());
        params.put("iserror", vMeetingsSummaryListSearchBean.getIserror());
        if (StringUtils.isNotBlank(vMeetingsSummaryListSearchBean.getJoiner())) params.put("joiner", vMeetingsSummaryListSearchBean.getJoiner());
        if (StringUtils.isNotBlank(vMeetingsSummaryListSearchBean.getModerator())) params.put("moderator", vMeetingsSummaryListSearchBean.getModerator());
        if (StringUtils.isNotBlank(vMeetingsSummaryListSearchBean.getMeHost())) params.put("meHost", vMeetingsSummaryListSearchBean.getMeHost());
        if (StringUtils.isNotBlank(vMeetingsSummaryListSearchBean.getTeacher())) params.put("teacher", vMeetingsSummaryListSearchBean.getTeacher());
        if (null != vMeetingsSummaryListSearchBean.getIsplan() && vMeetingsSummaryListSearchBean.getIsplan() > -1) {
            params.put("isplan", vMeetingsSummaryListSearchBean.getIsplan());
        }
        if (null != vMeetingsSummaryListSearchBean.getOrgCodeTerm())
            params.put("orgCodeTerm", vMeetingsSummaryListSearchBean.getOrgCodeTerm());
        //数据规则
        if (null != vMeetingsSummaryListSearchBean.getCurrentOrgCode())
            params.put("currentOrgCode", vMeetingsSummaryListSearchBean.getCurrentOrgCode());

        return meetingsDAO.selectMeetingStatistics(params);
    }

    @Override
    public MeMinutesDetailDTO getMinutesDetail(String meetingId) {
        // 会议信息
        Meetings meetings = meetingsDAO.selectByPrimaryKey(meetingId);
        if (null == meetings) {
            return null;
        }

        MeMinutesDetailDTO minutesDetail = ConvertUtil.beanToBean(meetings, MeMinutesDetailDTO.class);

        // 根据会议记录/心得体会文件地址读取内容
        if (!StringUtil.isBlank(meetings.getMinutesContent())) {
            String content = OnestUtil.getContent(meetings.getMinutesContent());
            minutesDetail.setContent(content);
        }

        // 参与情况
        Map<String, Object> conventParam = new HashedMap();
        conventParam.put("meetingid", meetingId);
        List<Conventioneer> conventioneerList = conventioneerDAO.selectByMeetingid(conventParam);
        // 与会者分组
        StringBuilder attends = new StringBuilder();
        StringBuilder absents = new StringBuilder();
        Integer attendNum = 0;
        Integer totalNum = 0;
        for (Conventioneer temp : conventioneerList) {
            if (temp.getIsjoin().equals(MeetingJoinStatusEnum.ATTENDANCE.getCode())) {
                attends.append(temp.getUsername() + ";");
                attendNum++;
            } else {
                absents.append(temp.getUsername().concat(":").concat(StringUtils.isNotBlank(temp.getReason()) ? temp.getReason() : "").concat(";"));
            }
            totalNum++;
        }
        minutesDetail.setAttends(attends.toString());
        minutesDetail.setAbsents(absents.toString());
        minutesDetail.setAttendNum(attendNum);
        minutesDetail.setTotalNum(totalNum);

        // 会议负责人
        if (!StringUtils.isBlank(meetings.getModerator())) {
            Users charger = usersDAO.selectByPrimaryKey(meetings.getModerator());
            minutesDetail.setCharger(charger);
        }

        // 取会议党支部
        minutesDetail.setOrganization(orgDAO.selectByPrimaryKey(meetings.getOrgid()));

        // 会议纪要附件
        minutesDetail.setMinutesAttachment(attachmentsDAO.selectListByObjIdType(meetingId, AttachTypeEnum.ME_MINUTES.getType()));

        if (meetings.getType() == MeetingTypeEnum.BRANCHLEADER.getCode()) { // 支部委员会相关处理
            // 记录者
            if (!StringUtils.isBlank(meetings.getRecorder())) {
                Users recorder = usersDAO.selectByPrimaryKey(meetings.getRecorder());
                if (null == recorder) {
                    return null;
                }
                minutesDetail.setRecorderName(recorder.getUsername());
            }

        }

        return minutesDetail;
    }

    @Override
    public PageInfo<Meetings> getMinutesLis(int pageNum, int pagesize, Map<String, Object> paramMap) {
        PageHelper.startPage(pageNum, pagesize);
//        List<Meetings> result = meetingsDAO.selectMinutesList(paramMap);
//        List<Meetings> result = meetingsDAO.selectMinutesListByMenuAuth(paramMap);
        List<Meetings> result = meetingsDAO.selectMinutesTask(paramMap);
        return new PageInfo<>(result);
    }

    @Override
    public void deleteCustomMeeting(String meetingId) {
        Meetings meetings = meetingsDAO.selectByPrimaryKey(meetingId);
        List<String> meetingIds = meetingsDAO.selectMeetingIdsByMainProcessId(meetings.getId());

        //查询会议检查项表是否有需要删除的数据，如果有就进行逻辑删除
        List<String> checkLists = checkListDAO.selectIDsByMeetingId(meetingId);
        if (ValidateUtil.isNotValid(checkLists)) checkListDAO.deleteByMeetingId(checkLists, meetingId);
        //检查与会者表是否有需要删除的数据，如果有就进行逻辑删除
        List<String> conventioneers = conventioneerDAO.selectIDsByMeetingId(meetingId);
        if (ValidateUtil.isNotValid(conventioneers)) conventioneerDAO.deleteByMeetingId(conventioneers, meetingId);
        //检查会议议案表是否有需要删除的数据，如果有就进行逻辑删除
        List<String> measures = measureDAO.selectIDsByMeetingId(meetingId);
        if (ValidateUtil.isNotValid(measures)) measureDAO.deleteByMeetingId(measures, meetingId);
        //检查落实情况表是否有需要删除的数据，如果有就进行逻辑删除
        List<String> todoTasks = todoTaskDAO.selectIDsByMeetingId(meetingId);
        if (ValidateUtil.isNotValid(todoTasks)) todoTaskDAO.deleteByMeetingId(todoTasks, meetingId);
        //检查会议要求表是否有需要删除的数据，如果有就进行逻辑删除
        List<String> meetingRequires = meetingRequireDAO.selectIDsByMeetingId(meetingId);
        if (ValidateUtil.isNotValid(meetingRequires)) meetingRequireDAO.deleteByMeetingId(meetingRequires, meetingId);
        // 删除会议相关请假流程和待办
        absenceInfoDAO.deleteTaskByObjId(meetingId);
        absenceInfoDAO.deleteByObjId(meetingId);
        //检查待办表是否有需要删除的数据，如果有就进行逻辑删除
        List<String> workTasks;
        if (MEETING_TYPE_ONLINE.equals(meetings.getChannelType()) && PROCESS_TYPE_MAIN.equals(meetings.getDistributionType())) {
            workTasks = workTaskDAO.selectIDsByObjIdCollect(meetingIds);
        } else {
            workTasks = workTaskDAO.selectIDsByObjId(meetingId);
        }
        if (ValidateUtil.isNotValid(workTasks)) workTaskDAO.deleteByObjId(workTasks, meetingId);

        //逻辑删除会议
        if (MEETING_TYPE_ONLINE.equals(meetings.getChannelType()) && PROCESS_TYPE_MAIN.equals(meetings.getDistributionType())) {
            meetingsDAO.deleteCustomMeetingByProcessIdsList(meetingIds);
        } else {
            meetingsDAO.deleteCustomMeeting(meetingId);
        }
    }

    @Override
    @Transactional
    public int recoveryMeetingForDelete(String meetingId, String currUserId, Date currTime) throws SystemFailureException {
        // 查询会议数据
        Meetings meetings = meetingsDAO.selectInvalidByPrimaryKey(meetingId, 1);
        // 有效性校验
        if (DataIsDeleteEnum.NORMAL.getCode().equals(meetings.getIsdeleted())) {
            logger.error("数据状态错误");
            throw new SystemFailureException("数据状态错误");
        }
        try {
            // 议检查项表
            checkListDAO.recoveryByMeetingId(meetingId, currUserId, currTime);
            // 与会者表
            conventioneerDAO.recoveryByMeetingId(meetingId, currUserId, currTime);
            // 会议议案表
            measureDAO.recoveryByMeetingId(meetingId, currUserId, currTime);
            // 落实情况表
            todoTaskDAO.recoveryByMeetingId(meetingId, currUserId, currTime);
            // 会议要求表
            meetingRequireDAO.recoveryByMeetingId(meetingId, currUserId, currTime);
            // 待办任务表
            workTaskDAO.recoveryByMeetingId(meetingId, currUserId, currTime);
            // 会议表
            return meetingsDAO.recoveryByMeetingId(meetingId, currUserId, currTime);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new SystemFailureException("还原失败，请检查数据");
        }
    }

    @Override
    public int addReplayTimes(Meetings meeting) {
        return meetingsDAO.addReplayTimes(meeting);
    }

    @Override
    public List<Meetings> selectMeetingsByMainProcessId(String mainProcessId) {
        return meetingsDAO.selectMeetingsByMainProcessId(mainProcessId);
    }

    @Override
    public List<Meetings> selectNotCreatedSigns() {
        return meetingsDAO.selectNotCreatedSigns();
    }

    @Override
    public void exchangeMeeting(String meetingIdInOfPlanId, Meetings meetingIdOutOfPlan) {

        meetingIdOutOfPlan.setIsplan(Short.valueOf("1"));
        meetingsDAO.updateByPrimaryKeySelective(meetingIdOutOfPlan);
        PlanTask planTask = planTaskDAO.selectByObjId(meetingIdInOfPlanId);
        if (MeetingStatusEnum.FINISHED.getCode().equals(meetingIdOutOfPlan.getStatus()))
            planTask.setStatus(SimpleDataEnum.PLANTASKDONE.getCode());

        planTask.setIsdeleted(Integer.valueOf(0));
        planTask.setObjid(meetingIdOutOfPlan.getId());
        planTaskDAO.updateByPrimaryKeySelective(planTask);
    }

    @Override
    public int deleteMeetingByOrgId(String orgId, String currUserId, Date currTime) {
        return meetingsDAO.deleteByOrgId(orgId, "组织架构删除", currUserId, currTime);
    }

    @Override
    public List<Users> getConventioneerListForMe(Meetings meeting) throws GeneralException {
        List<Integer> roleTypes = new ArrayList<>(10);
        roleTypes.add(RoleTypeEnum.PARTIER.getCode());
        // 会议受众列表
        List<Users> users;
//        users = usersDAO.selectByOrgCodeAndRole(meeting.getOrgcode(), roleTypes, true); // 党员列表
        users = usersDAO.selectByOrgCodeAndRole2(meeting.getOrgcode(), true); // 党员列表
        //2024 11.18 从参加人员中去选了
        List<Attendances> in = attendancesDAO.selectByMeetingId(meeting.getId(), false, "in", null, 1);
        //如果系统内参加人为空直接返回一个空列表
        if (!ValidateUtil.isNotValid(in)) return new ArrayList<>();
        Set<String> usersIdCollect = in.stream().map(Attendances::getPersonId).collect(Collectors.toSet());
        users = users.stream().filter(user->usersIdCollect.contains(user.getId())).collect(Collectors.toList());
//        if (MeetingTypeEnum.BRANCHLEADER.getCode().equals(meeting.getType())) {
//            users = usersDAO.selectByOrgCodeAndRole(meeting.getOrgcode(), roleTypes, true); // 党员列表
//
//            Map<String, Users> usersMap = new HashMap<>(16);
////            List<OrganizationLeaderListDTO> leaderList = userSV.getOrganizationLeaderList(meetings.getOrgid()); // 领导列表
//            // 任务编号：534，这里改为支部委员/总支委员，而非所有有职务的人员
//            String postCode = "";
//            // 判断会议是总支会议或是支部会议
//            Organization organization = orgDAO.selectByPrimaryKey(meeting.getOrgid());
//            if (organization.getOrgtype() == 3) {
//                postCode = "ZONGZHIWEIYUAN";
//            }
//            if (organization.getIsBranch() == 1) {
//                postCode = "ZHIBUWEIYUAN";
//            }
//            // 查询字典表中职务信息
//            Dictionaries dictionaries = dictionariesDAO.selectByCode(DICT_CODE_POST);
//            // 查询组织内用户职务信息
//            List<SysUserPositionDTO> leaderList = usersDAO.selectPositionByOrgId(postCode, dictionaries.getId(), meeting.getOrgid());
//
//            for (Users userTemp : users) {
//                for (SysUserPositionDTO leaderTemp : leaderList) {
//                    if (userTemp.getId().equals(leaderTemp.getUserId())) {
//                        usersMap.put(userTemp.getId(), userTemp);
//                        break;
//                    }
//                }
//            }
//            users = new ArrayList<>(usersMap.values());
//        }
//
////         委员会参会者去重
//        if (MeetingTypeEnum.BRANCHLEADER.getCode().equals(meeting.getType())) {
//            boolean isRecordLeader = false;
//            for (Users temp : users) {
//                if (StringUtils.isNotBlank(meeting.getRecorder()) && meeting.getRecorder().equals(temp.getId())) {
//                    isRecordLeader = true;
//                }
//            }
//            if (!isRecordLeader) {
//                Users record = usersDAO.selectNormalByPrimaryKey(meeting.getRecorder());
//                if (null != record) {
//                    Organization recorderOrg = orgDAO.selectByPrimaryKey(record.getOrgid());
//                    record.setCodestr(recorderOrg.getCodestr());
//                    record.setOrgsname(recorderOrg.getOrgsname());
//                    List<Role> roleList = roleDAO.selectUsersRolesByUserId(record.getId());
//
//                /*
//                记录员可以为党员和领导(组织者)，如果两角色兼具，则取党员角色；
//                仅有领导(组织者)角色，则不处理；
//                仅有党员角色，取党员角色；
//                两角色都没有，则报错
//                 */
//                    String roleId = null;
//                    for (Role temp : roleList) {
//                        if (RoleTypeEnum.PARTIER.getCode().equals(temp.getRoleType())) {
//                            roleId = temp.getId();
//                            break;
//                        }
//                    }
//                    if (StringUtils.isBlank(roleId)) {
//                        logger.error("委员会记录员角色有误");
//                        throw new GeneralException("PBMS_ME_1021", "委员会记录员角色有误");
//                    }
//                    record.setCurrentRoleId(roleId);
//                    users.add(record);
//                }
//            }
//        }
        return users;
    }

    @Override
    public int endMeetingsByPrimaryKey(String id, String userId, Date modifieddate) throws GeneralException {
        // 查询会议信息
        Meetings meetings = meetingsDAO.selectByPrimaryKey(id);
        if (null == meetings) {
            logger.error("未能查询到会议信息（id:" + meetings + ")");
            throw new GeneralException("PBMS_ME_1002", "未能查询到会议信息");
        }

        // 生成会议归档的短信提醒
        genMsgForHoldk(meetings, modifieddate, userId);
        // 非确认参加者，关闭提交记录的短信提醒
        List<Integer> statusList = new ArrayList<>(2);
        statusList.add(0);
        statusList.add(2);
        conventioneerDAO.updateNoticeSetByMeetingId(id, userId, modifieddate, 1, 0, statusList);

//        WorkTask workTask = new WorkTask();
//        workTask.setObjid(meetings.getId());
//        workTask.setType(WorkTaskClassEnum.CONTROL.getCode());
//        workTask.setTasktype(ConvertUtil.meetingTypeToTaskType(meetings.getType()));
//        workTask.setTaskstatus(Integer.valueOf(1));
//        workTask.setModifiedby("system");
//        workTask.setModifieddate(modifieddate);
//        workTaskDAO.doneByObjidAndType(workTask);
        //给党务工作者生成会议结束代办
        List<UserRoleForMeDTO> leaderIds = usersDAO.selectUserPMIdsByOrgid(meetings.getOrgid(), RoleTypeEnum.WORKER.getCode());
        updateWorkTask(meetings, leaderIds, modifieddate, WorkTaskClassEnum.HOLD.getCode(), ConvertUtil.meetingTypeToTaskType(meetings.getType()), "system");

        // 更新会议至“待归档”
        meetings.setEndtime(modifieddate);
        meetings.setModifiedby(userId);
        meetings.setModifieddate(modifieddate);
        meetings.setStatus(MeetingStatusEnum.TOBIEHOLD.getCode());
        Meetings meetings1=this.meetingsDAO.selectByPrimaryKey(meetings.getId());
        //
        if(StringUtils.isBlank(meetings.getModerator())){
            meetings.setModerator(meetings1.getModerator());
        }
        if(meetings.getActivityCount()==null){
            meetings.setActivityCount(meetings1.getActivityCount());
        }
        if(StringUtils.isBlank(meetings.getAddress())){
            meetings.setAddress(meetings1.getAddress());
        }
        if(StringUtils.isBlank(meetings.getRecordContent())){
            meetings.setRecordContent(meetings1.getRecordContent());
        }
        return meetingsDAO.updateByPrimaryKeySelective(meetings);
    }

    @Override
    public List<MeStistDTO> getMeetingStatistics(String meetingIds, String sortRule) {
        List<String> meetingIdss = Arrays.asList(meetingIds.split(","));
        return meetingsDAO.selectMeetingStatistics(null);
    }

    @Override
    public List<MeetingsRelatedDTO> queryAllRelatedMeetings(Integer type, String topic, Date startDate, Date endDate, String codeStr) {
        return meetingsDAO.selectAllRelatedMeetings(type, topic, startDate, endDate, codeStr);
    }

    @Override
    public List<MeetingsRelatedDTO> queryNotRelatedMeetings(Integer type, String topic, Date startDate, Date endDate, String codeStr) {
        return meetingsDAO.selectNotRelatedMeetings(type, topic, startDate, endDate, codeStr);
    }

    @Override
    public List<MeetingsRelatedDTO> queryNotRelatedMeetingsForVd(Integer type, String topic, Date startDate, Date endDate, String currUserId) {
        return meetingsDAO.selectNotRelatedMeetingsForVd(type, topic, startDate, endDate, currUserId);
    }

    @Override
    public PageInfo<AuditMeetingListDTO> getAuditMeetingList(String currentUserId, String currentUserRoleId, Integer type, String topic, Integer auditState, String orgId, Date auditTodoTimeStart, Date auditTodoTimeEnd, Date auditExampleTimeStart, Date auditExampleTimeEnd, Integer page, Integer limit) {
        PageHelper.startPage(page, limit);
        return new PageInfo<>(meetingsDAO.selectAuditMeetingList(currentUserId, currentUserRoleId, type, topic, auditState, orgId, auditTodoTimeStart, auditTodoTimeEnd, auditExampleTimeStart, auditExampleTimeEnd));
    }

    @Override
    public AuditMeetingDetailDTO getAuditMeetingDetail(String id, String userId) {
        AuditMeetingDetailDTO auditMeetingDetailDTO = meetingsDAO.selectAuditMeetingDetailDTOById(id);
        List<Attachments> attachmentsList = attachmentsDAO.selectListByObjId(auditMeetingDetailDTO.getId());

        for (Attachments attachments : attachmentsList) {
            String attType = attachments.getAtttype();
            if (PbmsConstants.QR_BUS_ME_SIGN.equals(attType))
                auditMeetingDetailDTO.setQrUrl(attachments.getUrl());
            else if (AttachTypeEnum.ME_MEETING.getType().equals(attType)) {
                List<Attachments> list = auditMeetingDetailDTO.getAttachmentsList();
                if (null == list) {
                    list = new ArrayList<>();
                    auditMeetingDetailDTO.setAttachmentsList(list);
                }
                list.add(attachments);
            } else if (AttachTypeEnum.ME_THEMEPIC.getType().equals(attType)) {
                List<Attachments> list = auditMeetingDetailDTO.getThemepicAttachement();
                if (null == list) {
                    list = new ArrayList<>();
                    auditMeetingDetailDTO.setThemepicAttachement(list);
                }
                list.add(attachments);
            } else if (AttachTypeEnum.ME_SIGN.getType().equals(attType)) {
                List<Attachments> list = auditMeetingDetailDTO.getSignAttachment();
                if (null == list) {
                    list = new ArrayList<>();
                    auditMeetingDetailDTO.setSignAttachment(list);
                }
                list.add(attachments);
            } else if (AttachTypeEnum.ME_PICTURE.getType().equals(attType)) {
                List<Attachments> list = auditMeetingDetailDTO.getPictureAttachment();
                if (null == list) {
                    list = new ArrayList<>();
                    auditMeetingDetailDTO.setPictureAttachment(list);
                }
                list.add(attachments);
            } else if (AttachTypeEnum.ME_MINUTES.getType().equals(attType)) {
                List<Attachments> list = auditMeetingDetailDTO.getMinutesAttachment();
                if (null == list) {
                    list = new ArrayList<>();
                    auditMeetingDetailDTO.setMinutesAttachment(list);
                }
                list.add(attachments);
            }
        }
        auditMeetingDetailDTO.setMeVoiceTranslationInfos(meVoiceTranslationInfoDAO.selectByObjId(auditMeetingDetailDTO.getId()));
        auditMeetingDetailDTO.setAuditReviewLogList(reviewLogDAO.selectByTypeAndObjId(ProcessTypeEnum.ME_AUDIT.getCode(), auditMeetingDetailDTO.getId()));
        // 参与情况
        if (MEETING_TYPE_ONLINE.equals(auditMeetingDetailDTO.getChannelType()) && userId.equals(auditMeetingDetailDTO.getModerator())) {
            List<String> meetingIds = meetingsDAO.selectMeetingIdsByMainProcessId(auditMeetingDetailDTO.getMainProcessId());
            auditMeetingDetailDTO.setCountConventioneerInfoDTO(conventioneerDAO.selectCountConventioneerInfoByMeetingId(null, meetingIds));
        } else {
            auditMeetingDetailDTO.setCountConventioneerInfoDTO(conventioneerDAO.selectCountConventioneerInfoByMeetingId(auditMeetingDetailDTO.getId(), null));
        }
        return auditMeetingDetailDTO;
    }


    @Override
    public int commitToExamine(Meetings meetings, Users currentUser, Date date) {
        //
        Meetings newMeeting = meetingsDAO.selectByPrimaryKey(meetings.getId());
        //
        if (Integer.valueOf(0).equals(meetings.getIsSave())){
            newMeeting.setStatus(MeetingNewStatusEnum.TOEXAMINE.getCode());
        }
        newMeeting.setId(meetings.getId());
        newMeeting.setModifiedby(currentUser.getId());
        newMeeting.setModifieddate(date);
        Meetings meetings1=this.meetingsDAO.selectByPrimaryKey(meetings.getId());
        //
        if(StringUtils.isBlank(newMeeting.getModerator())){
            newMeeting.setModerator(meetings1.getModerator());
        }
        if(meetings.getActivityCount()==null){
            newMeeting.setActivityCount(meetings1.getActivityCount());
        }
        if(StringUtils.isBlank(newMeeting.getAddress())){
            newMeeting.setAddress(meetings1.getAddress());
        }
        if(StringUtils.isBlank(newMeeting.getRecordContent())){
            newMeeting.setRecordContent(meetings1.getRecordContent());
        }
        return meetingsDAO.updateByPrimaryKeySelective(newMeeting);
    }

    @Override
    public Integer generAuditTask(Meetings meetings, Users currUser, Date date) throws GeneralException {

        Integer audit_code = WorkTaskTypeEnum.AUDIT.getCode();

        meetings.setStatus(Integer.valueOf(450));
        meetings.setAuditState(Integer.valueOf(1));
        meetings.setModifiedby(currUser.getId());
        meetings.setModifieddate(date);
        meetings.setAuditTodoTime(date);

        List<WorkTask> workTaskList = new ArrayList<>();
        List<Users> userList = getUserByOrgId(meetings.getOrgcode());
        if (CollectionUtils.isEmpty(userList))
            throw new GeneralException("PBMS_ME_1066");

        for (Users user : userList) {
            WorkTask workTask = workTaskDAO.selectWorkTaskByObjIdAndTaskTypeAndType(meetings.getId(), audit_code, audit_code, user.getId(), user.getCurrentRoleId());
            if (null != workTask) {
                if (1 == workTask.getTaskstatus()) {
                    workTask.setTaskstatus(Integer.valueOf(0));
                    workTask.setModifiedby(currUser.getId());
                    workTask.setModifieddate(date);
                    workTaskDAO.updateByPrimaryKeySelective(workTask);
                }
            } else {
                workTask = new WorkTask();
                workTask.setId(UIDUtil.getUID());
                workTask.setObjid(meetings.getId());
                workTask.setTaskname("会议稽核【" + meetings.getTopic() + "】");
                workTask.setUserid(user.getId());
                workTask.setTasktype(audit_code);
                workTask.setType(audit_code);
                workTask.setTaskstatus(Integer.valueOf(0));
                workTask.setIsdeleted(Integer.valueOf(0));
                workTask.setCreatedby(currUser.getId());
                workTask.setCreateddate(date);
                workTask.setUserRole(user.getCurrentRoleId());
                workTaskList.add(workTask);
            }
        }

        ReviewLog reviewLog = new ReviewLog();
        reviewLog.setId(UIDUtil.getUID());
        reviewLog.setProcessType(ProcessTypeEnum.ME_AUDIT.getCode());
        reviewLog.setObjectId(meetings.getId());
        //5001( 1：提交稽核 2：稽核不通过 3：稽核通过)
        reviewLog.setOperationType(Integer.valueOf(1));
        reviewLog.setOperationDesc("提交稽核");
        reviewLog.setOperationUserId(currUser.getId());
        reviewLog.setOperationUserName(currUser.getUsername());
        reviewLog.setOperationTime(date);
        reviewLog.setRemark(currUser.getUsername().concat(currUser.getTelephones()).concat("，提交至中台进行数据稽核"));
        reviewLogDAO.insertSelective(reviewLog);

        if (!workTaskList.isEmpty())
            workTaskDAO.insertBatch(workTaskList);
        //
        Meetings meetings1=this.meetingsDAO.selectByPrimaryKey(meetings.getId());
        //
        if(StringUtils.isBlank(meetings.getModerator())){
            meetings.setModerator(meetings1.getModerator());
        }
        if(meetings.getActivityCount()==null){
            meetings.setActivityCount(meetings1.getActivityCount());
        }
        if(StringUtils.isBlank(meetings.getAddress())){
            meetings.setAddress(meetings1.getAddress());
        }
        if(StringUtils.isBlank(meetings.getRecordContent())){
            meetings.setRecordContent(meetings1.getRecordContent());
        }
        return meetingsDAO.updateByPrimaryKeySelective(meetings);
    }

    @Override
    public int sendBackAuditMsg(Users currUser, String objId, String objTopic, Integer objType, Integer processType, String subUserId) {
        Map<String, Object> params = new HashMap<>();
        params.put("processType", processType);
        params.put("objectId", objId);
        // 5001/6001/1021(1：提交稽核 2：稽核不通过 3：稽核通过)
        params.put("operationType", Integer.valueOf(1));

        if (StringUtils.isBlank(subUserId)) {
            List<ReviewLog> reviewLogs = reviewLogDAO.selectAppointByParams(params);
            subUserId = reviewLogs.get(0).getOperationUserId();
        }

        if (StringUtils.isNotBlank(subUserId)) {
            Users users = usersDAO.selectByPrimaryKey(subUserId);

            Map<String, String> pushMsgParams = new HashMap<>();

            if (ProcessTypeEnum.ME_AUDIT.getCode().equals(processType)) {
                String businessType = "";

                switch (objType) {
                    case 10:
                        businessType = MeetingTypeEnum.BRANCHMASSES.getDesc();
                        break;
                    case 20:
                        businessType = MeetingTypeEnum.BRANCHLEADER.getDesc();
                        break;
                    case 30:
                        businessType = MeetingTypeEnum.PARTYGROUP.getDesc();
                        break;
                    case 40:
                        businessType = MeetingTypeEnum.NEWSTUDY.getDesc();
                        break;
                    case 50:
                        businessType = MeetingTypeEnum.STUDY.getDesc();
                        break;
                }
                pushMsgParams.put("txt_businessType", businessType);
                pushMsgParams.put("rspKey", "rspId037");
            } else {
                pushMsgParams.put("rspKey", "rspId038");
            }

            pushMsgParams.put("txt_businessTheme", objTopic);
            pushMsgParams.put("phoneNum", users.getTelephones());

            Date currDate = new Date();

            Date shouldSendDate = comUtils.getNoticDate(pushMsgParams.get("rspKey"));

            PushMsg pushMsg = new PushMsg();
            pushMsg.setId(UIDUtil.getUID());
            pushMsg.setMsgname(objTopic.concat("中台稽核退回"));
            pushMsg.setContent(JSONUtils.toJSONString(pushMsgParams));
            pushMsg.setUserid(users.getId());
            pushMsg.setRspId(pushMsgParams.get("rspKey"));
            pushMsg.setAuthorid(currUser.getId());
            pushMsg.setTasktype(objType); // 系统中三会一课模块，任务类型和推送消息系统的业务类型保持一致，后期如有变动，需注意
            pushMsg.setObjid(objId);
            pushMsg.setMsgtype(9); // 消息重要程度
            pushMsg.setMsgstatus(0);
            pushMsg.setIsdeleted(DataIsDeleteEnum.NORMAL.getCode());
            pushMsg.setCreatedby(currUser.getId());
            pushMsg.setCreateddate(currDate);
            pushMsg.setShouldSendDate(shouldSendDate);

            return pushMsgDAO.insertSelective(pushMsg);
        }

        return 0;
    }

    @Override
    public Integer generSignatureToDo(Meetings meetings, Integer signState, JSONArray meMeetingSignDetailListJson, String orgId, String username, String currentUserId, Date date) throws SystemFailureException, StorageException {

        List<MeMeetingSignDetail> meMeetingSignDetails = meMeetingSignDetailDAO.selectByMeetingId(meetings.getId());
        Set<String> newUserId = new HashSet<>();
        Set<String> allUserIds = new HashSet<>();
        String baseId = UIDUtil.getUID(8);
        int index = 0;
        String roleId = roleDAO.selectIdsByRoleType(2).get(0);

        if (0 == signState && !meMeetingSignDetails.isEmpty()) {
            for (MeMeetingSignDetail meMeetingSignDetail : meMeetingSignDetails) {
                meMeetingSignDetail.setIsdeleted(Integer.valueOf(1));
                meMeetingSignDetail.setModifiedby(currentUserId);
                meMeetingSignDetail.setModifieddate(date);
                meMeetingSignDetailDAO.updateByPrimaryKeySelective(meMeetingSignDetail);

                WorkTask workTask = workTaskDAO.selectWorkTaskByObjIdAndTaskTypeAndType(meetings.getId(), ConvertUtil.meetingTypeToTaskType(meetings.getType()), WorkTaskClassEnum.SHUJISIGN.getCode(), meMeetingSignDetail.getUserId(), roleId);
                if (null != workTask) {
                    workTask.setTaskstatus(Integer.valueOf(0));
                    workTask.setModifiedby(currentUserId);
                    workTask.setModifieddate(date);
                    workTaskDAO.updateByPrimaryKeySelective(workTask);
                }
            }

        } else if (1 == signState) {

//            Set<String> keyStrs = new HashSet<>();
//            Set<String> end_tags = new HashSet<>();
//            Map<String, String> code_name = new HashMap<>();
//            List<DictionaryItems> dictionaryItems_end_tags = dictionaryItemsDAO.selectEnabledByDictCode("me_sign_end");
//            List<DictionaryItems> dictionaryItems = dictionaryItemsDAO.selectEnabledByDictCode("me_sign_role");
//            for (DictionaryItems dictionaryItem : dictionaryItems_end_tags)
//                end_tags.add(dictionaryItem.getItemtext());
//            for (DictionaryItems dictionaryItem : dictionaryItems)
//                code_name.put(dictionaryItem.getCodestr(), dictionaryItem.getItemtext());
//            for (int i = meMeetingSignDetailListJson.size() - 1; i > -1; i--)
//                keyStrs.add(code_name.get(meMeetingSignDetailListJson.getJSONObject(i).getString("signType")));

            Attachments attachments = attachmentsDAO.selectOneByObjIdType(meetings.getId(), AttachTypeEnum.ME_MINUTES.getType());

            ByteArrayOutputStream byteArrayOutputStream_xml = new ByteArrayOutputStream();
            ByteArrayOutputStream byteArrayOutputStream_pdf = new ByteArrayOutputStream();

            InputStream inputStream = OnestUtil.downloadByPrivateUrl(OnestUtil.getKeyByUrl(attachments.getUrl()));

            if (attachments.getAttname().endsWith("doc")) {
                Word2Html.word2003ToHtml(inputStream, byteArrayOutputStream_xml);
            } else {
                Word2Html.docx2007ToHtml(inputStream, byteArrayOutputStream_xml);
            }
//            Word2Html.checkKeyStrs(new ByteArrayInputStream(byteArrayOutputStream_xml.toByteArray()), keyStrs, end_tags);

            //重新生成pdf
            String xmlUrl;
            String pdfUrl;
            String uid = UIDUtil.getUID();
            xmlUrl = OnestUtil.storeByStream(new ByteArrayInputStream(byteArrayOutputStream_xml.toByteArray()), uid + ".html", attachments.getAtttype(), "default", "default");
            Html2Pdf.convertHtmlToPdf(byteArrayOutputStream_xml.toString(), byteArrayOutputStream_pdf);
            OnestUtil.close(byteArrayOutputStream_xml, byteArrayOutputStream_pdf);
            pdfUrl = OnestUtil.storeByStream(new ByteArrayInputStream(byteArrayOutputStream_pdf.toByteArray()), uid + ".pdf", attachments.getAtttype(), "default", "default");

            attachments.setExtFld3(OnestUtil.subStringUrl(xmlUrl));
            attachments.setExtFld1(OnestUtil.subStringUrl(pdfUrl));
            attachmentsDAO.updateByPrimaryKeySelective(attachments);

            Map<String, MeMeetingSignDetail> meMeetingSignDetailMap = new HashMap<>();
            for (MeMeetingSignDetail meMeetingSignDetail : meMeetingSignDetails)
                meMeetingSignDetailMap.put(meMeetingSignDetail.getSignType() + meMeetingSignDetail.getUserId(), meMeetingSignDetail);

            if (null != meMeetingSignDetailListJson)
                for (int i = meMeetingSignDetailListJson.size() - 1; i > -1; i--) {
                    JSONObject jsonObject = meMeetingSignDetailListJson.getJSONObject(i);
                    String userId = jsonObject.getString("userId");
                    String signType = jsonObject.getString("signType");
                    Integer needToSign = jsonObject.getInteger("needToSign");

                    if (null != needToSign && 1 == needToSign)
                        allUserIds.add(userId);

                    MeMeetingSignDetail meMeetingSignDetail = meMeetingSignDetailMap.get(signType + userId);
                    if (null == meMeetingSignDetail) {

                        newUserId.add(userId);
                        meMeetingSignDetail = new MeMeetingSignDetail();
                        meMeetingSignDetail.setId(UIDUtil.setUid(baseId, index++));
                        meMeetingSignDetail.setMeetingId(meetings.getId());
                        meMeetingSignDetail.setSignType(signType);
                        meMeetingSignDetail.setSignState(Integer.valueOf(0));
                        meMeetingSignDetail.setUserId(userId);
                        meMeetingSignDetail.setNeedToSign(needToSign);
                        meMeetingSignDetail.setIsdeleted(Integer.valueOf(0));
                        meMeetingSignDetail.setCreatedby(currentUserId);
                        meMeetingSignDetail.setCreateddate(date);
                        meMeetingSignDetailDAO.insertSelective(meMeetingSignDetail);
                    } else {
                        newUserId.remove(userId);
                        meMeetingSignDetail.setNeedToSign(needToSign);
                        meMeetingSignDetail.setSignState(Integer.valueOf(0));
                        meMeetingSignDetail.setSignDate(null);
                        meMeetingSignDetail.setModifiedby(currentUserId);
                        meMeetingSignDetail.setModifieddate(date);
                        meMeetingSignDetailDAO.updateByPrimaryKeySelective(meMeetingSignDetail);
                    }
                }
        }

        String meetingType = "unknow";
        if (meetings.getType() == MeetingTypeEnum.BRANCHMASSES.getCode()) {
            meetingType = MeetingTypeEnum.BRANCHMASSES.getDesc();
        } else if (meetings.getType() == MeetingTypeEnum.BRANCHLEADER.getCode()) {
            meetingType = MeetingTypeEnum.BRANCHLEADER.getDesc();
        } else if (meetings.getType() == MeetingTypeEnum.PARTYGROUP.getCode()) {
            meetingType = MeetingTypeEnum.PARTYGROUP.getDesc();
        } else if (meetings.getType() == MeetingTypeEnum.NEWSTUDY.getCode()) {
            meetingType = MeetingTypeEnum.NEWSTUDY.getDesc();
        }

        Map<String, Object> param = new HashMap<>();
        param.put("txt_businessType", meetingType);
        param.put("txt_businessTheme", meetings.getTopic());

        addWordTaskAndMsg(allUserIds, newUserId, meetings, WorkTaskClassEnum.SHUJISIGN.getCode(), ConvertUtil.meetingTypeToTaskType(meetings.getType()), roleDAO.selectIdsByRoleType(2).get(0), currentUserId, date, true, "书记签名", "rspId039", meetingType, param);

        ReviewLog reviewLog = new ReviewLog();
        reviewLog.setId(UIDUtil.setUid(baseId, index++));
        reviewLog.setProcessType(ProcessTypeEnum.ME_SIGN.getCode());
        reviewLog.setObjectId(meetings.getId());
        reviewLog.setOperationType(Integer.valueOf(1));
        reviewLog.setOperationDesc("提交会议纪要审核");
        reviewLog.setOperationUserId(currentUserId);
        reviewLog.setOperationUserName(username);
        reviewLog.setOperationTime(date);
        reviewLog.setUserOrgId(orgId);
        reviewLog.setRemark("已提交会议纪要审核");

        reviewLogDAO.insertSelective(reviewLog);

        meetings.setSignState(signState);
        meetings.setModifiedby(currentUserId);
        meetings.setModifieddate(date);
        Meetings meetings1=this.meetingsDAO.selectByPrimaryKey(meetings.getId());
        //
        if(StringUtils.isBlank(meetings.getModerator())){
            meetings.setModerator(meetings1.getModerator());
        }
        if(meetings.getActivityCount()==null){
            meetings.setActivityCount(meetings1.getActivityCount());
        }
        if(StringUtils.isBlank(meetings.getAddress())){
            meetings.setAddress(meetings1.getAddress());
        }
        if(StringUtils.isBlank(meetings.getRecordContent())){
            meetings.setRecordContent(meetings1.getRecordContent());
        }
        return meetingsDAO.updateByPrimaryKeySelective(meetings);
    }

    @Override
    public Integer signRefuse(String meetingId, String refuseReason, String orgId, String username, String userId, Date date) throws SystemFailureException {
        List<MeMeetingSignDetail> meMeetingSignDetails = meMeetingSignDetailDAO.selectByMeetingIdAndUserId(meetingId, userId);
        if (null == meMeetingSignDetails || meMeetingSignDetails.isEmpty())
            throw new SystemFailureException("前面记录未查询到");
        Meetings meetings = meetingsDAO.selectByPrimaryKey(meMeetingSignDetails.get(0).getMeetingId());
        Set<String> userIds = new HashSet<>();
        String baseId = UIDUtil.getUID(8);
        int index = 0;

        String roleId = roleDAO.selectIdsByRoleType(2).get(0);

        List<MeMeetingSignDetail> meMeetingSignDetails_all = meMeetingSignDetailDAO.selectByMeetingId(meetings.getId());
        if (!meMeetingSignDetails_all.isEmpty())
            for (MeMeetingSignDetail meetingSignDetail : meMeetingSignDetails_all) {
                WorkTask workTask = workTaskDAO.selectWorkTaskByObjIdAndTaskTypeAndType(meetings.getId(), ConvertUtil.meetingTypeToTaskType(meetings.getType()), WorkTaskClassEnum.SHUJISIGN.getCode(), meetingSignDetail.getUserId(), roleId);
                if (null != workTask) {
                    workTask.setTaskstatus(Integer.valueOf(1));
                    workTask.setModifiedby(userId);
                    workTask.setModifieddate(date);
                    workTaskDAO.updateByPrimaryKeySelective(workTask);
                    if (null != meetingSignDetail.getNeedToSign() && 1 == meetingSignDetail.getNeedToSign())
                        userIds.add(meetingSignDetail.getUserId());
                }
            }

        Integer workTaskType = ConvertUtil.meetingTypeToTaskType(meetings.getType());
        String meetingType = "unknow";
        if (meetings.getType() == MeetingTypeEnum.BRANCHMASSES.getCode()) {
            meetingType = MeetingTypeEnum.BRANCHMASSES.getDesc();
        } else if (meetings.getType() == MeetingTypeEnum.BRANCHLEADER.getCode()) {
            meetingType = MeetingTypeEnum.BRANCHLEADER.getDesc();
        } else if (meetings.getType() == MeetingTypeEnum.PARTYGROUP.getCode()) {
            meetingType = MeetingTypeEnum.PARTYGROUP.getDesc();
        } else if (meetings.getType() == MeetingTypeEnum.NEWSTUDY.getCode()) {
            meetingType = MeetingTypeEnum.NEWSTUDY.getDesc();
        }

        Map<String, Object> param = new HashMap<>();
        param.put("txt_businessType", meetingType);
        param.put("txt_businessTheme", meetings.getTopic());

        addWordTaskAndMsg(userIds, userIds, meetings, WorkTaskClassEnum.SHUJISIGN.getCode(), workTaskType, roleDAO.selectIdsByRoleType(2).get(0), userId, date, false, "签名拒绝", "rspId040", meetingType, param);

        // 会议组织者
        List<UserRoleForMeDTO> leaderIds = usersDAO.selectUserPMIdsByOrgid(meetings.getOrgid(), RoleTypeEnum.WORKER.getCode());
        List<PushMsg> pushMsgs = new ArrayList<>(leaderIds.size());
        for (UserRoleForMeDTO userRoleForMeDTO : leaderIds) {

            PushMsg pushMsg = new PushMsg();
            pushMsg.setId(UIDUtil.setUid(baseId, index++));
            pushMsg.setMsgname("签名拒绝" + meetings.getTopic());
            pushMsg.setUserid(userRoleForMeDTO.getUserid());
            pushMsg.setRspId("rspId041");
            pushMsg.setAuthorid(userId);
            pushMsg.setContent(JSONUtils.toJSONString(param));
            pushMsg.setTasktype(workTaskType);
            pushMsg.setObjid(meetings.getId());
            pushMsg.setMsgtype(9); // 消息重要程度
            pushMsg.setMsgstatus(0);
            pushMsg.setIsdeleted(DataIsDeleteEnum.NORMAL.getCode());
            pushMsg.setCreatedby(userId);
            pushMsg.setCreateddate(date);
            pushMsg.setShouldSendDate(new Date());
            pushMsgs.add(pushMsg);
        }

        if (!pushMsgs.isEmpty())
            pushMsgDAO.insertBatch(pushMsgs);

        ReviewLog reviewLog = new ReviewLog();
        reviewLog.setId(UIDUtil.setUid(baseId, index++));
        reviewLog.setProcessType(ProcessTypeEnum.ME_SIGN.getCode());
        reviewLog.setObjectId(meetings.getId());
        reviewLog.setOperationType(Integer.valueOf(2));
        reviewLog.setOperationDesc("审核不通过");
        reviewLog.setOperationUserId(userId);
        reviewLog.setOperationUserName(username);
        reviewLog.setOperationTime(date);
        reviewLog.setUserOrgId(orgId);
        reviewLog.setResuseReason(refuseReason);
        reviewLog.setRemark("已提交会议纪要审核");

        reviewLogDAO.insertSelective(reviewLog);

        meetings.setSignState(Integer.valueOf(2));
        meetings.setSignReason(refuseReason);
        meetings.setModifiedby(userId);
        meetings.setModifieddate(date);
//        List<Attachments> attachmentsList = attachmentsDAO.selectListByObjIdType(meetings.getId(), AttachTypeEnum.ME_MINUTES.getType());
//        for (Attachments attachments : attachmentsList) {
//            attachments.setExtFld1(null);
//            attachments.setExtFld3(null);
//            attachmentsDAO.updateByPrimaryKey(attachments);
//        }
                Meetings meetings1=this.meetingsDAO.selectByPrimaryKey(meetings.getId());
        //
        if(StringUtils.isBlank(meetings.getModerator())){
            meetings.setModerator(meetings1.getModerator());
        }
        if(meetings.getActivityCount()==null){
            meetings.setActivityCount(meetings1.getActivityCount());
        }
        if(StringUtils.isBlank(meetings.getAddress())){
            meetings.setAddress(meetings1.getAddress());
        }
        if(StringUtils.isBlank(meetings.getRecordContent())){
            meetings.setRecordContent(meetings1.getRecordContent());
        }
        return meetingsDAO.updateByPrimaryKeySelective(meetings);
    }

    @Override
    public Meetings sign(String meetingId, String signImageUrl, String userId, String userName, Date date) throws SystemFailureException {
        Meetings meetings = meetingsDAO.selectByPrimaryKey(meetingId);
        WorkTask workTask = workTaskDAO.selectWorkTaskByObjIdAndTaskTypeAndType(meetingId, null, WorkTaskClassEnum.SHUJISIGN.getCode(), userId, null);
        if (null == workTask)
            return null;
        List<MeMeetingSignDetail> meMeetingSignDetails = meMeetingSignDetailDAO.selectByMeetingIdAndUserId(workTask.getObjid(), userId);
        if (null == meMeetingSignDetails || meMeetingSignDetails.isEmpty())
            return null;

        List<DictionaryItems> dictionaryItems = dictionaryItemsDAO.selectEnabledByDictCode("me_sign_role");
        Set<String> signTypes = new HashSet<>();

        Attachments attachments = attachmentsDAO.selectOneByObjIdType(workTask.getObjid(), AttachTypeEnum.ME_MINUTES.getType());
        if (null == attachments || StringUtils.isBlank(attachments.getExtFld1()))
            throw new SystemFailureException("签名失败，源文件未找到");

        for (MeMeetingSignDetail meMeetingSignDetail : meMeetingSignDetails) {
            if (null == meMeetingSignDetail || 1 == meMeetingSignDetail.getSignState())
                continue;
            signTypes.add(meMeetingSignDetail.getSignType());
        }
//
//        ByteArrayOutputStream byteArrayOutputStream = Word2Html.addSign(OnestUtil.getInputStreamByONestUrl(attachments.getExtFld3()), signType, signImageUrl);
//        ByteArrayOutputStream byteArrayOutputStream = Word2Html.addSign2(OnestUtil.getInputStreamByONestUrl(attachments.getExtFld3()), signImageUrl);
        String html = Word2Html.addSign3(OnestUtil.getContent(attachments.getExtFld3()), signTypes, signImageUrl, dictionaryItems);

        if (null != html) {
            ByteArrayOutputStream byteArrayOutputStream_pdf = new ByteArrayOutputStream();
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(html.getBytes());
            String xmlUrl = null;
            String pdfUrl = null;

            String uid = UIDUtil.getUID();
            try {
                xmlUrl = OnestUtil.storeByStream(byteArrayInputStream, uid + ".html", attachments.getAtttype(), "default", "default");
                Html2Pdf.convertHtmlToPdf(html, byteArrayOutputStream_pdf);
                pdfUrl = OnestUtil.storeByStream(new ByteArrayInputStream(byteArrayOutputStream_pdf.toByteArray()), uid + ".pdf", attachments.getAtttype(), "default", "default");
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                throw new SystemFailureException("签名文件合并失败");
            } finally {
                OnestUtil.close(byteArrayInputStream, byteArrayOutputStream_pdf);
            }

            attachments.setExtFld1(OnestUtil.subStringUrl(pdfUrl));
            attachments.setExtFld3(OnestUtil.subStringUrl(xmlUrl));
            attachmentsDAO.updateByPrimaryKeySelective(attachments);
        }
        for (MeMeetingSignDetail meMeetingSignDetail : meMeetingSignDetails) {
            if (1 == meMeetingSignDetail.getSignState())
                continue;
            meMeetingSignDetail.setSignState(Integer.valueOf(1));
            meMeetingSignDetail.setSignUrl(signImageUrl);
            meMeetingSignDetail.setSignDate(date);
            meMeetingSignDetail.setModifiedby(userId);
            meMeetingSignDetail.setModifieddate(date);
            meMeetingSignDetailDAO.updateByPrimaryKeySelective(meMeetingSignDetail);
        }

        ReviewLog reviewLog = new ReviewLog();
        reviewLog.setId(UIDUtil.getUID());
        reviewLog.setProcessType(ProcessTypeEnum.ME_SIGN.getCode());
        reviewLog.setObjectId(workTask.getObjid());
        reviewLog.setOperationType(Integer.valueOf(3));
        reviewLog.setOperationDesc("审核通过");
        reviewLog.setOperationUserId(userId);
        reviewLog.setOperationUserName(userName);
        reviewLog.setOperationTime(date);
        reviewLog.setRemark("已提交会议纪要审核");

        reviewLogDAO.insertSelective(reviewLog);

        Integer countToDo = meMeetingSignDetailDAO.selectCountToDoByMeetingId(meetings.getId());
        if (0 == countToDo) {
            meetings.setSignState(Integer.valueOf(3));
            meetings.setModifiedby(userId);
            meetings.setModifieddate(date);
            Meetings meetings1=this.meetingsDAO.selectByPrimaryKey(meetings.getId());
            //
            if(StringUtils.isBlank(meetings.getModerator())){
                meetings.setModerator(meetings1.getModerator());
            }
            if(meetings.getActivityCount()==null){
                meetings.setActivityCount(meetings1.getActivityCount());
            }
            if(StringUtils.isBlank(meetings.getAddress())){
                meetings.setAddress(meetings1.getAddress());
            }
            if(StringUtils.isBlank(meetings.getRecordContent())){
                meetings.setRecordContent(meetings1.getRecordContent());
            }
            meetingsDAO.updateByPrimaryKeySelective(meetings);
        }

        workTask.setTaskstatus(Integer.valueOf(1));
        workTask.setModifiedby(userId);
        workTask.setModifieddate(date);
        workTaskDAO.updateByPrimaryKeySelective(workTask);
        return meetings;
    }

    private void addWordTaskAndMsg(Set<String> allUserIds, Set<String> newUserId, Meetings meeting, Integer type, int workTaskType, String roleId, String currentUserId, Date date, Boolean haveTask, String msgName, String rspId, String meetingType, Map<String, Object> param) {
        List<WorkTask> workTasks = new ArrayList<>();
        List<PushMsg> pushMsgs = new ArrayList<>();
        String baseId = UIDUtil.getUID(8);
        int i = 0;

        for (String userId : allUserIds) {

            Users users = usersDAO.selectByPrimaryKey(userId);
            param.put("rspKey", rspId);
            param.put("phoneNum", users.getTelephones());

            if (haveTask) {
                WorkTask workTask = workTaskDAO.selectWorkTaskByObjIdAndTaskTypeAndType(meeting.getId(), workTaskType, type, userId, roleId);
                if (null == workTask) {
                    workTask = new WorkTask();
                    workTask.setId(UIDUtil.setUid(baseId, i++));
                    workTask.setObjid(meeting.getId());
                    workTask.setSubobjid(null);
                    workTask.setTaskname(meeting.getTopic());
                    workTask.setSummary(meeting.getDescription());
                    workTask.setUserid(userId);
                    workTask.setTasktype(workTaskType);
                    workTask.setType(type);
                    workTask.setTaskstatus(Integer.valueOf(0));
                    workTask.setIsdeleted(DataIsDeleteEnum.NORMAL.getCode());
                    workTask.setCreatedby(currentUserId);
                    workTask.setCreateddate(date);
                    workTask.setUserRole(roleId);
                    workTasks.add(workTask);
                } else {
                    workTask.setTaskstatus(Integer.valueOf(0));
                    workTask.setModifiedby(null);
                    workTask.setModifieddate(null);
                    workTaskDAO.updateByPrimaryKeySelective(workTask);
                }
            }

            if (newUserId.contains(userId)) {
                PushMsg pushMsg = new PushMsg();
                pushMsg.setId(UIDUtil.setUid(baseId, i++));
                pushMsg.setMsgname(msgName + meeting.getTopic());
                pushMsg.setUserid(userId);
                pushMsg.setRspId(rspId);
                pushMsg.setAuthorid(userId);
                pushMsg.setContent(JSONUtils.toJSONString(param));
                pushMsg.setTasktype(workTaskType);
                pushMsg.setObjid(meeting.getId());
                pushMsg.setMsgtype(9); // 消息重要程度
                pushMsg.setMsgstatus(0);
                pushMsg.setIsdeleted(DataIsDeleteEnum.NORMAL.getCode());
                pushMsg.setCreatedby(currentUserId);
                pushMsg.setCreateddate(date);
                pushMsg.setShouldSendDate(new Date());
                pushMsgs.add(pushMsg);
            }
        }

        if (!pushMsgs.isEmpty())
            pushMsgDAO.insertBatch(pushMsgs);
        if (!workTasks.isEmpty())
            workTaskDAO.insertBatch(workTasks);
    }

    private List<Users> getUserByOrgId(String orgCode) {
        try {
            Organization company = orgDAO.selectCompanyByCode(orgCode);
            DictionaryItems dictionaryItems = dictionaryItemsDAO.selectByTextAndDictCode(company.getOrgfname(), "MEETING_AUDIT_SCOPE");

            List<UsersIdAndHrIdDTO> userHrList = usersDAO.selectUserIdByHrIds(Arrays.asList(dictionaryItems.getDescription().split(",")));
            List<String> roleIdList = roleDAO.selectIdsByRoleType(RoleTypeEnum.AUDIT.getCode());

            List<Users> userList = new ArrayList<>(10);
            for (UsersIdAndHrIdDTO temp : userHrList) {
                Users user = new Users();
                user.setId(temp.getUserId());
                user.setCurrentRoleId(roleIdList.get(0));
                userList.add(user);
            }
            return userList;
        } catch (Exception e) {
            logger.error("未查询到会议所在组织对应的稽核人员：orgCode=" + orgCode);
            return null;
        }
    }

    /**
     * 生成会议归档的短信提醒
     *
     * @param meeting
     * @param currDate
     */
    private void genMsgForHoldk(Meetings meeting, Date currDate, String userId) {
        //当前会议是否是线上会议且是主流程
        Map<String, Object> params = new HashMap<>();
        if (MEETING_TYPE_ONLINE.equals(meeting.getChannelType()) && PROCESS_TYPE_MAIN.equals(meeting.getDistributionType())) {
            List<String> meetingIds = meetingsDAO.selectMeetingIdsByMainProcessId(meeting.getId());
            params.put("meetingIds", meetingIds);
        } else {
            params.put("meetingid", meeting.getId());
        }

        if (NEED_RECORD_YES.equals(meeting.getNeedRecord())) {
            DictionaryItems dictionaryItems = dictionaryItemsDAO.selectByCodeAndDictCode(HOLD_MEETING_UNDERLINE, HOLD_MEETING);
            MeetingConventioneerDTO meetingRatio = conventioneerDAO.selectSubmitRatio(params);
            if (meetingRatio.getRatio() < Double.valueOf(dictionaryItems.getItemtext())) {
                logger.info("会议记录提交人数不达标，不足以归档");
                return;
            }
        }

        // 查询短信设置信息
        MeetingMsgSetting meetingMsgSetting = meetingMsgSettingDAO.selectByObjTypeAndObjId("01", meeting.getId());
        if (null == meetingMsgSetting) {
            return;
        }

        // 发送短信
        List<UserRoleForMeDTO> leaderIds = usersDAO.selectUserPMIdsByOrgid(meeting.getOrgid(), RoleTypeEnum.WORKER.getCode());
        List<PushMsg> pushMsgs = new ArrayList<>(10);
        boolean isMeeting = 40 == meeting.getType() ? false : true;
        Map<String, Object> param = new HashMap<>();
        param.put("txt_businessType", isMeeting ? "会议" : "党课");
        param.put("txt_businessTheme", meeting.getTopic());
        param.put("txt_action", "归档");
        param.put("txt_businessModule", "三会一课");
        param.put("rspKey", "rspId003");
        //加载短信静默时间配置
        Date date_rspId003 = comUtils.getNoticDate("rspId003");
        for (UserRoleForMeDTO temp : leaderIds) {
            PushMsg msg = new PushMsg();

            msg.setId(UIDUtil.getUID());
            msg.setMsgname((isMeeting ? "【会议归档】" : "【党课归档】") + meeting.getTopic());
            param.put("phoneNum", temp.getTelephones());
            msg.setContent(JSONUtils.toJSONString(param));
            msg.setUserid(temp.getUserid());
            msg.setAuthorid(userId);
            msg.setTasktype(ConvertUtil.meetingTypeToTaskType(meeting.getType()));
            msg.setObjid(meeting.getId());
            msg.setMsgstatus(0);
            msg.setSenddate(null);
            msg.setIsdeleted(0);
            msg.setCreatedby(userId);
            msg.setCreateddate(date_rspId003);
            msg.setRspId(String.valueOf(param.get("rspKey")));
            pushMsgs.add(msg);
        }

        try {
            // 更新短信设置表最近执行时间
            meetingMsgSetting.setModifiedBy(userId);
            meetingMsgSetting.setModifiedDate(currDate);
            meetingMsgSetting.setLastHoldNoticeTime(currDate);
            meetingMsgSettingDAO.updateByPrimaryKey(meetingMsgSetting);

            // 捕获异常并打印错误日志，不影响正常的提交会议记录流程
            if (CollectionUtils.isNotEmpty(pushMsgs)) {
                pushMsgDAO.insertBatch(pushMsgs);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> createMinutesByMeetingId(String meetingId) throws GeneralException {
        Meetings meetings = meetingsDAO.selectByPrimaryKey(meetingId);

        List<MeetingConventioneerDTO> conventioneerList = conventioneerDAO.selectMinutesByMeetingId(meetingId);

        int joinCount = 0;  //实际参会人数
        String absPersons = ""; //缺席人
        int totalFormal = 0;    //应到正式党员
        int totalGet = 0;   //应到预备党员
        int joinFormal = 0; //实到正式党员
        int joinGet = 0;    //实到预备党员

        for (MeetingConventioneerDTO conventioneer : conventioneerList) {
            if (conventioneer.getIsjoin() == 1) {
                //当前党员实际参会
                joinCount++;

                //应到党员数
                switch (conventioneer.getStage()) {
                    case 1:
                        totalFormal++;
                        joinFormal++;
                        break;
                    case 2:
                        totalGet++;
                        joinGet++;
                        break;
                }
            } else {
                //当前党员未参会;
                //缺勤人
                absPersons.concat(conventioneer.getUsername()).concat("（").concat(conventioneer.getReason()).concat("）、");

                //应到党员数
                switch (conventioneer.getStage()) {
                    case 1:
                        totalFormal++;
                        break;
                    case 2:
                        totalGet++;
                        break;
                }
            }
        }

        if (absPersons.length() > 0) {
            absPersons = absPersons.substring(0, absPersons.length() - 1);
        }

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");

        Map<String, Object> resultFTL = new HashMap<>();
        resultFTL.put("topic", StringUtils.isBlank(meetings.getTopic()) ? "" : meetings.getTopic());
        resultFTL.put("meetingType", meetings.getType());
        resultFTL.put("meetingTime", df.format(meetings.getStarttime()).concat(" 至 ").concat(df.format(meetings.getEndtime())));
        resultFTL.put("address", StringUtils.isBlank(meetings.getAddress()) ? "" : meetings.getAddress());
        resultFTL.put("meHost", StringUtils.isBlank(meetings.getMeHost()) ? "" : meetings.getMeHost());
        resultFTL.put("absPerson", absPersons);
        resultFTL.put("totalCount", conventioneerList.size());
        resultFTL.put("totalFormal", totalFormal);
        resultFTL.put("totalGet", totalGet);
        resultFTL.put("joinCount", joinCount);
        resultFTL.put("joinFormal", joinFormal);
        resultFTL.put("joinGet", joinGet);
        resultFTL.put("attendances", StringUtils.isBlank(meetings.getAttendances()) ? "" : meetings.getAttendances());
        if (StringUtils.isNotBlank(meetings.getRecorder())) {
            Users recorder = usersDAO.selectByPrimaryKey(meetings.getRecorder());
            resultFTL.put("recorder", recorder.getUsername());
        }
        return resultFTL;
    }

    /**
     * 提交待计划会议时的操作
     *
     * @param meeting
     * @param userList
     * @param sysDate
     * @param type
     * @param workTaskType
     */
    @Override
    public void updateWorkTask(Meetings meeting, List<UserRoleForMeDTO> userList, Date sysDate, Integer type, Integer workTaskType, String currUserId) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(userList)) {
            List<WorkTask> workTaskList = new ArrayList<>(10);
            for (UserRoleForMeDTO temp : userList) {
                // 生成待办任务
                WorkTask workTask = new WorkTask();
                workTask.setId(UIDUtil.getUID());
                workTask.setObjid(meeting.getId());
                workTask.setSubobjid(null);
                workTask.setTaskname(meeting.getTopic());
                workTask.setSummary(meeting.getDescription());
                workTask.setUserid(temp.getUserid());
                workTask.setTasktype(workTaskType);
                workTask.setType(type);
                workTask.setTaskstatus(Integer.valueOf(0));
                workTask.setIsdeleted(DataIsDeleteEnum.NORMAL.getCode());
                workTask.setCreatedby(currUserId);
                workTask.setCreateddate(sysDate);
                workTask.setModifiedby(null);
                workTask.setModifieddate(null);
                workTask.setUserRole(temp.getRoleid());
                workTaskList.add(workTask);
            }
            workTaskDAO.insertBatch(workTaskList);
        }
    }

    @Override
    public LinkedHashMap<String, Map<String, Map<Integer, MeetingsCountDTO>>> countMeByBranchAndType(VMeetingsSummaryListSearchBean vMeetingsSummaryListSearchBean) {
        Map<String, Object> params = new HashMap<>(16);
        params.put("type", vMeetingsSummaryListSearchBean.getType());
        params.put("starttime", vMeetingsSummaryListSearchBean.getStarttime());
        params.put("endtime", vMeetingsSummaryListSearchBean.getEndtime());
        params.put("orgname", vMeetingsSummaryListSearchBean.getOrgname());
        params.put("topic", vMeetingsSummaryListSearchBean.getTopic());
        params.put("isDeleted", vMeetingsSummaryListSearchBean.getIsDeleted());
        params.put("status", vMeetingsSummaryListSearchBean.getStatus());
        params.put("auditState", vMeetingsSummaryListSearchBean.getAuditState());
        params.put("iserror", vMeetingsSummaryListSearchBean.getIserror());
        if (null != vMeetingsSummaryListSearchBean.getIsplan() && vMeetingsSummaryListSearchBean.getIsplan() > -1) {
            params.put("isplan", vMeetingsSummaryListSearchBean.getIsplan());
        }
        if (null != vMeetingsSummaryListSearchBean.getOrgCodeTerm())
            params.put("orgCodeTerm", vMeetingsSummaryListSearchBean.getOrgCodeTerm());
        //数据规则
        if (null != vMeetingsSummaryListSearchBean.getCurrentOrgCode())
            params.put("currentOrgCode", vMeetingsSummaryListSearchBean.getCurrentOrgCode());
        Integer meType = vMeetingsSummaryListSearchBean.getType();

        List<MeetingsCountDTO> dataList = meetingsDAO.countMeByBranchAndType(params);
        Map<String, Map<String, Map<Integer, MeetingsCountDTO>>> comMap = new HashMap<>(16);
        List<Organization> orgList = new ArrayList<>(10);
        for (MeetingsCountDTO data : dataList) {
            String comName = data.getComName();

            Organization org = new Organization();
            org.setOrgfname(comName);
            org.setOrdernum(data.getOrdernum());
            orgList.add(org);

            Map<String, Map<Integer, MeetingsCountDTO>> branchMap;
            if (comMap.containsKey(comName)) {
                branchMap = comMap.get(comName);
            } else {
                branchMap = new HashMap<>(16);
            }

            String branchName = data.getBranchName();
            Map<Integer, MeetingsCountDTO> typeMap;
            if (branchMap.containsKey(branchName)) {
                typeMap = branchMap.get(branchName);
            } else {
                typeMap = new HashMap<>(16);
            }

            if (null == data.getMeType()) {
                if (null == meType) {
                    typeMap.put(10, data);
                    typeMap.put(20, data);
                    typeMap.put(30, data);
                    typeMap.put(40, data);
                } else {
                    typeMap.put(meType, data);
                }
            } else {
                Integer mType = data.getMeType();
                if (null == meType) {
                    setTypeMap(typeMap, mType, data, 10);
                    setTypeMap(typeMap, mType, data, 20);
                    setTypeMap(typeMap, mType, data, 30);
                    setTypeMap(typeMap, mType, data, 40);
                } else {
                    typeMap.put(data.getMeType(), data);
                }
            }
            branchMap.put(branchName, typeMap);

            comMap.put(comName, branchMap);
        }
        Collections.sort(orgList, new Comparator<Organization>() {
            @Override
            public int compare(Organization o1, Organization o2) {
                return o1.getOrdernum().compareTo(o2.getOrdernum());
            }
        });
        LinkedHashMap<String, Map<String, Map<Integer, MeetingsCountDTO>>> result = new LinkedHashMap<>(16);
        for (Organization temp : orgList) {
            result.put(temp.getOrgfname(), comMap.get(temp.getOrgfname()));
        }
        return result;
    }

    private void setTypeMap(Map<Integer, MeetingsCountDTO> typeMap, Integer mType, MeetingsCountDTO data, Integer meType) {
        if (Integer.valueOf(meType).equals(mType)) {
            MeetingsCountDTO dto;
            if (typeMap.containsKey(meType)) {
                dto = typeMap.get(meType);
                dto.setCountNum(dto.getCountNum() + data.getCountNum());
            } else {
                dto = new MeetingsCountDTO();
                dto.setComName(data.getComName());
                dto.setBranchName(data.getBranchName());
                dto.setCountNum(data.getCountNum());
                typeMap.put(meType, dto);
            }
        } else {
            if (!typeMap.containsKey(meType)) {
                MeetingsCountDTO dto = new MeetingsCountDTO();
                dto.setComName(data.getComName());
                dto.setBranchName(data.getBranchName());
                dto.setCountNum(0);
                typeMap.put(meType, dto);
            }
        }
    }

    @Override
    public void relatedTasksSaveAndUpdate(List<String> amtIds, String meetingId) {
        taskSV.relatedTasksSaveAndUpdate(amtIds,meetingId,"0");
    }

    @Override
    public List<AnActivityTaskRelationship> getRelatedTasks(String id) {
        return anActivityTaskRelationshipMapper.selectListByMeetingIdAndType(id,"0");
    }

    @Override
    public int passToExamine(String meetingId, Users user) throws GeneralException {
        Meetings meetings = meetingsDAO.selectByPrimaryKey(meetingId);
        meetings.setModifiedby(user.getId());
        meetings.setModifieddate(new Date());
        if (MeetingNewStatusEnum.SCHEMEREVIEW.getCode().equals(meetings.getStatus())){
            meetings.setStatus(MeetingNewStatusEnum.FILLIN.getCode());
            //todo 会议通知参会者  这里应该是oa审批接口回调之后去做
        }else if (MeetingNewStatusEnum.TOEXAMINE.getCode().equals(meetings.getStatus())){
            meetings.setStatus(MeetingNewStatusEnum.FINISHED.getCode());
        }else {
            throw new GeneralException("只能对状态为”方案审核“,“资料审核”的活动进行操作");
        }
        Meetings meetings1=this.meetingsDAO.selectByPrimaryKey(meetings.getId());
        //
        if(StringUtils.isBlank(meetings.getModerator())){
            meetings.setModerator(meetings1.getModerator());
        }
        if(meetings.getActivityCount()==null){
            meetings.setActivityCount(meetings1.getActivityCount());
        }
        if(StringUtils.isBlank(meetings.getAddress())){
            meetings.setAddress(meetings1.getAddress());
        }
        if(StringUtils.isBlank(meetings.getRecordContent())){
            meetings.setRecordContent(meetings1.getRecordContent());
        }
        return meetingsDAO.updateByPrimaryKeySelective(meetings);
    }

    @Override
    public int complete(String meetingId, Users user) throws GeneralException {
        //获取当前活动
        Meetings meetings = meetingsDAO.selectByPrimaryKey(meetingId);
        if (MeetingNewStatusEnum.FILE.getCode().equals(meetings.getStatus())){
            //归档
            meetings.setId(meetingId);
            meetings.setModifiedby(user.getId());
            meetings.setModifieddate(new Date());
            meetings.setStatus(MeetingNewStatusEnum.FINISHED.getCode());
            Meetings meetings1=this.meetingsDAO.selectByPrimaryKey(meetings.getId());
            //
            if(StringUtils.isBlank(meetings.getModerator())){
                meetings.setModerator(meetings1.getModerator());
            }
            if(meetings.getActivityCount()==null){
                meetings.setActivityCount(meetings1.getActivityCount());
            }
            if(StringUtils.isBlank(meetings.getAddress())){
                meetings.setAddress(meetings1.getAddress());
            }
            if(StringUtils.isBlank(meetings.getRecordContent())){
                meetings.setRecordContent(meetings1.getRecordContent());
            }
            return meetingsDAO.updateByPrimaryKeySelective(meetings);
        }else {
            throw new GeneralException("只能对状态为”归档”的活动进行操作");
        }
    }

    @Override
    public int approvaProcessResult(String meetingId, Integer businessType, List<Attachments> attachmentsList) throws GeneralException {
//        Meetings meetings = meetingsDAO.selectByPrimaryKey(meetingId);
        log.info("OA审批更新，参数：meetingId={}, businessType={}, 附件：{}",
                meetingId, businessType, JSON.toJSONString(attachmentsList));
        Meetings update = meetingsDAO.selectByPrimaryKey(meetingId);
        update.setId(meetingId);
        update.setModifiedby("OA");
        update.setModifieddate(new Date());
        if (MeetingNewStatusEnum.SCHEMEREVIEW.getCode().equals(businessType)) {
            // 当前为活动方案审批，下一步为填写活动记录
            update.setStatus(MeetingNewStatusEnum.FILLIN.getCode());
            // 生成通知参会人信息
            generateParticipantNotificationInformation(meetingId);
        } else if (MeetingNewStatusEnum.TOEXAMINE.getCode().equals(businessType)) {
            // 当前为活动记录审批，下一步为结束
            update.setStatus(MeetingNewStatusEnum.FINISHED.getCode());
        }else {
            throw new GeneralException("只能对状态为”方案审核“,“资料审核”的活动进行操作");
        }
        //保存附件
        attachmentsDAO.insertCollect(attachmentsList);
        return meetingsDAO.updateByPrimaryKeySelective(update);
    }

    private void generateParticipantNotificationInformation(String meetingId) {
        log.info("生成参会人通知消息");
        List<Attendances> attendances = attendancesDAO.selectByMeetingId(meetingId, true, null, null, null);
        List<Map<String, String>> collect = attendances.stream().map(item -> {
            Map<String, String> map = new HashMap<>();
            map.put("personPhone", item.getPersonPhone());
            map.put("personName", item.getPersonName());
            return map;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)){
        log.info("生成会议通知：{}", JSON.toJSONString(collect));
        //生成会议通知
            Meetings meetings = meetingsDAO.selectByPrimaryKey(meetingId);
            Msg msg = new Msg();
            msg.setId(UUID.randomUUID().toString().replace("-", ""));
            msg.setIsdeleted(0);
            msg.setCreatedby("system");
            msg.setCreateddate(new Date());
            msg.setModifiedby("system");
            msg.setModifieddate(new Date());
            msg.setScheduleSendTime(new Date());
            JSONObject content = new JSONObject();
            content.put("meetingName",meetings.getTopic());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formattedTime = sdf.format(meetings.getStarttime());
            content.put("time", formattedTime);
            content.put("location",meetings.getAddress());
            List<String> phoneList = collect.stream()
                    .map(m -> m.get("personPhone"))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            content.put("receivers",phoneList);
            List<String> nameList = collect.stream()
                    .map(m -> m.get("personName"))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            content.put("receivers",phoneList);
            content.put("info","活动通知："+nameList);
            msg.setContent(content.toJSONString());
            msg.setMsgType(MessageTypeEnum.MEETING_NOTIFICATION.getCode());
            msg.setSendChanner(2); // 粤政易
            msg.setSendState(0); // 未发送
            msg.setReceiverOrgId(meetings.getOrgid());
            msg.setReceiverOrgName(meetings.getOrgname());
            msgDao.insert(msg);
        }
    }

    /**
     * 获取组织生活统计
     *
     * @param codestr
     * @return
     */
    @Override
    public List<OrgLifeStatistics> getOrgLifeStatistics(String codestr) {

        return meetingsDAO.getOrgLifeStatistics(codestr);
    }

    @Override
    public Integer getActivityPeriod(String orgId, String meetingType, String year, String studyType) {
        return meetingsDAO.selectActivityPeriod(orgId, meetingType,year, studyType);
    }
}
