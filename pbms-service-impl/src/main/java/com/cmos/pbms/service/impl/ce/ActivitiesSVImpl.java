package com.cmos.pbms.service.impl.ce;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.exception.SystemFailureException;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.an.AnActivityTaskRelationship;
import com.cmos.pbms.beans.ce.Activities;
import com.cmos.pbms.beans.ce.ActivityForAPP;
import com.cmos.pbms.beans.ce.Actors;
import com.cmos.pbms.beans.common.Attachments;
import com.cmos.pbms.beans.common.PushMsg;
import com.cmos.pbms.beans.common.WorkTask;
import com.cmos.pbms.beans.dto.AuditActivitiesListDTO;
import com.cmos.pbms.beans.dto.BpBranchActivityDTO;
import com.cmos.pbms.beans.dto.IndentationTargetContentDTO;
import com.cmos.pbms.beans.dto.UsersIdAndHrIdDTO;
import com.cmos.pbms.beans.enums.*;
import com.cmos.pbms.beans.pm.Organization;
import com.cmos.pbms.beans.sys.DictionaryItems;
import com.cmos.pbms.beans.sys.ReviewLog;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.dao.an.AnActivityTaskRelationshipMapper;
import com.cmos.pbms.dao.ce.ActivitiesDAO;
import com.cmos.pbms.dao.ce.ActorsDAO;
import com.cmos.pbms.dao.common.AttachmentsDAO;
import com.cmos.pbms.dao.common.PushMsgDAO;
import com.cmos.pbms.dao.common.WorkTaskDAO;
import com.cmos.pbms.dao.dd.AnswererDAO;
import com.cmos.pbms.dao.dd.SubjectDAO;
import com.cmos.pbms.dao.pm.OrganizationDAO;
import com.cmos.pbms.dao.sys.*;
import com.cmos.pbms.iservice.an.AnTaskSV;
import com.cmos.pbms.iservice.ce.IActivitiesSV;
import com.cmos.pbms.iservice.common.IPushMsgSV;
import com.cmos.pbms.iservice.me.IMeetingsSV;
import com.cmos.pbms.iservice.sys.IFootPointSV;
import com.cmos.pbms.iservice.sys.ISysUserScoreDtlSV;
import com.cmos.pbms.service.common.ComUtils;
import com.cmos.pbms.service.impl.sys.ScoreOperateUtils;
import com.cmos.pbms.utils.CacheServiceUtil;
import com.cmos.pbms.utils.DateUtil;
import com.cmos.pbms.utils.MockPageUtil;
import com.cmos.pbms.utils.UIDUtil;
import com.cmos.pbms.utils.constants.PbmsConstants;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Service(group = "pbms", retries = -1)
public class ActivitiesSVImpl implements IActivitiesSV {

    private static final Logger logger = LoggerFactory.getLogger(ActivitiesSVImpl.class);

    @Autowired
    private ActivitiesDAO activitiesDAO;

    @Autowired
    private ActorsDAO actorsDAO;

    @Autowired
    private AnswererDAO answererDAO;

    @Autowired
    private WorkTaskDAO workTaskDAO;

    @Autowired
    private PushMsgDAO pushMsgDAO;

    @Autowired
    private OrganizationDAO organizationDAO;

    @Autowired
    private SubjectDAO subjectDAO;

    @Autowired
    private ParamDao paramDao;

    @Autowired
    private UsersDAO usersDAO;

    @Autowired
    private AttachmentsDAO attachmentsDAO;

    @Autowired
    private RoleDAO roleDAO;

    @Autowired
    private IFootPointSV footPointSV;

    @Autowired
    private ISysUserScoreDtlSV sysUserScoreDtlService;

    @Autowired
    private ComUtils comUtils;

    @Autowired
    private DictionaryItemsDAO dictionaryItemsDAO;

    @Autowired
    private ReviewLogDAO reviewLogDAO;

    @Autowired
    private IMeetingsSV meetingsSV;

    @Autowired
    private IPushMsgSV pushMsgSV;

    @Resource
    private AnTaskSV taskSV;

    @Resource
    private AnActivityTaskRelationshipMapper anActivityTaskRelationshipMapper;

    @Override
    @Transactional
    public int deleteByPrimaryKey(String id, String currentuserid) {
        Activities record = activitiesDAO.selectBeanByPrimaryKey(id);
        int result = activitiesDAO.deleteByPrimaryKey(id);

        //删除待办
        workTaskDAO.deleteByObjid(id);
        if (0 == result || record.getActstatus().equals(0) || record.getActstatus().equals(10)) { //草稿和发布中，是没有待办的
            return result;
        }
        //发送消息--非草稿状态
        if (record.getActtype().equals(2)) {    //非招募(指派)--活动组员发送
            List<Map<String, Object>> userlist = actorsDAO.selectByActivityId(id);
            for (Map<String, Object> user : userlist) {
                String userid = user.get("userid").toString();
                String phoneNum = user.get("telephones").toString();
//                String title = "【指派】" + record.getActname();
                insertPushMsg(record.getId(), record.getActname(), userid, phoneNum, userid, 1);
            }
        } else {    //招募--1、待办发出去，给收到待办的人发 2、没有发待办，给招募范围的人发
            long currentTime = (new Date()).getTime();
            long reedate = record.getRecendtime() == null ? 0 : record.getRecendtime().getTime();
//            String title = "【招募】" + record.getActname();
            if (record.getActstatus().equals(40) && currentTime > reedate) { //当前时间大于招募结束时间 已经发了待办
                //获取招募类的范围人员---给已经参加待办的人员发送待办
                List<Map<String, Object>> userlist = actorsDAO.selectByActivityId(id);
                for (Map<String, Object> anUserlist : userlist) {
                    insertPushMsg(record.getId(), record.getActname(), anUserlist.get("userid").toString(), anUserlist.get("telephones").toString(), currentuserid, 2);
                }
            } else {
                //获取招募类的范围人员
                List<Users> userlist;
                if (null != record.getTimelinessType() && record.getTimelinessType().isEmpty() && "01".equals(record.getTimelinessType())) {
                    //实时
                    String codestr = organizationDAO.selectByPrimaryKey(record.getRecorgid()).getCodestr();
                    userlist = usersDAO.selectByOrgCodeStr(codestr);
                } else {
                    //补录
                    userlist = usersDAO.selectByActivity(record.getId());
                }
                for (Users anUserlist : userlist) {
                    insertPushMsg(record.getId(), record.getActname(), anUserlist.getId(), anUserlist.getTelephones(), currentuserid, 2);
                }
            }
        }
        //最后删除活动参与者
        actorsDAO.deleteByActivityId(id);
        return result;
    }

    private void insertPushMsg(String objid, String title, String userid, String phoneNum, String currentuserid, int type) {
        // 生成消息推送内容
        Map<String, Object> param = new HashMap<>(16);
        param.put("txt_businessType", "主题党日");
        param.put("txt_businessTheme", title);
        param.put("txt_action", "取消");
        param.put("txt_businessModule", "主题党日");
        param.put("rspKey", "rspId005");
        Date rspId_rspId005 = comUtils.getNoticDate("rspId005");
        param.put("phoneNum", phoneNum);

        PushMsg pushMsg = new PushMsg();
        String pid = UIDUtil.getUID();
        pushMsg.setId(pid);
        pushMsg.setMsgname("【主题党日取消】" + title);
        pushMsg.setRspId(String.valueOf(param.get("rspKey")));
//        pushMsg.setContent("活动已取消：您参与的活动“" + title + "”，已取消。");
        pushMsg.setContent(JSONUtils.toJSONString(param));
        pushMsg.setUserid(userid);
        pushMsg.setAuthorid(currentuserid);
        pushMsg.setTasktype(10);
        pushMsg.setObjid(objid);
        pushMsg.setMsgtype(type);
        pushMsg.setMsgstatus(0);
        pushMsg.setCreatedby(currentuserid);
        pushMsg.setCreateddate(new Date());
        pushMsg.setShouldSendDate(rspId_rspId005);
        pushMsgDAO.insertSelective(pushMsg);
    }

    @Override
    public int insert(Activities record) {
        return activitiesDAO.insert(record);
    }

    @Override
    public int insertSelective(Activities record, Users currUser) throws GeneralException {
        //是否需要创建短信
        Boolean createMsg = false;
        //创建发布短信
        Boolean msgOfPublish = false;
        if (record.getTimelinessType().equals("01") && record.getActtype() == 2 && record.getActstatus() == 10) {
            createMsg = true;
            msgOfPublish = true;

        }
        //关联任务
        saveAndUpdateRelatedTasksByIds(record);


        if (record.getActstatus() == 10) {
            insertPublishLog(record, currUser);
        }

        updateActors(record, createMsg, msgOfPublish, record.getCreatedby());
        return activitiesDAO.insertSelective(record);
    }

    private void saveAndUpdateRelatedTasksByIds(Activities record) {
        List<String> taskIds = new ArrayList<>();
        if (record.getRelatedTasksIds() != null) {
            String[] ids = record.getRelatedTasksIds().split(",");
            taskIds = Arrays.asList(ids);
        }
        taskSV.relatedTasksSaveAndUpdate(taskIds , record.getId(), "3");

    }

    @Override
    public int getActivitiesCountByOrgid(String orgid, String year) {
        return activitiesDAO.selectActivitiesCountByOrgid(orgid, year);
    }

    @Override
    public int getCeParticipatorCountByOrgid(String orgid, String year) {
        return activitiesDAO.selectCeParticipatorCountByOrgid(orgid, year);
    }

    @Override
    public Map<String, Object> getByPrimaryKey(String id) {
        Map<String, Object> activities = activitiesDAO.selectByPrimaryKey(id);
        List<Map<String, Object>> actors = actorsDAO.selectByActivityId(id);
        if (actors != null && !actors.isEmpty()) {
            String object = JSON.toJSONString(actors);
            activities.put("actors", object);
        }
        return activities;
    }

    @Override
    public Map<String, Object> getFullDetailById(String id) {
        Map<String, Object> re = activitiesDAO.selectByPrimaryKey(id);

        if (re.get("themepic") != null && !Strings.isNullOrEmpty(re.get("themepic").toString())) {
            String themepic = re.get("themepic").toString();
            String[] s1 = themepic.split("/");
            String[] s2 = s1[s1.length - 1].split("\\.");
            String aid = s2[0];
            Attachments attachment = attachmentsDAO.selectByPrimaryKey(aid);
            if (attachment != null) {
                re.put("attachname", attachment.getAttname());
                re.put("attachsize", attachment.getFilesize());
            }

        }

        //查关联任务
        List<AnActivityTaskRelationship> relatedTasks = anActivityTaskRelationshipMapper.selectListByMeetingIdAndType(id, "3");
        re.put("relatedTasks", relatedTasks);

        // 活动组员
        re.put("actors", actorsDAO.selectByActivityId(id));
        re.put("ce_att", attachmentsDAO.selectListByObjIdType(String.valueOf(re.get("id")), AttachTypeEnum.CE_ATT.getType()));
        re.put("ce_hold", attachmentsDAO.selectListByObjIdType(String.valueOf(re.get("id")), AttachTypeEnum.CE_HOLD.getType()));

        return re;
    }

    @Override
    public PageInfo<Activities> getByPageWithParam(Integer pageNum, Integer pageSize, Map<String, Object> params) {

        String sponsor = (String) params.get("sponsor");
        if (StringUtils.isNoneBlank(sponsor)) {
            Organization organization = organizationDAO.selectByPrimaryKey(sponsor);
            if (null != organization)
                params.put("orgCodeStr", organization.getCodestr());

        }
        PageHelper.startPage(pageNum, pageSize);
        List<Activities> list = activitiesDAO.selectByCondition(params);
        return new PageInfo<>(list);
    }

    @Override
    public PageInfo<ActivityForAPP> getAllForAPP(Integer pageNum, Integer pageSize, String title, String orgid, String userid) {
        Organization org = organizationDAO.selectByPrimaryKey(orgid);
        List<ActivityForAPP> list = activitiesDAO.selectAllForAPP(org == null ? "" : org.getCodestr(), title, userid);
        //  按状态排序：招募中-》进行中-》即将开始-》已结束升序  ; 根据开始时间进行降序排列
        String[] strname = {"sstatus", "begintime"};
        boolean[] strsort = {true, false};
        return MockPageUtil.sort(list, pageNum, pageSize, strname, strsort);
    }

    @Override
    public Map<String, Object> getByIdForAPP(String id, String currentuserid) {
        Map<String, Object> activities = activitiesDAO.selectByIdForAPP(id);
        if (null == activities) {
            return null;
        }
        List<Map<String, Object>> members = activitiesDAO.selectMemberById(id);
        activities.put("members", JSON.toJSONString(members));
        Actors actor = actorsDAO.selectByActUser(id, currentuserid);
        // acttype 1-招募/2-指派
        String acttype = activities.get("acttype").toString();
        String actstatus = activities.get("actstatus").toString();
        String isevaluate = activities.get("isevaluate") == null ? "" : activities.get("isevaluate").toString(); //0-不评价 1-评价isevaluate.equals("true")
        if (actor != null) {
            // 指派：查看评价（当前活动状态是“已结束”，且个人评价记录不为空时，且活动后是否评价活动组员为1）
            if ("2".equals(acttype) && "60".equals(actstatus) && "2".equals(actor.getStatus().toString()) && "1".equals(isevaluate)) { //已评价
                activities.put("viewevaluation", true);
            }
            // 招募 : 显示“取消报名”。活动状态为“招募中”的时候，且个人已报名时，显示按钮）
            if ("1".equals(acttype) && "30".equals(actstatus)) {
                activities.put("cancelparticipation", true);
            }
            activities.put("actorsid", actor.getId());
        } else {
            // 招募：报名参加 （显示“报名参加”。１、活动状态为“招募中”的时候  2、个人未报名时 3、报名人数还没到达招募人数 ，显示按钮。
            //获取已经报名的人数
            List<Map<String, Object>> actors = actorsDAO.selectByActivityId(id);
            int realnum = actors == null ? 0 : actors.size();
            int recnum = activities.get("recnum") == null ? 0 : Integer.parseInt(activities.get("recnum").toString());
            if ("1".equals(acttype) && "30".equals(actstatus)) {
                if (realnum < recnum) {
                    activities.put("participation", true);
                } else if (realnum == recnum) {
                    activities.put("participation", false);
                }
            }

        }

        activities.put("ce_att", attachmentsDAO.selectListByObjIdType(String.valueOf(activities.get("id")), AttachTypeEnum.CE_ATT.getType()));
        return activities;
    }

    @Override
    public int updateByPrimaryKeySelective(Activities record, Users currUser) throws GeneralException {
        //是否需要创建短信
        Boolean createMsg = false;
        //创建发布短信:true发布，false变更
        Boolean msgOfPublish = false;

        Activities activities = activitiesDAO.selectBeanByPrimaryKey(record.getId());

        if (record.getTimelinessType().equals("01") && record.getActtype() == 2 && record.getActstatus() == 10) {
            createMsg = true;
            if (activities.getActstatus() == 0) {
                msgOfPublish = true;
            }
        }

        //更新关联任务
        saveAndUpdateRelatedTasksByIds(record);


        if (record.getActstatus() == 10) {
            insertPublishLog(record, currUser);
        }
        updateActors(record, createMsg, msgOfPublish, record.getModifiedby());
        return activitiesDAO.updateByPrimaryKeySelective(record);
    }

    private void insertPublishLog(Activities activities, Users currUser) {
        ReviewLog reviewLog = new ReviewLog();
        reviewLog.setId(UIDUtil.getUID());
        reviewLog.setProcessType(ProcessTypeEnum.ACT_AUDIT.getCode());
        reviewLog.setObjectId(activities.getId());
        //6001( 1：提交稽核 2：稽核不通过 3：稽核通过)
        reviewLog.setOperationType(Integer.valueOf(1));
        reviewLog.setOperationDesc("发布活动");
        reviewLog.setOperationUserId(currUser.getId());
        reviewLog.setOperationUserName(currUser.getUsername());
        reviewLog.setOperationTime(activities.getModifieddate() == null ? activities.getCreateddate() : activities.getModifieddate());
        reviewLog.setRemark(currUser.getUsername().concat(currUser.getTelephones()).concat("，发布主题党日活动"));
        reviewLogDAO.insertSelective(reviewLog);
    }

    @Override
    public int updateByPrimaryKey(Activities record) {
        return activitiesDAO.updateByPrimaryKey(record);
    }

    @Override
    public String changeStatus(String actiSn) {
        JSONObject resultObj = ChangeFactory.changeStatus(activitiesDAO, subjectDAO, workTaskDAO, pushMsgDAO, organizationDAO, actorsDAO, answererDAO, usersDAO, paramDao, roleDAO, footPointSV, attachmentsDAO, sysUserScoreDtlService, actiSn, comUtils);
        return resultObj.toString();
    }

    @Override
    public int getUserActivitiesCount(String userid, String instatus) {
        return activitiesDAO.selectUserActivitiesCount(userid, instatus);
    }

    @Override
    public int getFinishActivitiesCount(String orgcode) {
        return activitiesDAO.selectFinishActivitiesCount(orgcode);
    }

    @Override
    public int getFinishActivitiesActorCount(String orgcode) {
        return activitiesDAO.selectFinishActivitiesActorCount(orgcode);
    }

    @Override
    public List<ActivityForAPP> searchActivitiesByTitle(String title, String codeStr, String userId) {
        return activitiesDAO.selectAllForAPP(codeStr, title, userId);
    }

    //添加活动的参与者
    private void updateActors(Activities record, Boolean createMsg, Boolean msgOfPublish, String msgAuthor) throws GeneralException {
        String createdby = record.getCreatedby() == null ? record.getModifiedby() : record.getCreatedby();
        Date createddate = record.getCreateddate() == null ? record.getModifieddate() : record.getCreateddate();

        List<Actors> alist = new ArrayList<>(16);
        String[] userids = record.getActors().split(",");
        if (!Strings.isNullOrEmpty(record.getActors()) && userids.length > 0) { //保存参与者
            Set<String> userIdSet = new HashSet<>(16);
            for (String userid : userids) {
                Actors actors = new Actors();
                String id = UIDUtil.getUID();
                actors.setId(id);
                actors.setActivityid(record.getId());
                actors.setStatus(0);
                actors.setUserid(userid);
                actors.setCreatedby(createdby);
                actors.setCreateddate(createddate);
                alist.add(actors);
                userIdSet.add(userid);
            }
            // add by jcm 2019-2-14 16:52:05 编号：9530 解决主题党日中，组长没有作为活动成员的问题。
            if (!userIdSet.contains(record.getLeader())) {
                Actors actors = new Actors();
                String id = UIDUtil.getUID();
                actors.setId(id);
                actors.setActivityid(record.getId());
                actors.setStatus(0);
                actors.setUserid(record.getLeader());
                actors.setCreatedby(createdby);
                actors.setCreateddate(createddate);
                alist.add(actors);
                userIdSet.add(record.getLeader());
            }
            actorsDAO.deleteByActivityId(record.getId());
            actorsDAO.insertBatch(alist);

            //批量生成短信
            if (createMsg) {
                String msgName = null;
                Integer msgtaskType = null;
                Map<String, Object> param = new HashMap<>();
                param.put("txt_businessTheme", record.getActname());
                param.put("txt_startTime", record.getActbegintime());
                param.put("txt_address", record.getAddr());
                if (msgOfPublish) {
                    //活动发布时短信
                    msgName = "【发布主题党日活动】".concat(record.getActname());
                    msgtaskType = PushMsgTaskTypeEnum.ACT_PUBLISH.getCode();
                    param.put("rspKey", "rspId046");
                } else {
                    //已发布活动变更时短信
                    msgName = "【变更主题党日活动】".concat(record.getActname());
                    msgtaskType = PushMsgTaskTypeEnum.ACT_UPDATE.getCode();
                    param.put("rspKey", "rspId047");
                }
                //批量生成短信
                pushMsgSV.insertMsgInBatch(record.getId(), new ArrayList<>(userIdSet), msgAuthor, comUtils.getNoticDate(param.get("rspKey").toString()), msgName, 9, msgtaskType, param);
            }
        }
    }

    @Override
    public int holdAct(String id, String attDesc, Users currUser) throws GeneralException {
        Date currTime = new Date();

        Activities activities = activitiesDAO.selectBeanByPrimaryKey(id);
        //更新活动状态为待稽核
        activities.setActstatus(Integer.valueOf(55));
        activities.setAuditState(Integer.valueOf(1));
        activities.setAttDesc(attDesc);
        activities.setModifiedby(currUser.getId());
        activities.setModifieddate(currTime);
        activities.setSubAuditTime(currTime);
        activities.setSubAuditUser(currUser.getId());

        List<Users> userList = getUserByOrgId(activities.getSponsor());
        if (CollectionUtils.isEmpty(userList)) {
            logger.error("稽核员未配置，请联系管理员");
            throw new GeneralException("PBMS_ME_1066");
        }

        List<WorkTask> workTaskList = new ArrayList<>();
        for (Users user : userList) {
            WorkTask workTask = workTaskDAO.selectWorkTaskByObjIdAndTaskTypeAndType(id, WorkTaskTypeEnum.ACT_AUDIT.getCode(), WorkTaskTypeEnum.ACT_AUDIT.getCode(), user.getId(), user.getCurrentRoleId());
            if (null != workTask) {
                if (1 == workTask.getTaskstatus()) {
                    workTask.setTaskstatus(Integer.valueOf(0));
                    workTask.setModifiedby(currUser.getId());
                    workTask.setModifieddate(currTime);
                    workTaskDAO.updateByPrimaryKeySelective(workTask);
                }
            } else {
                workTask = new WorkTask();
                workTask.setId(UIDUtil.getUID());
                workTask.setObjid(id);
                workTask.setTaskname("【中台稽核】".concat(activities.getActname()));
                workTask.setUserid(user.getId());
                workTask.setTasktype(WorkTaskTypeEnum.ACT_AUDIT.getCode());
                workTask.setType(WorkTaskTypeEnum.ACT_AUDIT.getCode());
                workTask.setTaskstatus(Integer.valueOf(0));
                workTask.setIsdeleted(Integer.valueOf(0));
                workTask.setCreatedby(currUser.getId());
                workTask.setCreateddate(currTime);
                workTask.setUserRole(user.getCurrentRoleId());
                workTaskList.add(workTask);
            }
        }

        ReviewLog reviewLog = new ReviewLog();
        reviewLog.setId(UIDUtil.getUID());
        reviewLog.setProcessType(ProcessTypeEnum.ACT_AUDIT.getCode());
        reviewLog.setObjectId(id);
        //6001( 1：提交稽核 2：稽核不通过 3：稽核通过)
        reviewLog.setOperationType(Integer.valueOf(1));
        reviewLog.setOperationDesc("提交稽核");
        reviewLog.setOperationUserId(currUser.getId());
        reviewLog.setOperationUserName(currUser.getUsername());
        reviewLog.setOperationTime(currTime);
        reviewLog.setRemark(currUser.getUsername().concat(currUser.getTelephones()).concat("，提交至中台进行数据稽核"));
        reviewLogDAO.insertSelective(reviewLog);

        if (!workTaskList.isEmpty()) {
            workTaskDAO.insertBatch(workTaskList);
        }

        return activitiesDAO.updateByPrimaryKeySelective(activities);
    }

    private List<Users> getUserByOrgId(String orgId) {
        try {
            Organization company = organizationDAO.selectCompanyById(orgId);
            DictionaryItems dictionaryItems = dictionaryItemsDAO.selectByTextAndDictCode(company.getOrgfname(), "MEETING_AUDIT_SCOPE");

            List<UsersIdAndHrIdDTO> userHrList = usersDAO.selectUserIdByHrIds(Arrays.asList(dictionaryItems.getDescription().split(",")));
            List<String> roleIdList = roleDAO.selectIdsByRoleType(RoleTypeEnum.AUDIT.getCode());

            List<Users> userList = new ArrayList<>(10);
            for (UsersIdAndHrIdDTO temp : userHrList) {
                Users user = new Users();
                user.setId(temp.getUserId());
                user.setCurrentRoleId(roleIdList.get(0));
                userList.add(user);
            }
            return userList;
        } catch (Exception e) {
            logger.error("未查询到主题党日活动所在组织对应的稽核人员：orgId=" + orgId);
            return null;
        }
    }

    @Override
    public PageInfo<AuditActivitiesListDTO> getAuditList(Integer page, Integer limit, Date searchStartTime, Date searchEndTime, String actname, String orgId, Integer auditState, Date subTimeStart, Date subTimeEnd, Date auditTimeStart, Date auditTimeEnd, Users currUser) throws GeneralException {
        Calendar calendar = Calendar.getInstance();

        Map<String, Object> params = new HashMap<>();
        params.put("roleId", currUser.getCurrentRoleId());
        params.put("userId", currUser.getId());

        if (StringUtils.isNotBlank(actname)) params.put("actname", actname);
        if (StringUtils.isNotBlank(orgId)) params.put("orgId", orgId);
        if (searchStartTime != null) params.put("searchStartTime", searchStartTime);
        if (searchEndTime != null) {
            calendar.setTime(searchEndTime);
            calendar.add(Calendar.DATE, 1);
            params.put("searchEndTime", calendar.getTime());
        }
        if (auditState != null) params.put("auditState", auditState);
        if (subTimeStart != null) params.put("subTimeStart", subTimeStart);
        if (subTimeEnd != null) {
            calendar.setTime(subTimeEnd);
            calendar.add(Calendar.DATE, 1);
            params.put("subTimeEnd", calendar.getTime());
        }
        if (auditTimeStart != null) params.put("auditTimeStart", auditTimeStart);
        if (auditTimeEnd != null) {
            calendar.setTime(auditTimeEnd);
            calendar.add(Calendar.DATE, 1);
            params.put("auditTimeEnd", calendar.getTime());
        }

        PageHelper.startPage(page, limit);
        List<AuditActivitiesListDTO> result = activitiesDAO.selectAuditList(params);

        return new PageInfo<>(result);
    }

    @Override
    public int updateHoldDesc(String id, String attDesc, Users currUser) {
        Activities activities = new Activities();
        activities.setId(id);
        activities.setAttDesc(attDesc);
        activities.setModifiedby(currUser.getId());
        activities.setModifieddate(new Date());
        return activitiesDAO.updateByPrimaryKeySelective(activities);
    }

    @Override
    public PageInfo<BpBranchActivityDTO> getActivitiesByOrg(Integer pageNum, Integer pageSize, String orgId) {
        PageHelper.startPage(pageNum, pageSize);
        List<BpBranchActivityDTO> dataList = activitiesDAO.selectActivitiesByOrg(orgId);
        for (BpBranchActivityDTO temp : dataList) {
            List<Attachments> fileList = attachmentsDAO.selectListByObjIdType(temp.getId(), AttachTypeEnum.CE_HOLD.getType());
            List<Attachments> picList = new ArrayList<>(10);
            for (Attachments file : fileList) {
                if (isFilePic(file.getAttname())) {
                    picList.add(file);
                }
            }
            temp.setPicList(picList);
        }
        return new PageInfo<>(dataList);
    }

    private boolean isFilePic(String fileName) {
        String reg = ".+(.JPEG|.jpeg|.JPG|.jpg|.PNG|.png)$";
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(fileName.toLowerCase());
        return matcher.find();
    }

    @Override
    public int auditAct(String actId, Integer auditState, String auditReason, Users currUser) throws GeneralException {
        Date currTime = new Date();

        Activities activities = activitiesDAO.selectBeanByPrimaryKey(actId);
        //更新稽核意见
        activities.setAuditReason(auditReason);
        activities.setModifieddate(currTime);
        activities.setModifiedby(currUser.getId());

        if (auditState != 1) {
            activities.setAuditTime(currTime);
            activities.setAuditUser(currUser.getId());
            activities.setAuditState(auditState);

            ReviewLog reviewLog = new ReviewLog();
            reviewLog.setId(UIDUtil.getUID());
            // 9001( 1：提交稽核 2：稽核不通过 3：稽核通过)
            reviewLog.setOperationType(auditState);
            reviewLog.setOperationDesc(2 == auditState ? "稽核不通过" : "稽核通过");
            reviewLog.setOperationUserId(currUser.getId());
            reviewLog.setOperationUserName(currUser.getUsername());
            reviewLog.setOperationTime(currTime);
            reviewLog.setResuseReason(auditReason);
            reviewLog.setRemark("智慧中台已".concat(reviewLog.getOperationDesc()));
            reviewLog.setProcessType(ProcessTypeEnum.ACT_AUDIT.getCode());
            reviewLog.setObjectId(activities.getId());
            reviewLogDAO.insert(reviewLog);

            WorkTask workTask = new WorkTask();
            workTask.setObjid(actId);
            workTask.setType(WorkTaskTypeEnum.ACT_AUDIT.getCode());
            workTask.setTasktype(WorkTaskTypeEnum.ACT_AUDIT.getCode());
            workTask.setTaskstatus(Integer.valueOf(1));
            workTask.setModifiedby(currUser.getId());
            workTask.setModifieddate(currTime);
            workTaskDAO.doneByObjidAndTypeAndUser(workTask);
        }

        switch (auditState) {
            case 2:
                //稽核退回

                if (!CacheServiceUtil.checkNotAllow(PbmsConstants.sendPtTaskInfo_notify.concat(DateUtil.format(currTime, "yyyyMMdd")).concat(activities.getId()) + "submit")) {
                    // 短信提醒
                    meetingsSV.sendBackAuditMsg(currUser, activities.getId(), activities.getActname(), WorkTaskTypeEnum.ACT_AUDIT.getCode(), ProcessTypeEnum.ACT_AUDIT.getCode(), activities.getSubAuditUser());
                }
                break;

            case 3:
                //稽核通过
                try {
                    activities.setActstatus(Integer.valueOf(60));
                    //活动结束后，参加社区活动，添加积分
                    List<Map<String, Object>> userlist = actorsDAO.selectByActivityId(activities.getId());
                    String title = StringEscapeUtils.unescapeHtml3(activities.getActname());
                    ScoreOperateUtils.addScoreMap(userlist, title, "system", activities.getActtype() == 1 ? ScoreCodeEnum.PARTINACTIVITY_ZM : ScoreCodeEnum.PARTINACTIVITY_ZP);
                    //留痕
                    saveIndentation(activities, userlist);
                    //积分
                    saveScore(Integer.valueOf(50), activities, userlist, currTime);
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
                break;
        }

        return activitiesDAO.updateByPrimaryKey(activities);
    }

    private void saveScore(Integer objType, Activities activities, List<Map<String, Object>> userlist, Date timeStamp) {
        String objId;
        String ruleNo;
        objId = activities.getId();
        ruleNo = "50101";

        for (Map<String, Object> user : userlist) {
            try {
                sysUserScoreDtlService.addScoreInfo(objType, objId, ruleNo, user.get("userid").toString(), "system", timeStamp, null, null, null);
            } catch (ParseException e1) {
                logger.error(e1.getMessage(), e1);
            } catch (SystemFailureException e2) {
                logger.error(e2.getMessage(), e2);
            }
        }
    }

    /**
     * @方法名：saveOrgIndentation
     * @方法作用：处理社区活动痕迹
     * @方法参数：
     * @返回结果：
     * @作者：牛文钻
     * @日期：2018/9/13
     */
    private void saveIndentation(Activities act, List<Map<String, Object>> userlist) {

        //获取组织信息
        Organization organization = organizationDAO.selectByPrimaryKey(act.getSponsor());

        String themePic = getObjPic(act.getId());

        IndentationTargetContentDTO orgTargetContentDTO = new IndentationTargetContentDTO();
        orgTargetContentDTO.setFpTopic("组织“".concat(act.getActname()).concat("”"));
        orgTargetContentDTO.setObjTopic(act.getActname());
        orgTargetContentDTO.setThemepic(themePic);

        footPointSV.addFootPoint(Integer.valueOf(2), Integer.valueOf(20300), Integer.valueOf(20301), act.getId(), JSON.toJSONString(orgTargetContentDTO), organization.getId(), organization.getCodestr(), "组织", null, 0, "system");

        for (Map<String, Object> user : userlist) {

            IndentationTargetContentDTO targetContentDTO = new IndentationTargetContentDTO();
            targetContentDTO.setFpTopic("参加“".concat(organization.getOrgsname()).concat("”举办的").concat("“").concat(act.getActname()).concat("”活动"));
            targetContentDTO.setObjTopic(act.getActname());
            targetContentDTO.setThemepic(themePic);

            footPointSV.addFootPoint(Integer.valueOf(1), Integer.valueOf(10300), Integer.valueOf(10301), act.getId(), JSON.toJSONString(targetContentDTO), user.get("userid").toString(), organization.getCodestr(), "参加", null, 0, "system");
        }

    }

    private String getObjPic(String objId) {
        List<Attachments> attList = attachmentsDAO.selectListByObjId(objId);
        for (Attachments attachments : attList) {
            if (attachments.getAttname().endsWith(".jpg") || attachments.getAttname().endsWith(".jpeg") || attachments.getAttname().endsWith(".png")) {
                return attachments.getUrl();
            }
        }
        return "";
    }
}
