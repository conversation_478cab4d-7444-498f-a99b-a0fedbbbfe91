package com.cmos.pbms.service.impl.sk;

import com.alibaba.dubbo.config.annotation.Service;
import com.cmos.pbms.beans.sk.SkPushQuestions;
import com.cmos.pbms.dao.sk.SkPushQuestionsDAO;
import com.cmos.pbms.iservice.sk.ISkPushQuestionsSV;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @类名：SkPushQuestionsSVImpl
 * @类的作用：
 * @作者：牛文钻
 * @创建时间：2019/5/15
 */

@Service(group = "pbms", retries = -1)
public class SkPushQuestionsSVImpl implements ISkPushQuestionsSV {

    @Autowired
    private SkPushQuestionsDAO pushQuestionsDAO;

    @Override
    public int insert(SkPushQuestions record) {
        return pushQuestionsDAO.insert(record);
    }

    @Override
    public int updateByPrimaryKey(SkPushQuestions record) {
        return pushQuestionsDAO.updateByPrimaryKey(record);
    }

    @Override
    public List<SkPushQuestions> getAllStatusByPushId(String pushId) {
        return pushQuestionsDAO.selectAllStatusByPushId(pushId);
    }

    @Override
    public List<SkPushQuestions> getByPushId(String pushId) {
        return pushQuestionsDAO.selectByPushId(pushId);
    }

    @Override
    public List<SkPushQuestions> getByQuestionId(String questionId) {
        return pushQuestionsDAO.selectByQuestionId(questionId);
    }
}
