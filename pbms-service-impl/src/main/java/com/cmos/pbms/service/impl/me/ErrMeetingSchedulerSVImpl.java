package com.cmos.pbms.service.impl.me;

import com.alibaba.dubbo.config.annotation.Service;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.enums.MeetingStatusEnum;
import com.cmos.pbms.beans.enums.MeetingTypeEnum;
import com.cmos.pbms.beans.me.Meetings;
import com.cmos.pbms.dao.me.MeetingsDAO;
import com.cmos.pbms.iservice.me.IErrMeetingSchedulerSV;
import com.cmos.pbms.utils.NgTaskUtil;
import com.cmos.pbms.utils.constants.PbmsConstants;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @类名：ErrMeetingSchedulerSVImpl
 * @类的作用：
 * @作者：牛文钻
 * @创建时间：2019/3/15
 */
@Service(group = "pbms", retries = -1, async = true)
public class ErrMeetingSchedulerSVImpl implements IErrMeetingSchedulerSV {

    private static final Logger logger = LoggerFactory.getLogger(ErrMeetingSchedulerSVImpl.class);

    private static final String sys_user = "system";

    @Autowired
    private MeetingsDAO meetingsDAO;

    @Override
    public void handleMToBeNotified(String sn, Date currTime) {
        String rtnCode = "0";
        String rtnMsg = "检查三会一课异常的待计划及待通知状态（月度），异常调度检查，成功。";

        try {
            String type = MeetingTypeEnum.BRANCHLEADER.getCode().toString().concat(",").concat(MeetingTypeEnum.PARTYGROUP.getCode().toString());
            String status = MeetingStatusEnum.TOBEPLAN.getCode().toString().concat(",").concat(MeetingStatusEnum.TOBENOTIFY.getCode().toString());
            exceCheck(type, status, currTime, false);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            rtnCode = "-9999";
            rtnMsg = "检查三会一课异常的待计划及待通知状态（月度），异常调度检查，失败:" + e.getMessage();
        } finally {
            NgTaskUtil.restExecStatus(PbmsConstants.CHECK_MEETING_ERR_NOTIFIED);
            NgTaskUtil.updateNgTaskLog(sn, rtnCode, rtnMsg);
        }

    }

    @Override
    public void handleMToBeHeld(String sn, Date currTime) {
        String rtnCode = "0";
        String rtnMsg = "检查三会一课异常的待召开状态（月度），成功。";

        try {
            String type = MeetingTypeEnum.BRANCHLEADER.getCode().toString().concat(",").concat(MeetingTypeEnum.PARTYGROUP.getCode().toString());
            String status = MeetingStatusEnum.TOBEPLAN.getCode().toString().concat(",").concat(MeetingStatusEnum.TOBENOTIFY.getCode().toString()).concat(MeetingStatusEnum.TOBECONVENE.getCode().toString());
            exceCheck(type, status, currTime, false);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            rtnCode = "-9999";
            rtnMsg = "检查三会一课异常的待召开状态（月度），失败:" + e.getMessage();
        } finally {
            NgTaskUtil.restExecStatus(PbmsConstants.CHECK_MEETING_ERR_NOTIFIED);
            NgTaskUtil.updateNgTaskLog(sn, rtnCode, rtnMsg);
        }

    }

    @Override
    public void handleMToBeConcluded(String sn, Date currTime) {
        String rtnCode = "0";
        String rtnMsg = "检查三会一课异常的待归档状态（月度），成功。";

        try {
            String type = MeetingTypeEnum.BRANCHLEADER.getCode().toString().concat(",").concat(MeetingTypeEnum.PARTYGROUP.getCode().toString());
            String status = MeetingStatusEnum.TOBEPLAN.getCode().toString().concat(",").concat(MeetingStatusEnum.TOBENOTIFY.getCode().toString()).concat(MeetingStatusEnum.TOBECONVENE.getCode().toString()).concat(MeetingStatusEnum.CONVENING.getCode().toString()).concat(MeetingStatusEnum.TOBIEHOLD.getCode().toString());
            exceCheck(type, status, currTime, false);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            rtnCode = "-9999";
            rtnMsg = "检查三会一课异常的待归档状态（月度），失败:" + e.getMessage();
        } finally {
            NgTaskUtil.restExecStatus(PbmsConstants.CHECK_MEETING_ERR_NOTIFIED);
            NgTaskUtil.updateNgTaskLog(sn, rtnCode, rtnMsg);
        }

    }

    @Override
    public void handleMBeOverdue(String sn, Date currTime) {
        String rtnCode = "0";
        String rtnMsg = "检查三会一课异常的超期未完成状态（月度），成功。";

        try {
            String type = MeetingTypeEnum.BRANCHLEADER.getCode().toString().concat(",").concat(MeetingTypeEnum.PARTYGROUP.getCode().toString());
            String status = MeetingStatusEnum.TOBEPLAN.getCode().toString().concat(",").concat(MeetingStatusEnum.TOBENOTIFY.getCode().toString()).concat(MeetingStatusEnum.TOBECONVENE.getCode().toString()).concat(MeetingStatusEnum.CONVENING.getCode().toString()).concat(MeetingStatusEnum.TOBIEHOLD.getCode().toString());
            exceCheck(type, status, currTime, true);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            rtnCode = "-9999";
            rtnMsg = "检查三会一课异常的超期未完成状态（月度），失败:" + e.getMessage();
        } finally {
            NgTaskUtil.restExecStatus(PbmsConstants.CHECK_MEETING_ERR_NOTIFIED);
            NgTaskUtil.updateNgTaskLog(sn, rtnCode, rtnMsg);
        }

    }

    @Override
    public void handleQToBeNotified(String sn, Date currTime) {
        String rtnCode = "0";
        String rtnMsg = "检查三会一课异常的待计划及待通知状态（季度），成功。";

        try {
            String type = MeetingTypeEnum.BRANCHMASSES.getCode().toString().concat(",").concat(MeetingTypeEnum.NEWSTUDY.getCode().toString());
            String status = MeetingStatusEnum.TOBEPLAN.getCode().toString().concat(",").concat(MeetingStatusEnum.TOBENOTIFY.getCode().toString());
            exceCheck(type, status, currTime, false);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            rtnCode = "-9999";
            rtnMsg = "检查三会一课异常的待计划及待通知状态（季度），失败:" + e.getMessage();
        } finally {
            NgTaskUtil.restExecStatus(PbmsConstants.CHECK_MEETING_ERR_NOTIFIED);
            NgTaskUtil.updateNgTaskLog(sn, rtnCode, rtnMsg);
        }

    }

    @Override
    public void handleQToBeHeld(String sn, Date currTime) {
        String rtnCode = "0";
        String rtnMsg = "检查三会一课异常的待召开状态（季度），成功。";

        try {
            String type = MeetingTypeEnum.BRANCHMASSES.getCode().toString().concat(",").concat(MeetingTypeEnum.NEWSTUDY.getCode().toString());
            String status = MeetingStatusEnum.TOBEPLAN.getCode().toString().concat(",").concat(MeetingStatusEnum.TOBENOTIFY.getCode().toString()).concat(MeetingStatusEnum.TOBECONVENE.getCode().toString());
            exceCheck(type, status, currTime, false);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            rtnCode = "-9999";
            rtnMsg = "检查三会一课异常的待召开状态（季度），失败:" + e.getMessage();
        } finally {
            NgTaskUtil.restExecStatus(PbmsConstants.CHECK_MEETING_ERR_NOTIFIED);
            NgTaskUtil.updateNgTaskLog(sn, rtnCode, rtnMsg);
        }

    }

    @Override
    public void handleQToBeConcluded(String sn, Date currTime) {
        String rtnCode = "0";
        String rtnMsg = "检查三会一课异常的待归档状态（季度），成功。";

        try {
            String type = MeetingTypeEnum.BRANCHMASSES.getCode().toString().concat(",").concat(MeetingTypeEnum.NEWSTUDY.getCode().toString());
            String status = MeetingStatusEnum.TOBEPLAN.getCode().toString().concat(",").concat(MeetingStatusEnum.TOBENOTIFY.getCode().toString()).concat(MeetingStatusEnum.TOBECONVENE.getCode().toString()).concat(MeetingStatusEnum.CONVENING.getCode().toString()).concat(MeetingStatusEnum.TOBIEHOLD.getCode().toString());
            exceCheck(type, status, currTime, false);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            rtnCode = "-9999";
            rtnMsg = "检查三会一课异常的待归档状态（季度），失败:" + e.getMessage();
        } finally {
            NgTaskUtil.restExecStatus(PbmsConstants.CHECK_MEETING_ERR_NOTIFIED);
            NgTaskUtil.updateNgTaskLog(sn, rtnCode, rtnMsg);
        }

    }

    @Override
    public void handleQBeOverdue(String sn, Date currTime) {
        String rtnCode = "0";
        String rtnMsg = "检查三会一课异常的超期未完成状态（季度），成功。";

        try {
            String type = MeetingTypeEnum.BRANCHMASSES.getCode().toString().concat(",").concat(MeetingTypeEnum.NEWSTUDY.getCode().toString());
            String status = MeetingStatusEnum.TOBEPLAN.getCode().toString().concat(",").concat(MeetingStatusEnum.TOBENOTIFY.getCode().toString()).concat(MeetingStatusEnum.TOBECONVENE.getCode().toString()).concat(MeetingStatusEnum.CONVENING.getCode().toString()).concat(MeetingStatusEnum.TOBIEHOLD.getCode().toString());
            exceCheck(type, status, currTime, true);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            rtnCode = "-9999";
            rtnMsg = "检查三会一课异常的超期未完成状态（季度），失败:" + e.getMessage();
        } finally {
            NgTaskUtil.restExecStatus(PbmsConstants.CHECK_MEETING_ERR_NOTIFIED);
            NgTaskUtil.updateNgTaskLog(sn, rtnCode, rtnMsg);
        }

    }

    private void exceCheck(String type, String status, Date currTime, boolean overdue) {
        Map<String, Object> params = new HashMap<>();
        params.put("type", type.split(","));
        params.put("status", status.split(","));
        params.put("currTime", currTime);

        List<Meetings> errMeetings = meetingsDAO.selectErrPlannedMeetings(params);

        if (null != errMeetings && !errMeetings.isEmpty()) {
            Map<String, Object> handleParams = new HashMap<>();
            if (overdue) {
                handleParams.put("iserror", 2);
            } else {
                handleParams.put("iserror", 1);
            }
            handleParams.put("modifiedby", sys_user);
            handleParams.put("modifieddate", currTime);
            handleParams.put("meetingList", errMeetings);
            meetingsDAO.updateErrPlannedMeetings(handleParams);
        }
    }
}
