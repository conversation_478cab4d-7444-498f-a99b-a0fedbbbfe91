package com.cmos.pbms.service.impl.pm;

import com.alibaba.dubbo.config.annotation.Service;
import com.cmos.pbms.beans.pm.PmOrganizationChangeDtl;
import com.cmos.pbms.dao.pm.PmOrganizationChangeDtlDAO;
import com.cmos.pbms.iservice.pm.IPmOrganizationChangeDtlSV;
import org.springframework.beans.factory.annotation.Autowired;

@Service(group = "pbms", retries = -1)
public class PmOrganizationChangeDtlSVImpl implements IPmOrganizationChangeDtlSV {

    @Autowired
    private PmOrganizationChangeDtlDAO pmOrganizationChangeDtlDAO;

    @Override
    public PmOrganizationChangeDtl getByOrgChangeId(String orgChangeId) {
        return pmOrganizationChangeDtlDAO.selectByOrgChangeId(orgChangeId);
    }
}
