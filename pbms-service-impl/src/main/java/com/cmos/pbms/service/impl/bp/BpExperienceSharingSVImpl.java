package com.cmos.pbms.service.impl.bp;

import com.alibaba.dubbo.config.annotation.Service;
import com.cmos.pbms.beans.bp.BpExperienceSharing;
import com.cmos.pbms.beans.dto.BpExperienceSharingListDTO;
import com.cmos.pbms.dao.bp.BpExperienceSharingDAO;
import com.cmos.pbms.iservice.bp.IBpExperienceSharingSV;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service(group = "pbms", retries = -1)
public class BpExperienceSharingSVImpl implements IBpExperienceSharingSV {

    @Autowired
    private BpExperienceSharingDAO bpExperienceSharingDAO;

    @Override
    public int insertSelective(BpExperienceSharing bpExperienceSharing) {
        return bpExperienceSharingDAO.insertSelective(bpExperienceSharing);
    }

    @Override
    public BpExperienceSharing getByPrimaryKey(String id) {
        return bpExperienceSharingDAO.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(BpExperienceSharing bpExperienceSharing) {
        return bpExperienceSharingDAO.updateByPrimaryKeySelective(bpExperienceSharing);
    }

    @Override
    public int updateByPrimaryKey(BpExperienceSharing bpExperienceSharing) {
        return bpExperienceSharingDAO.updateByPrimaryKey(bpExperienceSharing);
    }

    @Override
    public PageInfo<BpExperienceSharingListDTO> getExperienceSharingPersonList(Integer pageNum, Integer pageSize, String userId) {
        PageHelper.startPage(pageNum, pageSize);
        List<BpExperienceSharingListDTO> dataList = bpExperienceSharingDAO.selectExperienceSharingPersonList(userId);
        return new PageInfo<>(dataList);
    }

    @Override
    public PageInfo<BpExperienceSharingListDTO> getExperienceSharingList(Integer pageNum, Integer pageSize, String orgId) {
        PageHelper.startPage(pageNum, pageSize);
        List<BpExperienceSharingListDTO> dataList = bpExperienceSharingDAO.selectExperienceSharingList(orgId);
        return new PageInfo<>(dataList);
    }

    @Override
    public int addReviewsCount(String id) {
        return bpExperienceSharingDAO.addThumbAndReview(id, null, 1);
    }

    @Override
    public int reduceReviewsCount(String id) {
        return bpExperienceSharingDAO.addThumbAndReview(id, null, -1);
    }

    @Override
    public int addThumbsCount(String id) {
        return bpExperienceSharingDAO.addThumbAndReview(id, 1, null);
    }

    @Override
    public int reduceThumbsCount(String id) {
        return bpExperienceSharingDAO.addThumbAndReview(id, -1, null);
    }
}
