package com.cmos.pbms.service.impl.me;

import com.alibaba.dubbo.config.annotation.Service;
import com.cmos.common.exception.SystemFailureException;
import com.cmos.common.validator.me.VMeetingConventioneerSearchBean;
import com.cmos.common.validator.me.VMeetingNeerListObj;
import com.cmos.common.validator.me.VPartyBranchMemberSearchBean;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.dto.*;
import com.cmos.pbms.beans.enums.SimpleDataEnum;
import com.cmos.pbms.beans.me.Conventioneer;
import com.cmos.pbms.beans.me.Meetings;
import com.cmos.pbms.beans.pm.Organization;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.dao.me.ConventioneerDAO;
import com.cmos.pbms.dao.me.MeetingsDAO;
import com.cmos.pbms.dao.pm.OrganizationDAO;
import com.cmos.pbms.dao.sys.UsersDAO;
import com.cmos.pbms.iservice.me.IConventioneerSV;
import com.cmos.pbms.utils.UIDUtil;
import com.cmos.pbms.utils.ValidateUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * 与会者服务实现类
 * 使用了Dubbo服务注解,自动注册为Dubbo服务
 *
 * <AUTHOR>
 */
@Service(group = "pbms", retries = -1)
public class ConventioneerSVImpl implements IConventioneerSV {
    private static final Logger logger = LoggerFactory.getLogger(ConventioneerSVImpl.class);

    private static final Integer IS_JOIN_DEFAULT = 0;// 会议实际是否参与（0：未参加）
    private static final Integer IS_JOIN_YES = 1;// 会议实际是否参与（1：确认参加）
    private static final Integer STA_JOIN = 1; //会前统计参加情况（1准时参加，2请假）
    private static final Integer RECORD_VALID_YES = 0;// 数据状态（0：正常）
    private static final Integer MEETING_STATUS_TO_BE_CONFIRM = 0;// 会议状态（0：待确认）
    private static final Integer MEETING_STATUS_TO_BE_JOIN = 100;// 会议状态（100：待参加）

    private static final String IS_ONLINE = "02";//线上直播标识

    @Autowired
    private ConventioneerDAO conventioneerDAO;

    @Autowired
    private UsersDAO usersDAO;

    @Autowired
    private OrganizationDAO organizationDAO;

    @Autowired
    private MeetingsDAO meetingsDAO;

    @Override
    public int insertConventioneer(Conventioneer conventioneer) {
        return conventioneerDAO.insert(conventioneer);
    }

    @Override
    public int deleteById(String convertioneerId) {
        return conventioneerDAO.deleteByPrimaryKey(convertioneerId);
    }

    @Override
    public int deleteByIdLogic(String convertioneerId) {
        return conventioneerDAO.deleteConvertioneerById(convertioneerId);
    }

    @Override
    public PageInfo<MeetingConventioneerDTO> getConventioneerListByParams(VMeetingConventioneerSearchBean searchBean) {
        Map<String, Object> params = new HashMap<>(16);
        if (searchBean.getSearchAll()) {
            List<String> meetingIds = meetingsDAO.selectMeetingIdsByMainProcessId(searchBean.getMeetingid());
            params.put("meetingids", meetingIds);
        } else {
            params.put("meetingid", searchBean.getMeetingid());
        }
        params.put("username", searchBean.getUsername());
        params.put("isjoin", searchBean.getIsjoin());
        params.put("status", searchBean.getStatus());
        params.put("issubmitrecord", searchBean.getIssubmitrecord());
        params.put("isgood", searchBean.getIsgood());

        PageHelper.startPage(searchBean.getPage(), searchBean.getLimit());
        List<MeetingConventioneerDTO> list = conventioneerDAO.selectListByParams(params);

        return new PageInfo<>(list);
    }


    /**
     * 获取缺席的人员列表
     *
     * @param meetingId
     * @return
     */
    @Override
    public List<MeetingConventioneerDTO> getMeetingConventioneerAbsenteeList(String meetingId) {
        return   conventioneerDAO.selectAbsenteeListByMeetingId(meetingId);

    }

    @Override
    public PageInfo<MeetingConventioneerDTO> getConventioneerList(VMeetingNeerListObj searchBean) {
        Map<String, Object> params = new HashMap<>(16);
        if ("true".equals(searchBean.getIsSearchAll())) {
            List<String> meetingIds = meetingsDAO.selectMeetingIdsByMainProcessId(searchBean.getMeetingid());
            params.put("meetingids", meetingIds);
        } else {
            params.put("meetingid", searchBean.getMeetingid());
        }
        params.put("username", searchBean.getUsername());
        params.put("orgname", searchBean.getOrgname());

        PageHelper.startPage(searchBean.getPage(), searchBean.getLimit());
        List<MeetingConventioneerDTO> list = conventioneerDAO.selectListByParams(params);

        return new PageInfo<>(list);
    }

    @Override
    public List<MeetingConventioneerDTO> getConventioneerListByMeetingId(String meetingid) {
        Map<String, Object> params = new HashMap<>(16);
        params.put("meetingid", meetingid);

        return conventioneerDAO.selectListByParams(params);
    }

    @Override
    public List<MeetingConventioneerDTO> getSubmitRecordListByMeetingId(String meetingid) {
        Map<String, Object> params = new HashMap<>(16);
        params.put("meetingid", meetingid);
        params.put("issubmitrecord", 1);
        return conventioneerDAO.selectListByParams(params);
    }

    @Override
    public List<MeetingConventioneerDTO> getSubmitRecordListByMeetingIdWithoutStatus(String meetingid) {
        Map<String, Object> params = new HashMap<>(16);
        params.put("meetingid", meetingid);
        params.put("issubmitrecord", 1);
        return conventioneerDAO.selectListByParamsWithoutStatus(params);
    }

    @Override
    public MeetingRecordDTO getMeetingRecord(String conventioneerid) {
        return conventioneerDAO.selectMeetingRecordById(conventioneerid);
    }

    @Override
    public MeetingRecordDTO getMeetingRecord(String meetingid, String userid) {
        return conventioneerDAO.selectRecordByMeetingAndUser(meetingid, userid);
    }

    @Override
    public List<Conventioneer> getConventioneerByMeetingsId(String meetingsid, Boolean searchAll) {
        Map<String, Object> params = new HashedMap();
        if (searchAll) {
            List<String> meetingIds = meetingsDAO.selectMeetingIdsByMainProcessId(meetingsid);
            params.put("meetingids", meetingIds);
        } else {
            params.put("meetingid", meetingsid);
        }
        return conventioneerDAO.selectByMeetingid(params);
    }

    @Override
    public List<ConventioneerSimpleDTO> getConventioneerSimpleDTOByMeetingsId(String meetingsid, Boolean searchAll) {
        List<String> meetingIds = new ArrayList<>();
        if (searchAll) {
            List<String> meetingIdss = meetingsDAO.selectMeetingIdsByMainProcessId(meetingsid);
            meetingIds.addAll(meetingIdss);
        } else {
            meetingIds.add(meetingsid);
        }
        return conventioneerDAO.selectConventioneerSimpleDTOBByMeetingids(meetingIds);
    }

    @Override
    public void insertConventioneerInBatch(Meetings meeting, List<Users> userList, String userId, Date sysDate, List<String> noMsgUserList, Integer isMeetingNotice, Integer isRecordSubmit) throws SystemFailureException {
        Set<String> notReceiveUser = new HashSet<>(16);
        notReceiveUser.addAll(noMsgUserList);

        String baseUid = UIDUtil.getUID(8);
        int uidIndex = 0;

        List<Conventioneer> conventioneerList = new ArrayList<>();
        Set<String> userIds = new HashSet<>(16);
        for (Users temp : userList) {
            if (userIds.contains(temp.getId())) {
                continue;
            }
            Conventioneer conventioneer = new Conventioneer();
            if (uidIndex >= 99999) {
                baseUid = UIDUtil.getUID(8);
                uidIndex = 0;
            }
            uidIndex++;
            conventioneer.setId(UIDUtil.setUid(baseUid, uidIndex));
            conventioneer.setMeetingid(meeting.getId());
            conventioneer.setUserid(temp.getId());
            conventioneer.setOrgid(temp.getOrgid());
            conventioneer.setOrgcode(temp.getCodestr());
            conventioneer.setOrgname(temp.getOrgsname());
            //设置参会人员默认状态为准时参加，实际参会状态为未参加
            conventioneer.setStaJoin(STA_JOIN);
//            需求默认全部参加会议
//            conventioneer.setIsjoin("02".equals(meeting.getTimelinessType()) ? IS_JOIN_YES : IS_JOIN_DEFAULT);
            conventioneer.setIsjoin(IS_JOIN_YES);
            conventioneer.setConfirmtime(null);
            conventioneer.setReason(null);
            conventioneer.setTitle(null);
            conventioneer.setMinutecontent(null);
            conventioneer.setSubtime(null);
            conventioneer.setStatus(MEETING_STATUS_TO_BE_CONFIRM);
            conventioneer.setIsdeleted(RECORD_VALID_YES);
            conventioneer.setCreatedby(userId);
            conventioneer.setCreateddate(sysDate);
            conventioneer.setIssubmitrecord(0);
            conventioneer.setIsgood(0);
            if (SimpleDataEnum.NOTOPENNOTICE.getCode().equals(isMeetingNotice)) { // 设置不提醒，则不管人员选择与否，都不会提醒
                conventioneer.setIsReceiveJoinMsg(SimpleDataEnum.NOTRECEIVEMSG.getCode());
            } else {
                conventioneer.setIsReceiveJoinMsg(notReceiveUser.contains(temp.getId()) ? SimpleDataEnum.NOTRECEIVEMSG.getCode() : SimpleDataEnum.ISRECEIVEMSG.getCode());
            }
            if (SimpleDataEnum.NOTOPENNOTICE.getCode().equals(isRecordSubmit)) { // 设置不提醒，则不管人员选择与否，都不会提醒
                conventioneer.setIsReceiveRecordMsg(SimpleDataEnum.NOTRECEIVEMSG.getCode());
            } else {
                conventioneer.setIsReceiveRecordMsg(notReceiveUser.contains(temp.getId()) ? SimpleDataEnum.NOTRECEIVEMSG.getCode() : SimpleDataEnum.ISRECEIVEMSG.getCode());
            }
            conventioneerList.add(conventioneer);
            userIds.add(temp.getId());
        }
        if (ValidateUtil.isValid(conventioneerList)) {
            logger.info("批量生成与会者数目：0。（该组织下无成员）");
        } else {
            int insertConventioneerCount = conventioneerDAO.insertBatch(conventioneerList);
            logger.info("批量生成与会者数目：" + insertConventioneerCount);
            if (insertConventioneerCount != userIds.size()) {
                throw new SystemFailureException("批量生成与会者数量异常");
            }
        }
    }

    @Override
    public Conventioneer getConventioneerByUnionId(String meetingId, String userId) {
        return conventioneerDAO.selectByUnionId(meetingId, userId);
    }

    @Override
    public Conventioneer getConventioneerById(String conventionerid) {
        return conventioneerDAO.selectByPrimaryKey(conventionerid);
    }

    @Override
    public int updateConventioneerById(Conventioneer conventioneer) {
        return conventioneerDAO.updateByPrimaryKey(conventioneer);
    }

    @Override
    public PageInfo<UserListBean> getPartyBranchMember(VPartyBranchMemberSearchBean searchBean) {
        // 获取组织
        Organization organization = organizationDAO.selectByPrimaryKey(searchBean.getOrgid());

        // 查询支部领导
//        List<String> leaderIdList = usersDAO.selectLeaderIdsByOrgid(searchBean.getOrgid());
//        StringBuilder leaderIdsSB= new StringBuilder();
//        for(String id : leaderIdList) {
//            leaderIdsSB.append("'" + id + "',");
//        }
//        String leaderIds = leaderIdsSB.toString() + "''";

        // 查询支部及支部下的党小组党员（排除支部领导）
        Map<String, Object> params = new HashMap<>(16);
        params.put("codestr", organization.getCodestr());
        params.put("leaderIds", null);
        params.put("username", searchBean.getUsername());

        PageHelper.startPage(searchBean.getPage(), searchBean.getLimit());
        List<UserListBean> userList = usersDAO.selectPartyBranchMember(params);

        return new PageInfo<>(userList);
    }

    @Override
    public MeetingConventioneerDTO getSubmitRatio(String meetingid, Boolean searchAll) {
        Map<String, Object> params = new HashMap<>();
        if (searchAll) {
            List<String> meetingIds = meetingsDAO.selectMeetingIdsByMainProcessId(meetingid);
            params.put("meetingIds", meetingIds);
        } else {
            params.put("meetingId", meetingid);
        }
        return conventioneerDAO.selectSubmitRatio(params);
    }

    @Override
    public List<MeetingConventioneerDTO> getNotSubmitRecord(String meetingid, Boolean isOnline) {
        List<String> meetingIds;
        if (isOnline) {
            meetingIds = meetingsDAO.selectMeetingIdsByMainProcessId(meetingid);
        } else {
            meetingIds = new ArrayList<>(10);
            meetingIds.add(meetingid);
        }

        Map<String, Object> params = new HashMap<>();
        params.put("meetingids", meetingIds);
        params.put("isOnline", isOnline);
        return conventioneerDAO.selectNotSubmitRecord(params);
    }

    @Override
    public List<MeConventioneerInfoDTO> getMeConventInfoByMeetingIds(Collection<String> meetingids) {
        return conventioneerDAO.selectMeConventInfoByMeetingIds(meetingids);
    }

    @Override
    public int selectPersonSum(String meetingId, Boolean searchAll, Users users) {
        Meetings meetings = meetingsDAO.selectByPrimaryKey(meetingId);
        Map<String, Object> params = new HashedMap();
        if (searchAll && IS_ONLINE.equals(meetings.getChannelType()) && meetings.getModerator().equals(users.getId())) {
            List<String> meetingIds = meetingsDAO.selectMeetingIdsByMainProcessId(meetingId);
            params.put("meetingIds", meetingIds);
        } else {
            params.put("meetingId", meetingId);
        }
        return conventioneerDAO.selectPersonSum(params);
    }

    @Override
    public int updateJoinStatus(String meetingId, String userId) {
        Map<String, Object> params = new HashMap<>();
        params.put("meetingId", meetingId);
        params.put("userid", userId);
        params.put("haveInteractive", "01");
        params.put("startTime", new Date());
        List<Conventioneer> list = conventioneerDAO.joinStatus(params);
        if (!list.isEmpty()) {
            Set<String> neerIdSet = new HashSet<>(16);
            for (Conventioneer temp : list) {
                neerIdSet.add(temp.getId());
            }
            params.put("ids", neerIdSet);
            return conventioneerDAO.updateJoinStatus(params);
        } else {
            return 0;
        }
    }

    @Override
    public void setConventioneerJoinStatusByMainProcessId(String mainProcessId) {
        //获取所有参会人员ID
        List<String> ConventioneerIds = conventioneerDAO.selectIdsByMainProcessID(mainProcessId);

        Map<String, Object> params = new HashMap<>();
        params.put("Ids", ConventioneerIds);
        params.put("modifiedby", "System");
        params.put("modifieddate", new Date());
        //根据参会人员ID修改确认状态
        conventioneerDAO.updateConfirmStatus(params);
    }

    @Override
    public int updateIsReceiveRecordMsg(Conventioneer conventioneer, Boolean searchAll) {
        if (searchAll) {
            List<String> meetingIds = meetingsDAO.selectMeetingIdsByMainProcessId(conventioneer.getMeetingid());
            if (CollectionUtils.isNotEmpty(meetingIds)) {
                conventioneer.setMeetingids(meetingIds);
                conventioneer.setMeetingid(null);
            }
        }
        return conventioneerDAO.updateIsReceiveRecordMsg(conventioneer);
    }

    @Override
    public List<MeetingConventioneerDTO> selectListByParams(Map<String, Object> paramMap) {
        return conventioneerDAO.selectListByParams(paramMap);
    }

    @Override
    public int setAllin(String meetingId, String userId) {
        Meetings meetings = meetingsDAO.selectByPrimaryKey(meetingId);

        Date currTime = new Date();

        Map<String, Object> params = new HashMap<>();
        params.put("isJoin", 1);
        params.put("meetingId", meetingId);
        params.put("signAddress", meetings.getAddress());
        params.put("confirmtime", currTime);
        params.put("confirmUser", userId);
        params.put("modifiedby", userId);
        params.put("modifieddate", currTime);

        return conventioneerDAO.setAllin(params);
    }


    /**
     * 获取全部成员员
     *
     * @param searchBean
     * @return
     */
    @Override
    public PageInfo<UserListBean> getPartyBranchMemberALL(VPartyBranchMemberSearchBean searchBean) {
//        // 获取组织
//        Organization organization = organizationDAO.selectByPrimaryKey(searchBean.getOrgid());

        // 查询支部领导
//        List<String> leaderIdList = usersDAO.selectLeaderIdsByOrgid(searchBean.getOrgid());
//        StringBuilder leaderIdsSB= new StringBuilder();
//        for(String id : leaderIdList) {
//            leaderIdsSB.append("'" + id + "',");
//        }
//        String leaderIds = leaderIdsSB.toString() + "''";

        // 查询支部及支部下的党小组党员（排除支部领导）
        Map<String, Object> params = new HashMap<>(16);
//        params.put("codestr", organization.getCodestr());
        params.put("leaderIds", null);
        params.put("username", searchBean.getUsername());

        PageHelper.startPage(searchBean.getPage(), searchBean.getLimit());
        List<UserListBean> userList = usersDAO.selectPartyBranchMember(params);

        return new PageInfo<>(userList);
    }
}
