package com.cmos.pbms.service.impl.bs;

import com.alibaba.dubbo.config.annotation.Service;
import com.cmos.common.exception.SystemFailureException;
import com.cmos.common.validator.bs.VActivityStatisticalSearchBean;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.dto.*;
import com.cmos.pbms.beans.pm.Organization;
import com.cmos.pbms.beans.sys.DictionaryItems;
import com.cmos.pbms.beans.sys.SysScoreRule;
import com.cmos.pbms.beans.sys.SysUserScoreDtl;
import com.cmos.pbms.dao.ce.ActivitiesDAO;
import com.cmos.pbms.dao.me.PlanTaskDAO;
import com.cmos.pbms.dao.pm.OrganizationDAO;
import com.cmos.pbms.dao.pm.UserpositionDAO;
import com.cmos.pbms.dao.rn.ReaderDAO;
import com.cmos.pbms.dao.rn.ReadingDAO;
import com.cmos.pbms.dao.sys.*;
import com.cmos.pbms.iservice.bs.IBigScreenSV;
import com.cmos.pbms.utils.ConvertUtil;
import com.cmos.pbms.utils.DateUtil;
import com.cmos.pbms.utils.ValidateUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 大屏服务实现类
 * 使用了Dubbo服务注解,自动注册为Dubbo服务
 *
 * <AUTHOR>
 */
@Service(group = "pbms", retries = -1)
public class BigScreenSVImpl implements IBigScreenSV {

    private static final Logger logger = LoggerFactory.getLogger(BigScreenSVImpl.class);
    private static final String DICT_CODE_PROVINCE = "PROVINCE"; // 字典码编号-省份
    private static final String EXCLUDE_PROVINCE = "重客中心"; // 报表统计时需要排除的省公司
    private static final String ITEM_CODE_LOGIN_TIMES = "LOGIN_TIMES"; // 字典明细表-累计登陆编码
    private static final String LOGIN_PARAM = "LOGIN_PARAM"; // 字典表-登录相关参数
    private static final String LOGIN_VALID_TIME = "LOGIN_VALID_TIME"; // 字典表明细表-登录失效时间（分钟）
    @Autowired
    private OrganizationDAO organizationDAO;
    @Autowired
    private UsersDAO usersDAO;
    @Autowired
    private ActivitiesDAO activitiesDAO;
    @Autowired
    private ReadingDAO readingDAO;
    @Autowired
    private ReaderDAO readerDAO;
    @Autowired
    private UserpositionDAO userpositionDAO;
    @Autowired
    private SysUserScoreDtlDAO userScoreDtlDAO;
    @Autowired
    private FootPointDAO footPointDAO;
    @Autowired
    private SysScoreRuleDAO scoreRuleDAO;
    @Autowired
    private SysUserLoginInfoDAO userLoginInfoDAO;
    @Autowired
    private DictionaryItemsDAO dictionaryItemsDAO;
    @Autowired
    private PlanTaskDAO planTaskDAO;

    @Override
    public Map<String, OrgMeetingFinishRateDTO> getMeCompletionInfo(String orgId, Integer yearParam, Integer season) {
        Map<String, OrgMeetingFinishRateDTO> result = new HashMap<>();
        String orgCode = null;
        if (StringUtils.isNotBlank(orgId)) {
            Organization organization = organizationDAO.selectByPrimaryKey(orgId);
            if (null != organization)
                orgCode = organization.getCodestr();
        }

        Date beginDate = null;
        Date endDate = null;
        if (null != yearParam) {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.YEAR, yearParam);
            calendar.set(Calendar.MONTH, null == season ? 0 : (season - 1) * 3);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);

            beginDate = calendar.getTime();

            calendar.add(Calendar.MONTH, 3);
            endDate = calendar.getTime();
        }

        // 查询需排除的省份
        String provinceCode = getExcludeProvince();
        // 下属党支部和党小组的三会一课要求次数和完成次数
        Map<String, OrgMeetingFinishRateDTO> meetingFinishRate = getMeetingFinishRate(orgCode, provinceCode, yearParam, season, beginDate, endDate);

        List<OrgMeetingFinishRateDTO> orgMeetingFinishRateDTOList = organizationDAO.selectAllOrgMeetingFinishRateDTOByOrgCode(orgCode, provinceCode, endDate);
        //结构化本级组织和所有下级组织
        OrgMeetingFinishRateDTO orgMeetingFinishRateDTO = generAllOrgMeetingFinishRateDTO(orgMeetingFinishRateDTOList, orgCode);
        if (null == orgMeetingFinishRateDTO)
            return null;

        //计算本级组织和下属组织的三会一课完成率
        generAllOrgMeetingFinishRateDTO(orgMeetingFinishRateDTO, meetingFinishRate);

        //本级获取领导信息
        orgMeetingFinishRateDTO.setLeader(userpositionDAO.getOrgLeaderLimitOne(orgId));
        List<OrgMeetingFinishRateDTO> children = orgMeetingFinishRateDTO.getChildren();
        if (null != children && !children.isEmpty()) {
            for (OrgMeetingFinishRateDTO child : children) {
                if (null == child)
                    continue;
                child.setLeader(userpositionDAO.getOrgLeaderLimitOne(child.getOrgId()));
            }
        }
        generResult(orgId, result, orgMeetingFinishRateDTO);
        return result;
    }

    private void generResult(String orgId, Map<String, OrgMeetingFinishRateDTO> result, OrgMeetingFinishRateDTO orgMeetingFinishRateDTO) {
        if (StringUtils.isNotBlank(orgId)) {
            result.put("com", orgMeetingFinishRateDTO);
            generBranchAndGroupFinishRate(orgMeetingFinishRateDTO);
            return;
        }
        OrgMeetingFinishRateDTO com = new OrgMeetingFinishRateDTO();
        OrgMeetingFinishRateDTO hq = new OrgMeetingFinishRateDTO();

        List<OrgMeetingFinishRateDTO> com_children = new ArrayList<>();
        List<OrgMeetingFinishRateDTO> hq_children = new ArrayList<>();
        com.setChildren(com_children);
        hq.setChildren(hq_children);
        result.put("com", com);
        result.put("hq", hq);
        List<OrgMeetingFinishRateDTO> children = orgMeetingFinishRateDTO.getChildren();
        if (null == children || children.isEmpty())
            return;
        //获取在线本部的省份标示代码
        List<Organization> organizationList = organizationDAO.selectOrganizationByLevel(0);
        if (null == organizationList || organizationList.isEmpty() || organizationList.size() > 1) {
            return;
        }

        String province_hq = organizationList.get(0).getProvince();
        for (OrgMeetingFinishRateDTO child : children) {
            if (province_hq.equals(child.getProvinceId())) {
                copyBranchAndGroupFinishRate(hq, child);
                hq_children.add(child);
            } else {
                copyBranchAndGroupFinishRate(com, child);
                com_children.add(child);
            }
        }
        generBranchAndGroupFinishRate(com);
        generBranchAndGroupFinishRate(hq);
    }

    private OrgMeetingFinishRateDTO generAllOrgMeetingFinishRateDTO(List<OrgMeetingFinishRateDTO> orgMeetingFinishRateDTOList, String orgCode) {
        if (null == orgCode)
            orgCode = "";
        OrgMeetingFinishRateDTO result = null;

        Map<String, OrgMeetingFinishRateDTO> orgMeetingFinishRateDTOMap = new HashMap<>();
        for (OrgMeetingFinishRateDTO orgMeetingFinishRateDTO : orgMeetingFinishRateDTOList) {
            orgMeetingFinishRateDTOMap.put(orgMeetingFinishRateDTO.getOrgId(), orgMeetingFinishRateDTO);
        }
        for (OrgMeetingFinishRateDTO orgMeetingFinishRateDTO : orgMeetingFinishRateDTOList) {
            if (orgCode.equals(orgMeetingFinishRateDTO.getOrgCode()))
                result = orgMeetingFinishRateDTO;

            OrgMeetingFinishRateDTO parent = orgMeetingFinishRateDTOMap.get(orgMeetingFinishRateDTO.getParentId());
            if (null != parent) {
                List<OrgMeetingFinishRateDTO> children = parent.getChildren();
                if (null == children) {
                    children = new ArrayList<>();
                    parent.setChildren(children);
                }
                children.add(orgMeetingFinishRateDTO);
            }
        }

        return result;
    }

    private void generAllOrgMeetingFinishRateDTO(OrgMeetingFinishRateDTO orgMeetingFinishRateDTO, Map<String, OrgMeetingFinishRateDTO> meetingFinishRate) {
        if (null == orgMeetingFinishRateDTO)
            return;
        String orgId = orgMeetingFinishRateDTO.getOrgId();
        List<OrgMeetingFinishRateDTO> children = orgMeetingFinishRateDTO.getChildren();
        if (null != children) {
            for (int i = 0; i < children.size(); i++) {
                OrgMeetingFinishRateDTO child = children.get(i);

                generAllOrgMeetingFinishRateDTO(child, meetingFinishRate);

                if (1 == child.getIsDeleted() || null == child.getRate()) {
                    children.remove(i);
                    i--;
                    continue;
                }

                copyBranchAndGroupFinishRate(orgMeetingFinishRateDTO, child);
            }
            Collections.sort(children, new Comparator<OrgMeetingFinishRateDTO>() {
                @Override
                public int compare(OrgMeetingFinishRateDTO o1, OrgMeetingFinishRateDTO o2) {
                    if (null == o1)
                        return 1;
                    if (null == o2)
                        return -1;

                    BigDecimal rate_o1 = o1.getRate();
                    BigDecimal rate_o2 = o2.getRate();

                    if (null == rate_o1)
                        return 1;
                    if (null == rate_o2)
                        return -1;
                    return rate_o2.compareTo(rate_o1);
                }
            });

        }
        if (meetingFinishRate.containsKey(orgId))
            copyBranchAndGroupFinishRate(orgMeetingFinishRateDTO, meetingFinishRate.get(orgId));

        generBranchAndGroupFinishRate(orgMeetingFinishRateDTO);

    }

    private void copyBranchAndGroupFinishRate(OrgMeetingFinishRateDTO orgMeetingFinishRateDTO, OrgMeetingFinishRateDTO branchAndGroupFinishRate) {
        if (null == branchAndGroupFinishRate)
            return;
        orgMeetingFinishRateDTO.setCountFinishNum(addNum(orgMeetingFinishRateDTO.getCountFinishNum(), branchAndGroupFinishRate.getCountFinishNum()));
        orgMeetingFinishRateDTO.setFinishNum(addNum(orgMeetingFinishRateDTO.getFinishNum(), branchAndGroupFinishRate.getFinishNum()));
        orgMeetingFinishRateDTO.setFinishNum10(addNum(orgMeetingFinishRateDTO.getFinishNum10(), branchAndGroupFinishRate.getFinishNum10()));
        orgMeetingFinishRateDTO.setFinishNum20(addNum(orgMeetingFinishRateDTO.getFinishNum20(), branchAndGroupFinishRate.getFinishNum20()));
        orgMeetingFinishRateDTO.setFinishNum30(addNum(orgMeetingFinishRateDTO.getFinishNum30(), branchAndGroupFinishRate.getFinishNum30()));
        orgMeetingFinishRateDTO.setFinishNum40(addNum(orgMeetingFinishRateDTO.getFinishNum40(), branchAndGroupFinishRate.getFinishNum40()));
        orgMeetingFinishRateDTO.setTotalNum(addNum(orgMeetingFinishRateDTO.getTotalNum(), branchAndGroupFinishRate.getTotalNum()));
        orgMeetingFinishRateDTO.setTotalNum10(addNum(orgMeetingFinishRateDTO.getTotalNum10(), branchAndGroupFinishRate.getTotalNum10()));
        orgMeetingFinishRateDTO.setTotalNum20(addNum(orgMeetingFinishRateDTO.getTotalNum20(), branchAndGroupFinishRate.getTotalNum20()));
        orgMeetingFinishRateDTO.setTotalNum30(addNum(orgMeetingFinishRateDTO.getTotalNum30(), branchAndGroupFinishRate.getTotalNum30()));
        orgMeetingFinishRateDTO.setTotalNum40(addNum(orgMeetingFinishRateDTO.getTotalNum40(), branchAndGroupFinishRate.getTotalNum40()));
    }

    private void generBranchAndGroupFinishRate(OrgMeetingFinishRateDTO orgMeetingFinishRateDTO) {

//        orgMeetingFinishRateDTO.setFinishRate(gener(orgMeetingFinishRateDTO.getTotalNum(), orgMeetingFinishRateDTO.getFinishNum()));

        if (null == orgMeetingFinishRateDTO.getTotalNum() || 0 == orgMeetingFinishRateDTO.getTotalNum())
            orgMeetingFinishRateDTO.setRate(null);
        else if (orgMeetingFinishRateDTO.getFinishNum() >= orgMeetingFinishRateDTO.getTotalNum())
            orgMeetingFinishRateDTO.setRate(BigDecimal.valueOf(100));
        else
            orgMeetingFinishRateDTO.setRate(gener(orgMeetingFinishRateDTO.getTotalNum(), orgMeetingFinishRateDTO.getFinishNum()));

        //本级计算分类完成率

        BigDecimal rate_10 = gener(orgMeetingFinishRateDTO.getTotalNum10(), orgMeetingFinishRateDTO.getFinishNum10());
        BigDecimal rate_20 = gener(orgMeetingFinishRateDTO.getTotalNum20(), orgMeetingFinishRateDTO.getFinishNum20());
        BigDecimal rate_30 = gener(orgMeetingFinishRateDTO.getTotalNum30(), orgMeetingFinishRateDTO.getFinishNum30());
        BigDecimal rate_40 = gener(orgMeetingFinishRateDTO.getTotalNum40(), orgMeetingFinishRateDTO.getFinishNum40());
//
        orgMeetingFinishRateDTO.setRate10(rate_10);
        orgMeetingFinishRateDTO.setRate20(rate_20);
        orgMeetingFinishRateDTO.setRate30(rate_30);
        orgMeetingFinishRateDTO.setRate40(rate_40);
//
//        orgMeetingFinishRateDTO.setRate(generRate(rate_10, rate_20, rate_30, rate_40));
    }

    private BigDecimal generRate(BigDecimal rate_10, BigDecimal rate_20, BigDecimal rate_30, BigDecimal rate_40) {
        int count = 0;
        BigDecimal allRate = BigDecimal.valueOf(0);
        if (null != rate_10) {
            count++;
            allRate = allRate.add(rate_10);
        }

        if (null != rate_20) {
            count++;
            allRate = allRate.add(rate_20);
        }

        if (null != rate_30) {
            count++;
            allRate = allRate.add(rate_30);
        }

        if (null != rate_40) {
            count++;
            allRate = allRate.add(rate_40);
        }

        if (0 == count)
            return null;
        return allRate.divide(BigDecimal.valueOf(count), 0, RoundingMode.HALF_UP);
    }

    private BigDecimal gener(Integer totalNum, Integer finishNum) {
        if (null == totalNum || 0 == totalNum)
            return null;
        if (finishNum >= totalNum)
            return BigDecimal.valueOf(100);
        return BigDecimal.valueOf(finishNum * 100).divide(BigDecimal.valueOf(totalNum), 0, RoundingMode.HALF_UP);
    }

    @Override
    public PageInfo<PersonRankDTO> getPersonRanking(String orgId, int pageNum, int pageSize) {
        // 查询组织信息
        String orgCode = null;
        Organization organization = organizationDAO.selectByPrimaryKey(orgId);
        if (!ValidateUtil.isValid(organization)) {
            orgCode = organization.getCodestr();
        }

        // 查询用户积分情况
        PageHelper.startPage(pageNum, pageSize);
        List<PersonRankDTO> ranklist = usersDAO.selectPersonScoreRank(orgCode);
        return new PageInfo<>(ranklist);
    }

    @Override
    public ActivityForBsDTO getActivityInfo(String orgId) {
        // 查询组织信息
        String orgCode = null;
        if (!StringUtils.isBlank(orgId)) {
            Organization organization = organizationDAO.selectByPrimaryKey(orgId);
            if (!ValidateUtil.isValid(organization)) {
                orgCode = organization.getCodestr();
            }
        }

        // 查询社区活动统计
        ActivityForBsDTO activityForBsDTO = activitiesDAO.selectActivitiesCountByOrgCode(orgCode);
        // 查询读书活动统计
        int readingCount = readingDAO.selectReadingCountByOrgCode(orgCode);
        // 查询读书人次统计
        int readerCount = readerDAO.selectReaderCountByOrgCode(orgCode);

        activityForBsDTO.setRnActTime(readingCount);
        activityForBsDTO.setRnJoinTime(readerCount);
        return activityForBsDTO;
    }

    @Override
    public OrgUserDistributeDTO personDistributeInfo(String orgId, boolean isHQBranch) {
        // 返回值
        OrgUserDistributeDTO userDistribute = new OrgUserDistributeDTO();

        String codeStr = null;
        Organization organization;
        if (!StringUtils.isBlank(orgId)) {
            organization = organizationDAO.selectByPrimaryKey(orgId);
            codeStr = organization.getCodestr();
        } else {
            organization = organizationDAO.selectOrganizationByLevel(0).get(0);
        }
        if (null == organization) {
            return null;
        }

        if (!StringUtils.isBlank(organization.getParentid())) {
            Organization parentOrg = organizationDAO.selectByPrimaryKey(organization.getParentid());
            if (null != parentOrg) {
                userDistribute.setParentOrgsName(parentOrg.getOrgsname());
            }
        }

        // 组织下党员统计
        List<OrgMemberStage> countListWithStage = usersDAO.selectCountByOrg(codeStr);
        Map<Integer, Integer> memberStageMap = new HashMap<>(16);
        for (OrgMemberStage temp : countListWithStage) {
            memberStageMap.put(temp.getStage(), temp.getMemberNum());
        }

//        // 组织下人员统计
//        if (isHQBranch) {
//            // 查询省份组织信息
//            String fOrgCode = organization.getCodestr();
//
////            codeStr = fOrgCode.substring(0, fOrgCode.indexOf("."));
//            codeStr = organizationDAO.selectProvinceOrgInfoByProvince(organization.getProvince()).getCodestr();
//        }
        // 查询指定组织的分公司信息
        orgParentDTO provinceOrg = organizationDAO.selectCompanyByOrgId(organization.getId());
//        int totalNum = userRankInfoDAO.selectCountByOrg(codeStr);
//        UserRankInfo userRankInfo = userRankInfoDAO.selectCountByOrgIdAndProvince(provinceOrg.getProvince(), StringUtils.isBlank(provinceOrg.getParentOrgCode()) ? (StringUtils.isBlank(provinceOrg.getOrgCode()) ? organization.getId() : provinceOrg.getOrgCode()) : provinceOrg.getParentOrgCode());
//        int totalNum;
//        if (null == userRankInfo) {
//            totalNum = 0;
//        } else {
//            totalNum = userRankInfo.getTotalnum();
//        }

        userDistribute.setOrgsname(organization.getOrgsname());
        userDistribute.setOrgfname(organization.getOrgfname());
        userDistribute.setOrgName(organization.getOrgName());
        userDistribute.setOrgtype(organization.getOrgtype());
        userDistribute.setOrganization(organization);
//        userDistribute.setAllmember(totalNum);
//        userDistribute.setNewmember(0);
        int partiermember = (memberStageMap.containsKey(1) ? memberStageMap.get(1) : 0) + (memberStageMap.containsKey(2) ? memberStageMap.get(2) : 0);
        userDistribute.setPartiermember(partiermember);
        userDistribute.setMemberStageMap(memberStageMap);
//        userDistribute.setMemberrate(ConvertUtil.genPercent(memberStageMap.containsKey(1) ? memberStageMap.get(1) : 0, totalNum));

        return userDistribute;
    }

    @Override
    public List<Organization> getOrgGroupByOrgId(String orgid) {
        String codestr = null;
        if (!StringUtils.isBlank(orgid)) {
            Organization organization = organizationDAO.selectByPrimaryKey(orgid);
            if (null != organization) {
                codestr = organization.getCodestr();
            }
        }
        // 查询需排除的省份
        String provinceCode = getExcludeProvince();
        return organizationDAO.selectOrgGroupByCode(codestr, provinceCode);
    }

    /**
     * 根据组织编码，查询下属党支部和党小组的三会一课要求次数和完成次数
     *
     * @param orgCode   组织编码
     * @param beginDate
     * @param endDate
     * @return
     */
    private Map<String, OrgMeetingFinishRateDTO> getMeetingFinishRate(String orgCode, String provinceCode, Integer yearParam, Integer season, Date beginDate, Date endDate) {
        //党支部和党小组的要求次数(注意：此处的index 是 周期)
        List<BranchAndGroupCount> branchAndGroupRequestsCounts = organizationDAO.selectBranchAndGroupRequest(orgCode, provinceCode, yearParam, season);
        //党支部和党小组的完成次数(注意：此处的index 是 月份)
        List<BranchAndGroupCount> branchAndGroupFinishCounts = organizationDAO.selectBranchAndGroupFinish(orgCode, provinceCode, beginDate, endDate);

        Map<String, OrgMeetingFinishRateDTO> result = new HashMap<>();
        Map<String, Integer> branchAndGroupFinishCounts_map = new HashMap<>();

        //处理完成次数
        for (BranchAndGroupCount branchAndGroupFinishCount : branchAndGroupFinishCounts) {

            Integer index = branchAndGroupFinishCount.getIndex();
            String type = branchAndGroupFinishCount.getType();

            //月份转季度 支部党员大会和党课
            if ("10".equals(type) || "40".equals(type)) {
                index = (index + 2) / 3;
                String key = branchAndGroupFinishCount.getOrgId() + index + type;
                Integer num = branchAndGroupFinishCount.getNum();

                Integer num_map = branchAndGroupFinishCounts_map.get(key);
                if (null == num_map)
                    branchAndGroupFinishCounts_map.put(key, num);
                else if (null != num)
                    branchAndGroupFinishCounts_map.put(key, num + num_map);
            } else {
                branchAndGroupFinishCounts_map.put(branchAndGroupFinishCount.getOrgId() + index + type, branchAndGroupFinishCount.getNum());
            }
        }
        //处理完成次数
        for (BranchAndGroupCount branchAndGroupRequestsCount : branchAndGroupRequestsCounts) {

            String orgId = branchAndGroupRequestsCount.getOrgId();
            Integer index = branchAndGroupRequestsCount.getIndex();
            String type = branchAndGroupRequestsCount.getType();
            Integer num_Requests = branchAndGroupRequestsCount.getNum();

            String key = orgId + index + type;

            OrgMeetingFinishRateDTO orgMeetingFinishRateDTO = result.get(orgId);
            if (null == orgMeetingFinishRateDTO) {
                orgMeetingFinishRateDTO = new OrgMeetingFinishRateDTO();
                orgMeetingFinishRateDTO.setOrgId(orgId);
                result.put(orgId, orgMeetingFinishRateDTO);
            }

            Integer num_Finish = branchAndGroupFinishCounts_map.remove(key);

            if ("10".equals(type))
                orgMeetingFinishRateDTO.addTotalAndFinishNum10(num_Requests, num_Finish);
            else if ("20".equals(type))
                orgMeetingFinishRateDTO.addTotalAndFinishNum20(num_Requests, num_Finish);
            else if ("30".equals(type))
                orgMeetingFinishRateDTO.addTotalAndFinishNum30(num_Requests, num_Finish);
            else if ("40".equals(type))
                orgMeetingFinishRateDTO.addTotalAndFinishNum40(num_Requests, num_Finish);

        }

        return result;
    }

    @Override
    public List<ActivityStatisticalDTO> getActivityStatistical(VActivityStatisticalSearchBean activityStatisticalSearchBean) throws SystemFailureException {

        // 查询开始日期
        Calendar startdate = Calendar.getInstance();
        // 查询结束日期
        Calendar enddate = Calendar.getInstance();
        // 过往对比开始日期
        Calendar historyStart = Calendar.getInstance();
        // 过往对比结束日期
        Calendar historyEnd = Calendar.getInstance();

        // 设置各个日期点
        setSearchDate(activityStatisticalSearchBean, startdate, enddate, historyStart, historyEnd);

        // 查询需排除的省份
        String provinceCode = getExcludeProvince();

        List<Organization> organizations = null;
        if ("01".equals(activityStatisticalSearchBean.getInitType()))
            organizations = organizationDAO.selectOrgByParentId(activityStatisticalSearchBean.getOrgId(), null, getHQorg().getProvince(), provinceCode);
        else if ("02".equals(activityStatisticalSearchBean.getInitType()))
            organizations = organizationDAO.selectOrgByParentId(activityStatisticalSearchBean.getOrgId(), getHQorg().getProvince(), null, provinceCode);

        if (CollectionUtils.isEmpty(organizations)) {
            organizations = new ArrayList<>(10);
            Organization organization = organizationDAO.selectByPrimaryKey(activityStatisticalSearchBean.getOrgId());
            organizations.add(organization);
        }

        // 根据查询日期获得要查询的数据表月份，格式化工具

        String searchMonth = DateUtil.format(startdate.getTime(), "yyyyMM");

        String historyTableName = DateUtil.format(historyStart.getTime(), "yyyyMM");

        Map<String, Object> params = new HashMap<>();
        params.put("tableName", searchMonth);
        params.put("orgId", activityStatisticalSearchBean.getOrgId());
        params.put("excludeProvince", provinceCode);

        //查询类型 10:登录 20:党建资讯 30:网上学习
        Integer[] loginType = {10, 20, 30};
        params.put("objTypes", loginType);

        SimpleDateFormat searchDateFormat = new SimpleDateFormat("yyyyMMdd");

        if (null != activityStatisticalSearchBean.getDay()) {
            params.put("recorddate", searchDateFormat.format(startdate.getTime()));
            params.put("startdate", startdate.getTime());
            params.put("enddate", enddate.getTime());
        }
        List<SysUserScoreDtl> sysUserScoreDtls = userScoreDtlDAO.statisticalActivity(params);

        //设置查询历史活跃量参数
        params.put("tableName", historyTableName);
        if (null != activityStatisticalSearchBean.getDay()) {
            params.put("recorddate", searchDateFormat.format(historyStart.getTime()));
            params.put("startdate", historyStart.getTime());
            params.put("enddate", historyEnd.getTime());
        }
        List<SysUserScoreDtl> historyScoreDtls = userScoreDtlDAO.statisticalActivity(params);

        //计算查询数据天数
        int differentDay = DateUtil.differentDays(startdate.getTime(), enddate.getTime());

        //计算活跃度结果
        List<ActivityStatisticalDTO> resultList = new ArrayList<>();

        for (Organization organization : organizations) {
            if(organization !=null){
                ActivityStatisticalDTO statisticalDTO = new ActivityStatisticalDTO();
                statisticalDTO.setOrgCode(organization.getCodestr());
                statisticalDTO.setOrgId(organization.getId());
                statisticalDTO.setOrgName(organization.getOrgName());
                statisticalDTO.setOrgsname(organization.getOrgsname());
                statisticalDTO.setMemberTotal(organizationDAO.countOrgsMemberTotalByOrgCode(organization.getCodestr()));

                //匹配当前活跃量
                matchActList(sysUserScoreDtls, statisticalDTO, differentDay, true);

                //匹配历史活跃量
                matchActList(historyScoreDtls, statisticalDTO, differentDay, false);

                resultList.add(statisticalDTO);
            }

        }

        return resultList;
    }

    /**
     * @方法名：matchActList
     * @方法作用：匹配并计算活跃度
     * @方法参数：
     * @返回结果：
     * @作者：牛文钻
     * @日期：2018/10/18
     */
    private void matchActList(List<SysUserScoreDtl> sysUserScoreDtls, ActivityStatisticalDTO statisticalDTO, int differentDay, Boolean isCurrent) {
        //登录量
        int loginActNum = 0;

        //教育量
        int eduActNum = 0;

        for (int j = 0; j < sysUserScoreDtls.size(); j++) {
            if (sysUserScoreDtls.get(j).getOrgCode().startsWith(statisticalDTO.getOrgCode())) {

                if (10 == sysUserScoreDtls.get(j).getObjType()) {
                    loginActNum++;
                } else if (20 == sysUserScoreDtls.get(j).getObjType() || 30 == sysUserScoreDtls.get(j).getObjType()) {
                    eduActNum++;
                }

                sysUserScoreDtls.remove(j);
                j--;

            }
        }

        DecimalFormat df = new DecimalFormat("0.00");

        //计算人均登录活跃度
        Double loginRate = statisticalDTO.getMemberTotal() <= 0 ? 0 : Double.valueOf(df.format(Float.valueOf(loginActNum) / statisticalDTO.getMemberTotal() / differentDay));

        //计算人均教育活跃度
        Double eduRate = statisticalDTO.getMemberTotal() <= 0 ? 0 : Double.valueOf(df.format(Float.valueOf(eduActNum) / statisticalDTO.getMemberTotal() / differentDay));

        if (isCurrent) {
            statisticalDTO.setLoginActNum(loginActNum);
            statisticalDTO.setEduActNum(eduActNum);
            statisticalDTO.setLoginRate(loginRate);
            statisticalDTO.setEduRate(eduRate);
        } else {
            statisticalDTO.setHistoryLoginActNum(loginActNum);
            statisticalDTO.setHistoryEduActNum(eduActNum);
            statisticalDTO.setHistoryLoginRate(loginRate);
            statisticalDTO.setHistoryEduRate(eduRate);
        }
    }

    /**
     * @方法名：setSearchDate
     * @方法作用：根据查询条件设置各查询日期点对象值
     * @方法参数activityStatisticalSearchBean：查询条件
     * @方法参数startdate：查询开始日期对象
     * @方法参数enddate：查询结束日期对象
     * @方法参数historyStart：历史对比开始日期
     * @方法参数historyEnd：历史对比结束日期
     * @返回结果：
     * @作者：牛文钻
     * @日期：2018/10/17
     */
    private void setSearchDate(VActivityStatisticalSearchBean activityStatisticalSearchBean, Calendar startdate, Calendar enddate, Calendar historyStart, Calendar historyEnd) {
        //判断是否根据日期查询？如果日期参数存在且大于0则是按日查询，否则是按月查询
        if (null != activityStatisticalSearchBean.getDay() && activityStatisticalSearchBean.getDay() > 0) {
            startdate.set(activityStatisticalSearchBean.getYear(), activityStatisticalSearchBean.getMonth() - 1, activityStatisticalSearchBean.getDay());
            enddate.setTime(startdate.getTime());
            enddate.add(Calendar.DAY_OF_MONTH, 1);
            historyStart.setTime(startdate.getTime());
            historyStart.add(Calendar.DAY_OF_MONTH, -1);
            historyEnd.setTime(startdate.getTime());
        } else {
            startdate.set(activityStatisticalSearchBean.getYear(), null == activityStatisticalSearchBean.getMonth() ? 0 : activityStatisticalSearchBean.getMonth() - 1, 1);
            enddate.setTime(startdate.getTime());
            enddate.add(Calendar.MONTH, 1);
            historyStart.setTime(startdate.getTime());
            historyStart.add(Calendar.MONTH, -1);
            historyEnd.setTime(startdate.getTime());
        }
    }

    @Override
    public Map<String, Object> getJoinActivity(VActivityStatisticalSearchBean activityStatisticalSearchBean) throws SystemFailureException {
        //查询开始日期
        Calendar startdate = Calendar.getInstance();
        //查询结束日期
        Calendar enddate = Calendar.getInstance();
        //过往对比开始日期
        Calendar historyStart = Calendar.getInstance();
        //过往对比结束日期
        Calendar historyEnd = Calendar.getInstance();

        //设置各个日期点
        setSearchDate(activityStatisticalSearchBean, startdate, enddate, historyStart, historyEnd);

        //根据查询日期获得要查询的数据表月份，格式化工具
        SimpleDateFormat tabelMonthFormat = new SimpleDateFormat("yyyyMM");

        String searchMonth = tabelMonthFormat.format(startdate.getTime());

        String historyTableName = tabelMonthFormat.format(historyStart.getTime());

        Map<String, Object> params = new HashMap<>();
        params.put("tableName", searchMonth);

        if (null != activityStatisticalSearchBean.getOrgId() && !"".equals(activityStatisticalSearchBean.getOrgId())) {
            Organization organization = organizationDAO.selectByPrimaryKey(activityStatisticalSearchBean.getOrgId());
            params.put("orgCode", organization.getCodestr());
        } else {
            if ("01".equals(activityStatisticalSearchBean.getInitType()))
                params.put("provinceNotCount", getHQorg().getProvince());
            else if ("02".equals(activityStatisticalSearchBean.getInitType()))
                params.put("provinceCount", getHQorg().getProvince());
        }

        SimpleDateFormat searchDateFormat = new SimpleDateFormat("yyyyMMdd");

        if (null != activityStatisticalSearchBean.getDay()) {
            params.put("recorddate", searchDateFormat.format(startdate.getTime()));
            params.put("startdate", startdate.getTime());
            params.put("enddate", enddate.getTime());
        }

        //查询规则类型 10101：登录, 20199：查阅新闻, 30101：视频, 30103：在线学习
        List<String> actTypes = new ArrayList<>(10);
        actTypes.add("10101");
        actTypes.add("30101");
        actTypes.add("30103");

        //查询相关规则，获取规则ID
        List<SysScoreRule> rules = scoreRuleDAO.selectRuleListByRuleNos(actTypes);

        String[] ruleIds = new String[rules.size()];

        for (int i = 0; i < rules.size(); i++) {
            ruleIds[i] = rules.get(i).getId();
        }
        params.put("objTypes", ruleIds);

        List<SysUserScoreDtl> userScoreDtls = userScoreDtlDAO.joinActivity(params);
        //查询查阅新闻活跃量
        params.put("startdate", startdate.getTime());
        params.put("enddate", enddate.getTime());
        List<JoinActivityDTO> newsActList = footPointDAO.joinActivity(params);
        //设置历史同期对比活跃量参数
        params.put("tableName", historyTableName);
        if (null != activityStatisticalSearchBean.getDay()) {
            params.put("recorddate", searchDateFormat.format(historyStart.getTime()));
            params.put("startdate", historyStart.getTime());
            params.put("enddate", historyEnd.getTime());
        }
        //查询历史活跃度
        List<SysUserScoreDtl> historyUserScoreDtls = null;
        List<JoinActivityDTO> historyNewsActList = null;

        try {
            historyUserScoreDtls = userScoreDtlDAO.joinActivity(params);

            //查询历史查阅新闻活跃量
            params.put("startdate", historyStart.getTime());
            params.put("enddate", historyEnd.getTime());
            historyNewsActList = footPointDAO.joinActivity(params);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        Map<String, Object> resultMap = new HashMap<>();

        calculationAct(resultMap, rules, userScoreDtls, null == historyUserScoreDtls ? userScoreDtls : historyUserScoreDtls);
        setNewsAct(resultMap, newsActList, null == historyNewsActList ? newsActList : historyNewsActList);

        return resultMap;
    }

    @Override
    public Map<String, Integer> getUserCount() {
        // 返回值
        Map<String, Integer> result = new HashMap<>(16);
        // 查询登陆累计人次
        DictionaryItems dictionaryItems = dictionaryItemsDAO.selectByCodeAndDictCode(ITEM_CODE_LOGIN_TIMES, LOGIN_PARAM);
        result.put("loginTimes", null == dictionaryItems ? 0 : Integer.valueOf(dictionaryItems.getItemtext()));
        // 查询当前在线人数
        DictionaryItems loginValidItems = dictionaryItemsDAO.selectByCodeAndDictCode(LOGIN_VALID_TIME, LOGIN_PARAM);
        Integer validTime = Integer.valueOf(loginValidItems.getItemtext());
        result.put("onlineNumber", userLoginInfoDAO.countOnlineNumber(validTime));
        return result;
    }

    private void setNewsAct(Map<String, Object> resultMap, List<JoinActivityDTO> newsActList, List<JoinActivityDTO> historyNewsActList) {
        JoinActivityDTO joinActivityDTO = new JoinActivityDTO();
        //新闻阅读类型设为20199
        joinActivityDTO.setActType(20199);
        if (null != newsActList && !newsActList.isEmpty()) {
            joinActivityDTO.setActNum(newsActList.get(0).getActNum());
        } else {
            joinActivityDTO.setActNum(0);
        }

        if (null != historyNewsActList && !historyNewsActList.isEmpty()) {
            joinActivityDTO.setHistoryActNum(historyNewsActList.get(0).getActNum());
        } else {
            joinActivityDTO.setHistoryActNum(0);
        }

        resultMap.put(setActKeyName(joinActivityDTO.getActType()), joinActivityDTO);
    }

    private void calculationAct(Map<String, Object> resultMap, List<SysScoreRule> rules, List<SysUserScoreDtl> userScoreDtls, List<SysUserScoreDtl> historyUserScoreDtls) {
        for (SysScoreRule rule : rules) {
            JoinActivityDTO joinActivityDTO = new JoinActivityDTO();
            joinActivityDTO.setRuleId(rule.getId());
            joinActivityDTO.setActType(Integer.parseInt(rule.getRuleNo()));

            int actNum = setCalCulation(joinActivityDTO.getRuleId(), userScoreDtls);
            joinActivityDTO.setActNum(actNum);

            int historyActNum = setCalCulation(joinActivityDTO.getRuleId(), historyUserScoreDtls);
            joinActivityDTO.setHistoryActNum(historyActNum);

            resultMap.put(setActKeyName(joinActivityDTO.getActType()), joinActivityDTO);
        }
    }

    private int setCalCulation(String ruleId, List<SysUserScoreDtl> userScoreDtls) {
        int actNum = 0;
        for (int i = 0; i < userScoreDtls.size(); i++) {
            if (ruleId.equals(userScoreDtls.get(i).getRuleId())) {
                actNum++;
                userScoreDtls.remove(i);
                i--;
            }
        }
        return actNum;
    }

    private String setActKeyName(Integer key) {
        String keyName;
        switch (key) {
            case 10101:
                keyName = "login";
                break;
            case 20199:
                keyName = "news";
                break;
            case 30101:
                keyName = "video";
                break;
            case 30103:
                keyName = "study";
                break;
            default:
                keyName = key.toString();
                break;
        }
        return keyName;
    }

    /**
     * 查询排除的省公司（重客中心）
     *
     * @return
     */
    private String getExcludeProvince() {
        return dictionaryItemsDAO.selectByTextAndDictCode(EXCLUDE_PROVINCE, DICT_CODE_PROVINCE).getCodestr();
    }

    private Organization getHQorg() throws SystemFailureException {
        List<Organization> organizationList = organizationDAO.selectOrganizationByLevel(0);
        if (null == organizationList || organizationList.isEmpty() || organizationList.size() > 1) {
            throw new SystemFailureException("获取本部组织失败");
        }
        return organizationList.get(0);
    }

    @Override
    public PageInfo<OrgMeetingFinishRateDTOTmp> staMeCompletionInfo(String orgId, Integer yearParam, Integer season, Integer page, Integer limit, Boolean isPage) throws SystemFailureException {
        // 查询需排除的省份
        String provinceCode = getExcludeProvince();
        if (isPage) {
            PageHelper.startPage(page, limit);
        }

        //分页查询所有党支部
        List<OrgMeetingFinishRateDTOTmp> allBranchslist = organizationDAO.selectAllBranchs(orgId, provinceCode);

        return new PageInfo<>(allBranchslist);
    }

    @Override
    public List<Organization> getHQOrgs() throws SystemFailureException {
        return organizationDAO.selectOrgByParentId(null, getHQorg().getProvince(), null, getExcludeProvince());
    }

    @Override
    public OrgMeetingFinishRateDTOTmp meCompletionInfo(String orgId, Integer yearParam, Integer season, String initType, Date staDate) throws SystemFailureException {
        // 查询会议情况
        List<OrgMeetingFinishRateDTOTmp> finishInfoList = meetingFinishRate(orgId, yearParam, season, initType, 0, staDate);

        // 汇总
        OrgMeetingFinishRateDTOTmp orgMeetingFinishRateDTO = new OrgMeetingFinishRateDTOTmp();
        for (OrgMeetingFinishRateDTOTmp temp : finishInfoList) {
            orgMeetingFinishRateDTO.setTotalNum(orgMeetingFinishRateDTO.getTotalNum() + temp.getTotalNum10() + temp.getTotalNum20() + temp.getTotalNum30() + temp.getTotalNum40());
            orgMeetingFinishRateDTO.setTotalNum10(orgMeetingFinishRateDTO.getTotalNum10() + temp.getTotalNum10());
            orgMeetingFinishRateDTO.setTotalNum20(orgMeetingFinishRateDTO.getTotalNum20() + temp.getTotalNum20());
            orgMeetingFinishRateDTO.setTotalNum30(orgMeetingFinishRateDTO.getTotalNum30() + temp.getTotalNum30());
            orgMeetingFinishRateDTO.setTotalNum40(orgMeetingFinishRateDTO.getTotalNum40() + temp.getTotalNum40());
            orgMeetingFinishRateDTO.setFinishNum(orgMeetingFinishRateDTO.getFinishNum() + temp.getFinishNum10() + temp.getFinishNum20() + temp.getFinishNum30() + temp.getFinishNum40());
            orgMeetingFinishRateDTO.setCountFinishNum(orgMeetingFinishRateDTO.getCountFinishNum()
                    + (temp.getFinishNum10() > temp.getTotalNum10() ? temp.getTotalNum10() : temp.getFinishNum10())
                    + (temp.getFinishNum20() > temp.getTotalNum20() ? temp.getTotalNum20() : temp.getFinishNum20())
                    + (temp.getFinishNum30() > temp.getTotalNum30() ? temp.getTotalNum30() : temp.getFinishNum30())
                    + (temp.getFinishNum40() > temp.getTotalNum40() ? temp.getTotalNum40() : temp.getFinishNum40()));
//            logger.info("1orgMeetingFinishRateDTO.getCountFinishNum()：" + orgMeetingFinishRateDTO.getCountFinishNum());
            orgMeetingFinishRateDTO.setFinishNum10(orgMeetingFinishRateDTO.getFinishNum10() + temp.getFinishNum10());
            orgMeetingFinishRateDTO.setFinishNum20(orgMeetingFinishRateDTO.getFinishNum20() + temp.getFinishNum20());
            orgMeetingFinishRateDTO.setFinishNum30(orgMeetingFinishRateDTO.getFinishNum30() + temp.getFinishNum30());
            orgMeetingFinishRateDTO.setFinishNum40(orgMeetingFinishRateDTO.getFinishNum40() + temp.getFinishNum40());
        }
//        logger.info("2orgMeetingFinishRateDTO.getCountFinishNum()：" + orgMeetingFinishRateDTO.getCountFinishNum());
        // 设置完成率
        orgMeetingFinishRateDTO.setFinishRate(ConvertUtil.genFixPercent(orgMeetingFinishRateDTO.getCountFinishNum(), orgMeetingFinishRateDTO.getTotalNum()));
        orgMeetingFinishRateDTO.setFinishRate10(ConvertUtil.genFixPercent(orgMeetingFinishRateDTO.getFinishNum10(), orgMeetingFinishRateDTO.getTotalNum10()));
        orgMeetingFinishRateDTO.setFinishRate20(ConvertUtil.genFixPercent(orgMeetingFinishRateDTO.getFinishNum20(), orgMeetingFinishRateDTO.getTotalNum20()));
        orgMeetingFinishRateDTO.setFinishRate30(ConvertUtil.genFixPercent(orgMeetingFinishRateDTO.getFinishNum30(), orgMeetingFinishRateDTO.getTotalNum30()));
        orgMeetingFinishRateDTO.setFinishRate40(ConvertUtil.genFixPercent(orgMeetingFinishRateDTO.getFinishNum40(), orgMeetingFinishRateDTO.getTotalNum40()));

        return orgMeetingFinishRateDTO;
    }

    private List<OrgMeetingFinishRateDTOTmp> meetingFinishRate(String orgId, Integer yearParam, Integer season, String initType, int lev, Date staDate) throws SystemFailureException {
        // 查询组织信息
        String orgCode = null;
        Integer levelnum = 1;
        if (StringUtils.isNotBlank(orgId)) {
            Organization organization = organizationDAO.selectByPrimaryKey(orgId);
            if (!ValidateUtil.isValid(organization)) {
                orgCode = organization.getCodestr();
                levelnum = organization.getLevelnum() + lev;
            }
        }

        Calendar calendar = Calendar.getInstance();
        //季度的第一天的零点
        calendar.set(yearParam, null == season ? 0 : (season - 1) * 3, 1, 0, 0, 0);
        Date startTime = calendar.getTime();
        //季度的后一天的零点
        calendar.add(Calendar.MONTH, null == season ? 12 : 3);
        Date endTime = calendar.getTime();

        // 查询各组织会议完成情况
        // 查询需排除的省份
        String provinceCode = getExcludeProvince();
        Organization organizationHQ = getHQorg();
        if ("02".equals(initType))
            return organizationDAO.selectMeetingFinishRate(orgCode, levelnum, yearParam, season, null, null, startTime, endTime, organizationHQ.getProvince(), staDate);
        else
            return organizationDAO.selectMeetingFinishRate(orgCode, levelnum, yearParam, season, provinceCode, organizationHQ.getProvince(), startTime, endTime, null, staDate);
    }

    @Override
    public Map<String, GroupMeetingRateDTO> getMeetingInfoForGroup(String orgId, Integer yearParam, Integer season) {
        String codestr = null;
        if (!StringUtils.isBlank(orgId)) {
            Organization organization = organizationDAO.selectByPrimaryKey(orgId);
            if (null != organization) {
                codestr = organization.getCodestr();
            }
        }
        List<GroupMeetingInfoDTO> meetingList = planTaskDAO.selectMeetingInfoForGroup(codestr, yearParam, season);
        if (null == meetingList || meetingList.isEmpty()) {
            return null;
        }

        // 按党小组统计会议数量
        Map<String, GroupMeetingRateDTO> result = new HashMap<>(16);
        for (GroupMeetingInfoDTO temp : meetingList) {
            String orgid = temp.getOrgid();
            GroupMeetingRateDTO meetingRate;
            if (result.containsKey(orgid)) {
                meetingRate = result.get(orgid);
                meetingRate.setTotalNum(meetingRate.getTotalNum() + 1);
                meetingRate.setFinishNum(temp.getStatus() == 10 ? meetingRate.getFinishNum() + 1 : meetingRate.getFinishNum());
            } else {
                meetingRate = new GroupMeetingRateDTO();
                meetingRate.setOrgfname(temp.getOrgfname());
                meetingRate.setOrgsname(temp.getOrgsname());
                meetingRate.setOrgid(orgid);
                meetingRate.setTotalNum(1);
                meetingRate.setFinishNum(temp.getStatus() == 10 ? 1 : 0);
            }
            result.put(orgid, meetingRate);
        }

        // 计算会议完成率
        for (Map.Entry<String, GroupMeetingRateDTO> temp : result.entrySet()) {
            GroupMeetingRateDTO meetingRate = temp.getValue();
            meetingRate.setRate(ConvertUtil.genPercent(meetingRate.getFinishNum(), meetingRate.getTotalNum()));
            temp.setValue(meetingRate);
        }

        return result;
    }

    @Override
    public List<OrgMeetingFinishRateDTOTmp> getBranchRanking(String orgId, boolean isHQBranch, Integer yearParam, Integer season, String initType, Date staDate) throws SystemFailureException {

        DecimalFormat decimalFormat = new DecimalFormat("##%"); // ##.00%
        List<OrgMeetingFinishRateDTOTmp> orgMeetingFinishRateDTOList = meetingFinishRate(orgId, yearParam, season, initType, 1, staDate);
        for (OrgMeetingFinishRateDTOTmp orgMeeFinRate : orgMeetingFinishRateDTOList) {
            orgMeeFinRate.setLeader(userpositionDAO.getOrgLeaderLimitOne(orgMeeFinRate.getOrgId()));
            orgMeeFinRate.setFinishNum((orgMeeFinRate.getFinishNum10() > orgMeeFinRate.getTotalNum10() ? orgMeeFinRate.getTotalNum10() : orgMeeFinRate.getFinishNum10())
                    + (orgMeeFinRate.getFinishNum20() > orgMeeFinRate.getTotalNum20() ? orgMeeFinRate.getTotalNum20() : orgMeeFinRate.getFinishNum20())
                    + (orgMeeFinRate.getFinishNum30() > orgMeeFinRate.getTotalNum30() ? orgMeeFinRate.getTotalNum30() : orgMeeFinRate.getFinishNum30())
                    + (orgMeeFinRate.getFinishNum40() > orgMeeFinRate.getTotalNum40() ? orgMeeFinRate.getTotalNum40() : orgMeeFinRate.getFinishNum40()));
            orgMeeFinRate.setTotalNum(orgMeeFinRate.getTotalNum10() + orgMeeFinRate.getTotalNum20() + orgMeeFinRate.getTotalNum30() + orgMeeFinRate.getTotalNum40());
            orgMeeFinRate.setRate(ConvertUtil.genDoublePercent(orgMeeFinRate.getFinishNum(), orgMeeFinRate.getTotalNum()));
            orgMeeFinRate.setFinishRate(decimalFormat.format(orgMeeFinRate.getRate()));
        }
        Collections.sort(orgMeetingFinishRateDTOList, new Comparator<OrgMeetingFinishRateDTOTmp>() {
            @Override
            public int compare(OrgMeetingFinishRateDTOTmp o1, OrgMeetingFinishRateDTOTmp o2) {
                //倒序
                return Double.compare(o2.getRate(), o1.getRate());
            }
        });

        return orgMeetingFinishRateDTOList;
    }

    @Override
    public List<OrgMeetingFinishRateDTOTmp> provinceRanking(Integer yearParam, Integer season, Date staDate) {
        Map<String, String> provinces = new HashMap<>();
        // 获取字典码ID-省份
        List<SelectBean> selectBeans = dictionaryItemsDAO.selectByDictCode(DICT_CODE_PROVINCE);
        for (SelectBean selectBean : selectBeans) {
            provinces.put(selectBean.getValue(), selectBean.getName());
        }

        Calendar calendar = Calendar.getInstance();
        //季度的第一天的零点
        calendar.set(yearParam, null == season ? 0 : (season - 1) * 3, 1, 0, 0, 0);
        Date startTime = calendar.getTime();
        //季度的后一天的零点
        calendar.add(Calendar.MONTH, null == season ? 12 : 3);
        Date endTime = calendar.getTime();
        // 查询各1级组织会议完成情况
        List<OrgMeetingFinishRateDTOTmp> finishInfoList = organizationDAO.selectMeetingFinishRate(null, Integer.valueOf(1), yearParam, season, null, null, startTime, endTime, null, staDate);

        // 按完成度汇总
        for (OrgMeetingFinishRateDTOTmp omfr : finishInfoList) {
            omfr.setProvinceName(provinces.get(omfr.getProvinceId()));
            omfr.setTotalNum(omfr.getTotalNum10() + omfr.getTotalNum20() + omfr.getTotalNum30() + omfr.getTotalNum40());
            omfr.setCountFinishNum(
                    (omfr.getFinishNum10() > omfr.getTotalNum10() ? omfr.getTotalNum10() : omfr.getFinishNum10())
                            + (omfr.getFinishNum20() > omfr.getTotalNum20() ? omfr.getTotalNum20() : omfr.getFinishNum20())
                            + (omfr.getFinishNum30() > omfr.getTotalNum30() ? omfr.getTotalNum30() : omfr.getFinishNum30())
                            + (omfr.getFinishNum40() > omfr.getTotalNum40() ? omfr.getTotalNum40() : omfr.getFinishNum40()));
            // 设置完成度
            omfr.setFinishRate(ConvertUtil.genFixPercent(omfr.getCountFinishNum(), omfr.getTotalNum()));
        }
        return finishInfoList;
    }

    @Override
    public Organization getOrgHQ(Integer yearParam, Integer season, Date staDate) {
        List<Organization> organizationList = organizationDAO.selectOrganizationByLevel(0);
        if (null == organizationList || organizationList.isEmpty() || organizationList.size() > 1) {
            return null;
        }
        Organization parentOrg = organizationList.get(0);

        Calendar calendar = Calendar.getInstance();
        //季度的第一天的零点
        calendar.set(yearParam, null == season ? 0 : (season - 1) * 3, 1, 0, 0, 0);
        Date startTime = calendar.getTime();
        //季度的后一天的零点
        calendar.add(Calendar.MONTH, null == season ? 12 : 3);
        Date endTime = calendar.getTime();

        // 子级组织完成度
        List<OrgMeetingFinishRateDTOTmp> finishInfo = organizationDAO.selectMeetingFinishRate(null, parentOrg.getLevelnum() + 1, yearParam, season, null, null, startTime, endTime, parentOrg.getProvince(), staDate);
        Map<String, String> rateMap = new HashMap<>(16);
        for (OrgMeetingFinishRateDTOTmp temp : finishInfo) {
            temp.setCountFinishNum(
                    (temp.getFinishNum10() > temp.getTotalNum10() ? temp.getTotalNum10() : temp.getFinishNum10())
                            + (temp.getFinishNum20() > temp.getTotalNum20() ? temp.getTotalNum20() : temp.getFinishNum20())
                            + (temp.getFinishNum30() > temp.getTotalNum30() ? temp.getTotalNum30() : temp.getFinishNum30())
                            + (temp.getFinishNum40() > temp.getTotalNum40() ? temp.getTotalNum40() : temp.getFinishNum40()));

            temp.setTotalNum(temp.getTotalNum10() + temp.getTotalNum20() + temp.getTotalNum30() + temp.getTotalNum40());
            temp.setFinishNum(temp.getFinishNum10() + temp.getFinishNum20() + temp.getFinishNum30() + temp.getFinishNum40());
            rateMap.put(temp.getOrgId(), ConvertUtil.genFixPercent(temp.getCountFinishNum(), temp.getTotalNum()));
//            logger.info(temp.getOrgId() + ":" + ConvertUtil.genFixPercent(temp.getCountFinishNum(), temp.getTotalNum()));
        }

        // 子级组织信息
        List<Organization> childOrgList = organizationDAO.selectOrgByParentIdAndProvince(parentOrg.getId(), parentOrg.getProvince());
        for (Organization temp : childOrgList) {
            temp.setMeFinishRate(rateMap.get(temp.getId()));
//            logger.info(temp.getId() + "=" + rateMap.get(temp.getId()));
        }
        parentOrg.setChildOrgList(childOrgList);

        return parentOrg;
    }

    private Integer addNum(Integer num_src, Integer num_des) {
        if (null == num_src)
            return num_des;
        else if (null != num_des)
            return num_src + num_des;

        return num_src;
    }
}


