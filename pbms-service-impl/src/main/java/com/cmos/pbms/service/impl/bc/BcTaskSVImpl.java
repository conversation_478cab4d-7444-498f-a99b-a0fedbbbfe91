package com.cmos.pbms.service.impl.bc;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.bc.BcBranchCardInfo;
import com.cmos.pbms.beans.bc.BcQuestion;
import com.cmos.pbms.beans.bc.BcTask;
import com.cmos.pbms.beans.bc.BcTaskAnswer;
import com.cmos.pbms.beans.bh.BhOrgHonor;
import com.cmos.pbms.beans.common.Attachments;
import com.cmos.pbms.beans.dto.*;
import com.cmos.pbms.beans.enums.AttachTypeEnum;
import com.cmos.pbms.beans.enums.ProcessTypeEnum;
import com.cmos.pbms.beans.pm.Organization;
import com.cmos.pbms.beans.sys.DictionaryItems;
import com.cmos.pbms.dao.bc.*;
import com.cmos.pbms.dao.bh.BhOrgHonorDAO;
import com.cmos.pbms.dao.bn.BnBranchNewsDocsDAO;
import com.cmos.pbms.dao.common.AttachmentsDAO;
import com.cmos.pbms.dao.common.ReviewsDAO;
import com.cmos.pbms.dao.common.ThumbsDAO;
import com.cmos.pbms.dao.pm.OrganizationDAO;
import com.cmos.pbms.dao.sys.DictionaryItemsDAO;
import com.cmos.pbms.dao.sys.ReviewLogDAO;
import com.cmos.pbms.dao.sys.UsersDAO;
import com.cmos.pbms.iservice.bc.IBcTaskAnswerSV;
import com.cmos.pbms.iservice.bc.IBcTaskSV;
import com.cmos.pbms.iservice.pt.IPtPlanInfoSV;
import com.cmos.pbms.utils.ConvertUtil;
import com.cmos.pbms.utils.DateUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service(group = "pbms", retries = -1)
public class BcTaskSVImpl implements IBcTaskSV {

    private static final Logger logger = LoggerFactory.getLogger(BcTaskSVImpl.class);

    @Autowired
    private BcBranchCardInfoDAO bcBranchCardInfoDAO;
    @Autowired
    private BcTaskDAO bcTaskDAO;
    @Autowired
    private BcTaskAnswerDAO bcTaskAnswerDAO;
    @Autowired
    private BcTaskAnswerItemDAO bcTaskAnswerItemDAO;
    @Autowired
    private AttachmentsDAO attachmentsDAO;
    @Autowired
    private OrganizationDAO organizationDAO;
    @Autowired
    private BcQuestionDAO bcQuestionDAO;
    @Autowired
    private ReviewLogDAO reviewLogDAO;
    @Autowired
    private BhOrgHonorDAO bhOrgHonorDAO;
    @Autowired
    private UsersDAO usersDAO;
    @Autowired
    private BnBranchNewsDocsDAO bnBranchNewsDocsDAO;
    @Autowired
    private ThumbsDAO thumbsDAO;
    @Autowired
    private ReviewsDAO reviewsDAO;
    @Autowired
    private DictionaryItemsDAO dictionaryItemsDAO;
    @Autowired
    private IPtPlanInfoSV ptPlanInfoSV;
    @Autowired
    private IBcTaskAnswerSV bcTaskAnswerSV;

    @Override
    public BcTask getByPrimaryKey(String id) {
        return bcTaskDAO.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(BcTask bcTask) {
        return bcTaskDAO.updateByPrimaryKeySelective(bcTask);
    }

    @Override
    public PageInfo<BcTaskListDTO> getBcTaskList(String orgId, String dataDate, String bcTitle, Integer bctState, Integer page, Integer limit) {
        PageHelper.startPage(page, limit);
        return new PageInfo<>(bcTaskDAO.selectBcTaskList(orgId, dataDate, bcTitle, bctState));
    }

    @Override
    public BcTaskDetailDTO getBcTaskDetail(String bctId) {

        return bcTaskDAO.selectBcTaskDetail(bctId);
    }

    @Override
    public PageInfo<BcTaskQuestionListDTO> getBcTaskQuestionList(String bctId, Integer bctaState, String taskType, Integer page, Integer limit) {
        PageHelper.startPage(page, limit);
        List<BcTaskQuestionListDTO> bcTaskQuestionListDTOS = bcTaskAnswerDAO.selectBcTaskQuestionList(bctId, bctaState, taskType);
        generBcTaskQuestionListDTO(bcTaskQuestionListDTOS);
        return new PageInfo<>(bcTaskQuestionListDTOS);
    }

    @Override
    public BcTaskMyBranchDTO getMyBranch(String orgId, String taskDate) {
        BcTaskMyBranchDTO result = new BcTaskMyBranchDTO();
        result.setDataDate(taskDate);
        result.setOrgId(orgId);

        Organization organization = organizationDAO.selectByPrimaryKey(orgId);
        result.setOrgsName(organization.getOrgsname());
        result.setOrgfName(organization.getOrgfname());

        BcTaskRankListDTO taskInfo = bcTaskDAO.selectBcTaskByOrgAndDate(orgId, taskDate);
        if (null != taskInfo) {
            result.setPreScore(taskInfo.getPreScore());
            result.setFinScore(taskInfo.getFinScore());
            result.setvRankingNum(taskInfo.getvRankingNum());
        }

        List<Attachments> logoList = attachmentsDAO.selectListByObjIdType(orgId, AttachTypeEnum.BC_ORGLOGO.getType());
        result.setLogoList(logoList);

        return result;
    }

    @Override
    public String getMonthStartAndEnd(String taskDate) throws ParseException {
        Date date = DateUtil.parse(taskDate, "yyyyMM");

        Calendar start = Calendar.getInstance();
        start.setTime(date);
        start.set(Calendar.DAY_OF_MONTH, 1);
        Date startTime = start.getTime();

        Calendar end = Calendar.getInstance();
        end.setTime(date);
        end.add(Calendar.MONTH, 1);
        end.set(Calendar.DAY_OF_MONTH, 1);
        end.add(Calendar.DATE, -1);
        Date endTime = end.getTime();

        return DateUtil.format(startTime, "M月d日") + "-" + DateUtil.format(endTime, "M月d日");
    }

    @Override
    public Map<String, Object> getBcRankList(String taskDate) {
        // 返回值
        Map<String, Object> result = new HashMap<>(16);

        List<BcTaskRankListDTO> dataList = bcTaskDAO.selectBcTaskRankList(taskDate, null);

        // 按排名， 分类为5个列表
        List<BcTaskRankListDTO> firstList = new ArrayList<>(10);
        List<BcTaskRankListDTO> secondList = new ArrayList<>(10);
        List<BcTaskRankListDTO> thirdList = new ArrayList<>(10);
        List<BcTaskRankListDTO> fourthList = new ArrayList<>(10);
        List<BcTaskRankListDTO> fifthList = new ArrayList<>(10);
        List<String> objIds = new ArrayList<>(10);

        for (BcTaskRankListDTO temp : dataList) {
            if (null == temp.getvRankingNum()) {
                continue;
            }
            if (temp.getvRankingNum() <= 3) {
                objIds.add(temp.getOrgId());
                firstList.add(temp);
            }
            if (temp.getvRankingNum() > 3 && temp.getvRankingNum() <= 10) {
                secondList.add(temp);
            }
            if (temp.getvRankingNum() > 10 && temp.getvRankingNum() <= 50) {
                thirdList.add(temp);
            }
            if (temp.getvRankingNum() > 50 && temp.getvRankingNum() <= 100) {
                fourthList.add(temp);
            }
            if (temp.getvRankingNum() > 100) {
                fifthList.add(temp);
            }
        }
        if (CollectionUtils.isNotEmpty(objIds)) {
            List<Attachments> attachmentsList = attachmentsDAO.selectListByObjIdsAndType(objIds, AttachTypeEnum.BC_ORGLOGO.getType());
            Map<String, List<Attachments>> attMap = new HashMap<>(16);
            for (Attachments temp : attachmentsList) {
                String key = temp.getObjid();
                if (attMap.containsKey(key)) {
                    List<Attachments> attList = attMap.get(key);
                    attList.add(temp);
                } else {
                    List<Attachments> attList = new ArrayList<>(10);
                    attList.add(temp);
                    attMap.put(key, attList);
                }
            }
            for (BcTaskRankListDTO temp : firstList) {
                String key = temp.getOrgId();
                if (attMap.containsKey(key)) {
                    temp.setLogoList(attMap.get(key));
                }
            }
        }
        result.put("firstList", firstList);
        result.put("secondList", secondList);
        result.put("thirdList", thirdList);
        result.put("fourthList", fourthList);
        result.put("fifthList", fifthList);
        try {
            result.put("dateScope", getMonthStartAndEnd(taskDate));
        } catch (ParseException e) {
            logger.error("日期格式转换异常，taskDate=" + taskDate);
            result.put("dateScope", "");
        }
        return result;
    }

    @Override
    public Map<String, Object> getBcRankByKeyword(String taskDate, String keyword) {
        // 返回值
        Map<String, Object> result = new HashMap<>(16);

        List<BcTaskRankListDTO> dataList = bcTaskDAO.selectBcTaskRankList(taskDate, keyword);

        result.put("dataList", dataList);
        try {
            result.put("dateScope", getMonthStartAndEnd(taskDate));
        } catch (ParseException e) {
            logger.error("日期格式转换异常，taskDate=" + taskDate);
            result.put("dateScope", "");
        }
        return result;
    }

    @Override
    public BcTaskQuestionDetailDTO getBcTaskQuestionDetail(String bctaId) {
        BcTaskQuestionDetailDTO bcTaskQuestionDetailDTO = bcTaskAnswerDAO.selectBcTaskQuestionDetail(bctaId);
        bcTaskQuestionDetailDTO.setBcTaskAnswerItemListDTOS(bcTaskAnswerItemDAO.selectBcTaskAnswerItemListByBctaId(bcTaskQuestionDetailDTO.getBctaId()));
        bcTaskQuestionDetailDTO.setAuditReviewLogList(reviewLogDAO.selectByTypeAndObjId(ProcessTypeEnum.BC_AUDIT.getCode(), bcTaskQuestionDetailDTO.getBctaId()));
        List<BcTaskQuestionDetailDTO> children = bcTaskAnswerDAO.selectBcTaskQuestionListByQParentId(bcTaskQuestionDetailDTO.getqParentId());
        bcTaskQuestionDetailDTO.setChildren(children);

        if (null != children && !children.isEmpty()) {
            for (BcTaskQuestionDetailDTO child : children)
                child.setBcTaskAnswerItemListDTOS(bcTaskAnswerItemDAO.selectBcTaskAnswerItemListByBctaId(bcTaskQuestionDetailDTO.getBctaId()));
        }

        while (StringUtils.isNotBlank(bcTaskQuestionDetailDTO.getqParentId())) {
            BcTaskQuestionDetailDTO bcTaskQuestionDetailDTO_parent = bcQuestionDAO.selectParentBcTaskQuestionDetail(bcTaskQuestionDetailDTO.getqParentId());
            bcTaskQuestionDetailDTO_parent.addChildren(bcTaskQuestionDetailDTO);
            bcTaskQuestionDetailDTO = bcTaskQuestionDetailDTO_parent;
        }
        return bcTaskQuestionDetailDTO;
    }

    @Override
    public BcAuditOrExpertBcBranchCardInfoDetailDTO getAuditOrExpertBcTaskDetail(String bcqId, Integer bctType) {
        BcAuditOrExpertBcTaskDetailDTO src = bcQuestionDAO.selectBcAuditOrExpertBcTaskDetail(bcqId);
        BcAuditOrExpertBcTaskDetailDTO bcAuditOrExpertBcTaskDetailDTO = src;
        while (StringUtils.isNotBlank(bcAuditOrExpertBcTaskDetailDTO.getqParentid())) {
            BcAuditOrExpertBcTaskDetailDTO bcAuditOrExpertBcTaskDetailDTO_parent = bcQuestionDAO.selectBcAuditOrExpertBcTaskDetail(bcAuditOrExpertBcTaskDetailDTO.getqParentid());
            bcAuditOrExpertBcTaskDetailDTO_parent.setChild(bcAuditOrExpertBcTaskDetailDTO);
            bcAuditOrExpertBcTaskDetailDTO = bcAuditOrExpertBcTaskDetailDTO_parent;
        }
        BcAuditOrExpertBcBranchCardInfoDetailDTO bcAuditOrExpertBcBranchCardInfoDetailDTO = bcBranchCardInfoDAO.selectBcAuditOrExpertBcBranchCardInfoDetail(bcAuditOrExpertBcTaskDetailDTO.getBcId());
        bcAuditOrExpertBcBranchCardInfoDetailDTO.setBcAuditOrExpertBcTaskDetailDTO(bcAuditOrExpertBcTaskDetailDTO);

        //结构化组织架构
        //机构化组织的答案

        return bcAuditOrExpertBcBranchCardInfoDetailDTO;
    }

    @Override
    public PageInfo<BcToDoTaskListDTO> getToDoBcTaskList(String orgId, Integer todoState, Integer page, Integer limit, String userId, String userRoleId) {
        PageHelper.startPage(page, limit);
        List<BcToDoTaskListDTO> dataList = bcTaskDAO.selectToDoBcTaskList(orgId, todoState, userId, userRoleId);
        for (BcToDoTaskListDTO temp : dataList) {
            if (StringUtils.isBlank(temp.getqTitle())) {
                Map<String, String> planTinyTypeMap = ptPlanInfoSV.getPlanTinyTypeMap();
                temp.setqTitle(planTinyTypeMap.containsKey(temp.getqCollectType()) ? planTinyTypeMap.get(temp.getqCollectType()) : "");
            }
        }
        return new PageInfo<>(dataList);
    }

    @Override
    public BcTaskBranchAbstractDTO getBcTaskBranchAbstract(String orgId, String taskDate) throws ParseException {
        BcTaskBranchAbstractDTO result = new BcTaskBranchAbstractDTO();
        result.setOrgId(orgId);

        Organization organization = organizationDAO.selectByPrimaryKey(orgId);
        result.setOrgsName(organization.getOrgsname());
        result.setOrgfName(organization.getOrgfname());

        BcTaskRankListDTO taskInfo = bcTaskDAO.selectBcTaskByOrgAndDate(orgId, taskDate);
        if (null != taskInfo) {
            result.setPreScore(taskInfo.getPreScore());
            result.setFinScore(taskInfo.getFinScore());
            result.setvRankingNum(taskInfo.getvRankingNum());
        }

        List<Attachments> logoList = attachmentsDAO.selectListByObjIdType(orgId, AttachTypeEnum.BC_ORGLOGO.getType());
        result.setLogoList(logoList);

        // 时间参数处理
        Date date = DateUtil.parse(taskDate, "yyyyMM");
        // 月初
        Calendar start = Calendar.getInstance();
        start.setTime(date);
        start.set(Calendar.DAY_OF_MONTH, 1);
        Date startTime = start.getTime();
        // 月末
        Calendar end = Calendar.getInstance();
        end.setTime(date);
        end.add(Calendar.MONTH, 1);
        end.set(Calendar.DAY_OF_MONTH, 1);
        end.add(Calendar.DATE, -1);
        Date endTime = end.getTime();
        List<BhOrgHonor> honorList = bhOrgHonorDAO.selectOrgHonorByOrgIdAndDateScope(orgId, startTime, endTime);
        result.setHonorList(honorList);

        return result;
    }

    @Override
    public BcBaoleiScoreDTO getBcBaoleiScoreInfo(String orgId, String taskDate) {
        BcBaoleiScoreDTO result = new BcBaoleiScoreDTO();
        BcTaskRankListDTO taskDetail = bcTaskDAO.selectBcTaskByOrgAndDate(orgId, taskDate);
        if (null == taskDetail) {
            return null;
        }
        result.setFinScore(taskDetail.getFinScore());
        result.setOrgId(taskDetail.getOrgId());
        result.setTaskId(taskDetail.getTaskId());

        BcBranchCardInfo bcBranchCardInfo = bcBranchCardInfoDAO.selectByDataDate(taskDate);
        result.setFortressDesc(bcBranchCardInfo.getFortressDesc());

        // 题目隶属关系
        Map<String, String> questionRelationMap = new HashMap<>(16);
        List<BcBaoleiDetailListDTO> scoreList = new ArrayList<>(10);
        List<BcQuestionRelationDTO> relationList = bcQuestionDAO.selectQuestionRelation(bcBranchCardInfo.getId());
        for (BcQuestionRelationDTO temp : relationList) {
            if (StringUtils.isBlank(temp.getRootId())) {
                if (StringUtils.isBlank(temp.getRootBakId())) {
                    questionRelationMap.put(temp.getqId(), temp.getqId());
                } else {
                    questionRelationMap.put(temp.getqId(), temp.getRootBakId());
                }
            } else {
                questionRelationMap.put(temp.getqId(), temp.getRootId());
            }

            if (Integer.valueOf(1).equals(temp.getqLevel())) {
                BcBaoleiDetailListDTO score = new BcBaoleiDetailListDTO();
                score.setqId(temp.getqId());
                score.setqTitle(temp.getqTitle());
                score.setqWeights(temp.getqWeights());
                scoreList.add(score);
            }
        }
        // 答题得分
        List<BcQuestionRelationDTO> questionRelationList = bcTaskAnswerDAO.selectQuestionAndScoreByBctId(taskDetail.getTaskId());

        Map<String, Integer> scoreMap = new HashMap<>(16);
        for (BcQuestionRelationDTO temp : questionRelationList) {
            String key = questionRelationMap.get(temp.getqId());
            if (scoreMap.containsKey(key)) {
                scoreMap.put(key, scoreMap.get(key) + (null == temp.getExpertScore() ? 0 : temp.getExpertScore()));
            } else {
                scoreMap.put(key, (null == temp.getExpertScore() ? 0 : temp.getExpertScore()));
            }
        }

        for (BcBaoleiDetailListDTO temp : scoreList) {
            String key = temp.getqId();
            temp.setTheoryScore(scoreMap.containsKey(key) ? scoreMap.get(key) : 0);
            temp.setFinalScore(divide_0(temp.getTheoryScore() * temp.getqWeights(), 100));
        }
        result.setScoreList(scoreList);

        return result;
    }

    private Double divide_0(Integer src, Integer divideBy) {
        DecimalFormat df = new DecimalFormat("#.##");
        return Double.valueOf(df.format(Double.valueOf(src) / Double.valueOf(divideBy)));
    }

    private Integer getAnswerScore(String qId, Integer isBottom, String taskId, Integer score) {
        if (Integer.valueOf(0).equals(isBottom)) {
            // 父级题目
            List<BcQuestionAnswerDTO> answerList = bcTaskAnswerDAO.selectQuestionAnswerByParentQId(qId, taskId);
            for (BcQuestionAnswerDTO temp : answerList) {
                score += getAnswerScore(temp.getqId(), temp.getqType(), taskId, 0);
            }
        } else {
            // 底层题目
            BcTaskAnswer taskAnswer = bcTaskAnswerDAO.selectQuestionAnswerByQId(qId, taskId);
            score += ((null == taskAnswer || null == taskAnswer.getExpertScore()) ? 0 : taskAnswer.getExpertScore());
        }
        return score;
    }

    @Override
    public JSONObject getBcLiuhaoScoreInfo(String orgId, String taskDate) {
        JSONObject result = new JSONObject();
        BcTaskRankListDTO taskDetail = bcTaskDAO.selectBcTaskByOrgAndDate(orgId, taskDate);
        if (null == taskDetail) {
            return null;
        }

        int startLevel = 0;
        List<BcQuestionLiuhaoDTO> liuhaoLevel2QuestionList = bcQuestionDAO.selectQuestionByParentCode(taskDate, "lhzb");
        JSONArray liuhaoList = new JSONArray();
        for (BcQuestionLiuhaoDTO question : liuhaoLevel2QuestionList) {
            JSONObject singleHao = new JSONObject();
            int score = getAnswerScore(question.getqId(), question.getqIsBottom(), taskDetail.getTaskId(), 0);
            singleHao.put("qTitle", question.getqTitle());
            singleHao.put("qScore", score);
            singleHao.put("qOrder", question.getqOrder());
            singleHao.put("qFullScore", question.getqScore());
            singleHao.put("qId", question.getqId());
            liuhaoList.add(singleHao);
            if (question.getqScore() == Integer.valueOf(score)) {
                startLevel++;
            }
        }
        result.put("startLevel", startLevel);
        result.put("scoreList", liuhaoList);
        result.put("taskId", taskDetail.getTaskId());

        return result;
    }

    @Override
    public JSONObject getBcSingleHao(String qId, String taskId) {
        BcQuestion bcQuestion = bcQuestionDAO.selectByPrimaryKey(qId);
        switch (bcQuestion.getqCollectType()) {
            case "lhzb_bzjsh_zzjgpz":
                JSONObject result1 = new JSONObject();
                result1.put("qTitle", bcQuestion.getqTitle());
                BcTaskAnswer bcTaskAnswer1 = bcTaskAnswerDAO.selectQuestionAnswerByQId(qId, taskId);
                result1.put("questionDesc", bcTaskAnswer1.getFinScoreRemake());
                List<BcLiuhaoOrgFullDTO> itemList1 = bcTaskAnswerItemDAO.selectOrgFull(qId, taskId);
                Map<String, Integer> postMap = getOrgPostMap();
                JSONArray shujiList = new JSONArray();
                Map<String, BcUserPostDTO> leaderObj = new HashMap<>();
                JSONObject content1 = new JSONObject();
                for (BcLiuhaoOrgFullDTO temp : itemList1) {
                    if (StringUtils.isBlank(temp.getObjId())) {
                        // 该题目无答案
                        continue;
                    }
                    String userPost = temp.getObjName();
                    if ("ZHIBUWEIYUAN".equals(temp.getqCollectType()) || "DANGXIAOZUZHANG".equals(temp.getqCollectType())) {
                        // 支部委员不属于具体职务，排除掉
                        continue;
                    }

                    BcUserPostDTO user = new BcUserPostDTO();
                    user.setUserName(temp.getUserName());
                    user.setUserPost(userPost);
                    user.setProfilephoto(temp.getProfilephoto());
                    user.setPostOrder(postMap.containsKey(userPost) ? postMap.get(userPost) : 9999);

                    if ("DANGWEISHUJI".equals(temp.getqCollectType()) && CollectionUtils.isEmpty(shujiList)) {
                        shujiList.add(user);
                    } else {
                        String userName = temp.getUserName();
                        if (leaderObj.containsKey(userName)) {
                            BcUserPostDTO newObj = leaderObj.get(userName);
                            newObj.setUserPost(newObj.getUserPost().concat("," + userPost));
                            if (newObj.getPostOrder() > user.getPostOrder()) {
                                newObj.setPostOrder(user.getPostOrder());
                            }
                            leaderObj.put(userName, newObj);
                        } else {
                            leaderObj.put(userName, user);
                        }
                    }
                }

                // 按字典职务排序
                List<BcUserPostDTO> sortArr = new ArrayList<BcUserPostDTO>(leaderObj.values());
                Collections.sort(sortArr, new Comparator<BcUserPostDTO>() {
                    @Override
                    public int compare(BcUserPostDTO o1, BcUserPostDTO o2) {
                        return o1.getPostOrder().compareTo(o2.getPostOrder());
                    }
                });

                content1.put("shujiList", shujiList);
                content1.put("leaderList", sortArr);
                result1.put("content", content1);
                return result1;
            case "lhzb_dyglh_dydljf":
                JSONObject result2 = new JSONObject();
                result2.put("qTitle", bcQuestion.getqTitle());
                BcTaskAnswer bcTaskAnswer2 = bcTaskAnswerDAO.selectQuestionAnswerByQId(qId, taskId);
                result2.put("questionDesc", bcTaskAnswer2.getFinScoreRemake());
                List<BcLiuhaoOrgFullDTO> itemList2 = bcTaskAnswerItemDAO.selectQuestionAndAnswerItemByParentIdAndTaskId(qId, taskId);
                int scoreRank = 0;
                int loginNum = 0;
                int unLoginNum = 0;
                JSONObject content2 = new JSONObject();
                for (BcLiuhaoOrgFullDTO temp : itemList2) {
                    if (StringUtils.isBlank(temp.getObjId())) {
                        // 该题目无答案
                        continue;
                    }
                    if ("lhzb_dyglh_dydljf_jf".equals(temp.getqCollectType())) {
                        scoreRank = Integer.valueOf(temp.getObjCode());
                    } else {
                        if ("0".equals(temp.getObjCode())) {
                            unLoginNum++;
                        } else if ("1".equals(temp.getObjCode())) {
                            loginNum++;
                        }
                    }
                }
                content2.put("loginNum", loginNum);
                content2.put("unLoginNum", unLoginNum);
                int totalNum = loginNum + unLoginNum;
                content2.put("loginRate", 0 == totalNum ? 0 : loginNum / totalNum);
                content2.put("loginRateStr", ConvertUtil.genFixFormatPercent(loginNum, totalNum));
                result2.put("scoreRank", scoreRank);
                result2.put("content", content2);
                return result2;
            case "lhzb_zdlsh_zdlsdw":
                JSONObject result3 = getAnswerWithMeeting(qId, taskId);
                return result3;
            case "lhzb_zzshh_ztdr":
                JSONObject result4 = new JSONObject();
                String questionDesc = "";
                List<BcLiuhaoImplementationMeDTO> itemList3 = bcTaskAnswerItemDAO.selectOrgLifeActivity(qId, taskId);
                JSONArray array4 = new JSONArray();
                for (BcLiuhaoImplementationMeDTO temp : itemList3) {
                    if (StringUtils.isBlank(questionDesc)) {
                        questionDesc = temp.getFinScoreRemake();
                    }

                    if (StringUtils.isBlank(temp.getMeId())) {
                        // 该题目无答案
                        continue;
                    }

                    JSONObject meeting = new JSONObject();
                    meeting.put("meId", temp.getMeId());
                    meeting.put("topic", temp.getTopic());
                    meeting.put("startTime", temp.getStartTime());
                    meeting.put("endTime", temp.getEndTime());
                    meeting.put("addr", temp.getAddr());
                    meeting.put("summary", temp.getSummary());
                    array4.add(meeting);
                }
                result4.put("questionDesc", questionDesc);
                result4.put("questionList", array4);
                return result4;
            case "lhzb_zzshh_zbwyh":
            case "lhzb_zzshh_zbdydh":
            case "lhzb_zzshh_dxzh":
            case "lhzb_zzshh_dk":
                JSONObject result5 = getAnswerWithMeeting(qId, taskId);
                return result5;
            case "lhzb_jcgzh_kzlyhd":
                JSONObject result6 = new JSONObject();
                result6.put("qTitle", bcQuestion.getqTitle());
                BcTaskAnswer bcTaskAnswer6 = bcTaskAnswerDAO.selectQuestionAnswerByQId(qId, taskId);
                result6.put("questionDesc", bcTaskAnswer6.getFinScoreRemake());
                List<BcLiuhaoBaseWorkDTO> itemList6 = bcTaskAnswerItemDAO.selectDoWork(qId, taskId);
                JSONArray yhdcsList = new JSONArray();
                JSONArray ysssbList = new JSONArray();
                JSONArray ybzbsList = new JSONArray();
                JSONArray ysbzlList = new JSONArray();
                JSONArray yglzdList = new JSONArray();
                JSONArray yryjfbzList = new JSONArray();
                JSONObject content6 = new JSONObject();
                for (BcLiuhaoBaseWorkDTO temp : itemList6) {
                    if (StringUtils.isBlank(temp.getFileUrl())) {
                        // 该题目无答案
                        continue;
                    }
                    JSONObject user = new JSONObject();
                    user.put("fileName", temp.getAttFileName());
                    user.put("fileUrl", temp.getFileUrl());
                    if ("lhzb_jcgzh_kzlyhd_yhdcs".equals(temp.getqCollectType())) {
                        yhdcsList.add(user);
                    } else if ("lhzb_jcgzh_kzlyhd_ysbss".equals(temp.getqCollectType())) {
                        ysssbList.add(user);
                    } else if ("lhzb_jcgzh_kzlyhd_ybzbs".equals(temp.getqCollectType())) {
                        ybzbsList.add(user);
                    } else if ("lhzb_jcgzh_kzlyhd_ysbzl".equals(temp.getqCollectType())) {
                        ysbzlList.add(user);
                    } else if ("lhzb_jcgzh_kzlyhd_yglzd".equals(temp.getqCollectType())) {
                        yglzdList.add(user);
                    } else if ("lhzb_jcgzh_kzlyhd_yryjfbz".equals(temp.getqCollectType())) {
                        yryjfbzList.add(user);
                    }
                }
                content6.put("有活动场所", yhdcsList);
                content6.put("有设施设备", ysssbList);
                content6.put("有标志标识", ybzbsList);
                content6.put("有书报专栏", ysbzlList);
                content6.put("有管理制度", yglzdList);
                content6.put("有人员经费保障", yryjfbzList);

                result6.put("content", content6);
                return result6;
            case "lhzb_zyfhh_kzdqhd":
                JSONObject result7 = new JSONObject();
                result7.put("qTitle", bcQuestion.getqTitle());
                BcTaskAnswer bcTaskAnswer7 = bcTaskAnswerDAO.selectQuestionAnswerByQId(qId, taskId);
                result7.put("questionDesc", bcTaskAnswer7.getFinScoreRemake());
                List<BcLiuhaoBaseWorkDTO> itemList7 = bcTaskAnswerItemDAO.selectDoWork(qId, taskId);
                JSONArray fileList = new JSONArray();
                JSONObject content7 = new JSONObject();
                for (BcLiuhaoBaseWorkDTO temp : itemList7) {
                    if (StringUtils.isBlank(temp.getFileUrl())) {
                        // 该题目无答案
                        continue;
                    }
                    JSONObject user = new JSONObject();
                    user.put("fileName", temp.getAttFileName());
                    user.put("fileUrl", temp.getFileUrl());
                    if ("lhzb_zyfhh_kzdqhd_fjsm".equals(temp.getqCollectType())) {
                        fileList.add(user);
                    }
                }
                content7.put("附件说明", fileList);

                result7.put("content", content7);
                return result7;
            default:
                return null;
        }
    }

    private JSONObject getAnswerWithMeeting(String qId, String taskId) {
        JSONObject result3 = new JSONObject();
        String questionDesc = "";
        List<BcLiuhaoImplementationMeDTO> itemList3 = bcTaskAnswerItemDAO.selectImplementation(qId, taskId);
        JSONArray array3 = new JSONArray();
        for (BcLiuhaoImplementationMeDTO temp : itemList3) {
            if (StringUtils.isBlank(questionDesc)) {
                questionDesc = temp.getFinScoreRemake();
            }

            if (StringUtils.isBlank(temp.getMeId())) {
                // 该题目无答案
                continue;
            }

            JSONObject meeting = new JSONObject();
            meeting.put("meId", temp.getMeId());
            meeting.put("topic", temp.getTopic());
            meeting.put("startTime", temp.getStartTime());
            meeting.put("endTime", temp.getEndTime());
            meeting.put("jiheyuanId", temp.getJiheyuanId());
            meeting.put("jiheyuanName", temp.getJiheyuanName());
            meeting.put("jiheyuanPhone", temp.getJiheyuanPhone());
            meeting.put("profilephoto", temp.getProfilephoto());
            array3.add(meeting);
        }
        result3.put("questionDesc", questionDesc);
        result3.put("questionList", array3);
        return result3;
    }

    @Override
    public JSONObject getLiuhaoQuestion(String orgId, String taskDate) {
        JSONObject result = new JSONObject();
        JSONArray data = new JSONArray();
        BcTaskRankListDTO taskDetail = bcTaskDAO.selectBcTaskByOrgAndDate(orgId, taskDate);
        if (null == taskDetail) {
            return null;
        }

        String taskId = taskDetail.getTaskId();
        List<BcQuestionLiuhaoDTO> liuhaoLevel2QuestionList = bcQuestionDAO.selectQuestionByParentCode(taskDate, "lhzb");
        for (BcQuestionLiuhaoDTO question : liuhaoLevel2QuestionList) {
            switch (question.getqTitle()) {
                case "班子建设好":
                    genSingleHaoQuestionDetailForFourLevel(data, taskId, question);
                    break;
                case "党员管理好":
                    genSingleHaoQuestionDetailForThreeLevel(data, taskId, question);
                    break;
                case "制度落实好":
                    genSingleHaoQuestionDetailForThreeLevel(data, taskId, question);
                    break;
                case "组织生活好":
                    genSingleHaoQuestionDetailForThreeLevel(data, taskId, question);
                    break;
                case "基础工作好":
                    genSingleHaoQuestionDetailForFourLevel(data, taskId, question);
                    break;
                case "作用发挥好":
                    genSingleHaoQuestionDetailForFourLevel(data, taskId, question);
                    break;
                default:
                    break;
            }
        }
        result.put("questionList", data);
        result.put("taskId", taskId);
        return result;
    }

    private Map<String, Integer> getOrgPostMap() {
        Map<String, Integer> map = new HashMap<>(16);
        List<DictionaryItems> itemList = dictionaryItemsDAO.selectEnabledByDictCode("PMORGPOST");
        for (DictionaryItems temp : itemList) {
            map.put(temp.getItemtext(), temp.getOrdernum());
        }
        return map;
    }

    @Override
    public JSONObject getBaseOrgInfo(String orgId, String taskDate) throws ParseException {
        JSONObject result = new JSONObject();

        BcTaskRankListDTO taskDetail = bcTaskDAO.selectBcTaskByOrgAndDate(orgId, taskDate);
        if (null == taskDetail) {
            return null;
        }

        // 组织名称信息
        Organization organization = organizationDAO.selectByPrimaryKey(orgId);
        result.put("orgsName", organization.getOrgsname());
        result.put("orgfName", organization.getOrgfname());
        result.put("foundDate", organization.getFoundDate());
        result.put("electionDhangeDate", organization.getElectionChangeDate());

        // logo图片列表
        List<Attachments> logoList = attachmentsDAO.selectListByObjIdType(orgId, AttachTypeEnum.BC_ORGLOGO.getType());
        result.put("logoList", logoList);

        // 组织荣誉
        // 时间参数处理
        Date date = DateUtil.parse(taskDate, "yyyyMM");
        // 月初
        Calendar start = Calendar.getInstance();
        start.setTime(date);
        start.set(Calendar.DAY_OF_MONTH, 1);
        Date startTime = start.getTime();
        // 月末
        Calendar end = Calendar.getInstance();
        end.setTime(date);
        end.add(Calendar.MONTH, 1);
        end.set(Calendar.DAY_OF_MONTH, 1);
        end.add(Calendar.DATE, -1);
        Date endTime = end.getTime();
        List<BhOrgHonor> honorList = bhOrgHonorDAO.selectOrgHonorByOrgIdAndDateScope(orgId, startTime, endTime);
        if (CollectionUtils.isNotEmpty(honorList)) {
            Map<String, String> map = getHonorDescMap();
            for (BhOrgHonor temp : honorList) {
                temp.setIssuerTypeStr(map.containsKey(temp.getIssuerType()) ? map.get(temp.getIssuerType()) : "");
            }
        }
        result.put("honorList", honorList);

        // 说明
        result.put("desription", null); // 和需求沟通，该处说明为写死的值，故后端不再给值，前端写死

        // 支委会（支部班子健全）
        BcQuestion bcQuestion1 = bcQuestionDAO.selectQuestionByDateAndCode(taskDate, "lhzb_bzjsh_zzjgpz");
        List<BcLiuhaoOrgFullDTO> itemList1 = bcTaskAnswerItemDAO.selectOrgFull(bcQuestion1.getId(), taskDetail.getTaskId());

        Map<String, BcUserPostDTO> leaderObj = new HashMap<>(16);
        Map<String, Integer> postMap = getOrgPostMap();
        for (BcLiuhaoOrgFullDTO temp : itemList1) {
            if (StringUtils.isBlank(temp.getObjId())) {
                // 该题目无答案
                continue;
            }
            String userName = temp.getUserName();
            String userPost = temp.getObjName();
            if ("ZHIBUWEIYUAN".equals(temp.getqCollectType()) || "DANGXIAOZUZHANG".equals(temp.getqCollectType())) {
                // 支部委员不属于具体职务，排除掉
                continue;
            }

            BcUserPostDTO user = new BcUserPostDTO();
            user.setUserName(userName);
            user.setUserPost(userPost);
            user.setProfilephoto(temp.getProfilephoto());
            user.setPostOrder(postMap.containsKey(userPost) ? postMap.get(userPost) : 9999);

            // 按姓名归类职务
            if (leaderObj.containsKey(userName)) {
                BcUserPostDTO newObj = leaderObj.get(userName);
                newObj.setUserPost(newObj.getUserPost().concat("," + userPost));
                if (newObj.getPostOrder() > user.getPostOrder()) {
                    newObj.setPostOrder(user.getPostOrder());
                }
                leaderObj.put(userName, newObj);
            } else {
                leaderObj.put(userName, user);
            }
        }

        // 按字典职务排序
        List<BcUserPostDTO> sortArr = new ArrayList<BcUserPostDTO>(leaderObj.values());
        Collections.sort(sortArr, new Comparator<BcUserPostDTO>() {
            @Override
            public int compare(BcUserPostDTO o1, BcUserPostDTO o2) {
                return o1.getPostOrder().compareTo(o2.getPostOrder());
            }
        });

        result.put("zwhList", sortArr);

        // 党小组（含小组长）
        List<SelectBean> childList = organizationDAO.selectOrgSelectByParentId(orgId);
        DictionaryItems postDict = dictionaryItemsDAO.selectByCodeAndDictCode("DANGXIAOZUZHANG", "PMORGPOST");
        String dxzzPostId = postDict.getId();
        JSONArray groupList = new JSONArray();
        for (SelectBean temp : childList) {
            List<UserListBean> userList = usersDAO.selectUsersByOrgAndPost(temp.getValue(), dxzzPostId);
            if (CollectionUtils.isEmpty(userList)) {
                continue;
            }
            UserListBean dxzz = userList.get(0);

            JSONObject group = new JSONObject();
            group.put("orgName", temp.getName());
            group.put("userName", dxzz.getUsername());
            group.put("profilephoto", dxzz.getProfilephoto());
            groupList.add(group);
        }
        result.put("groupList", groupList);

        // 党员（党员管理好）
        BcQuestion bcQuestion3 = bcQuestionDAO.selectQuestionByDateAndCode(taskDate, "lhzb_dyglh_dydljf");
        List<BcLiuhaoOrgFullDTO> itemList3 = bcTaskAnswerItemDAO.selectMemberMng(bcQuestion3.getId(), taskDetail.getTaskId());
        JSONArray dyList = new JSONArray();
        for (BcLiuhaoOrgFullDTO temp : itemList3) {
            if (StringUtils.isBlank(temp.getObjId())) {
                // 该题目无答案
                continue;
            }
            if ("lhzb_dyglh_dydljf_dl".equals(temp.getqCollectType())) {
                JSONObject user = new JSONObject();
                user.put("userName", temp.getUserName());
                user.put("profilephoto", temp.getProfilephoto());
                dyList.add(user);
            }
        }
        result.put("dyList", dyList);

        return result;
    }

    private Map<String, String> getHonorDescMap() {
        Map<String, String> map = new HashMap<>(16);
        List<DictionaryItems> itemList = dictionaryItemsDAO.selectEnabledByDictCode("org_issuer_type_config");
        for (DictionaryItems temp : itemList) {
            map.put(temp.getCodestr(), temp.getItemtext());
        }
        return map;
    }

    @Override
    public PageInfo<BnBranchNewsListDTO> getBcNewsList(Integer pageNum, Integer pageSize, String orgId, String taskDate) {
        BcTaskRankListDTO taskDetail = bcTaskDAO.selectBcTaskByOrgAndDate(orgId, taskDate);
        if (null == taskDetail) {
            return null;
        }

        BcQuestion question = bcQuestionDAO.selectQuestionByDateAndCode(taskDate, "zbxxs");
        if (null == question) {
            return null;
        }

        PageHelper.startPage(pageNum, pageSize);
//        List<BnBranchNewsListDTO> dataList = bcTaskAnswerItemDAO.getBcBnNewsList(question.getId(), taskDetail.getTaskId());
        List<BnBranchNewsListDTO> dataList = bcTaskAnswerItemDAO.getBcBnNewsList_2020(taskDetail.getOrgId());
        generBnBranchNewsListDTOList(dataList);

        return new PageInfo<>(dataList);
    }

    @Override
    public List<String> getTaskDateList() {
        List<String> taskDateList = new ArrayList<>(10);
        List<BcBranchCardInfo> bcBranchCardInfoList = bcBranchCardInfoDAO.selectTaskDateList();
        for (BcBranchCardInfo temp : bcBranchCardInfoList) {
            taskDateList.add(temp.getDataDate());
        }
        return taskDateList;
    }

    @Override
    public JSONObject getBcHistory(String orgId) throws ParseException {
        // 返回值
        JSONObject result = new JSONObject();
        // 组织基础信息
        Organization organization = organizationDAO.selectByPrimaryKey(orgId);
        result.put("orgfname", organization.getOrgfname());
        result.put("orgsname", organization.getOrgsname());
        result.put("picture", organization.getPicture());
        // 查询往期排名
        List<BcHistoryRankDTO> rankList = bcTaskDAO.selectBcHistoryRank(orgId);
        JSONArray rankArr = new JSONArray();
        if (CollectionUtils.isNotEmpty(rankList)) {
            if (rankList.size() == 1) {
                BcHistoryRankDTO rankDTO = rankList.get(0);
                JSONObject monthRank = new JSONObject();
                monthRank.put("orgId", orgId);
                monthRank.put("vRankingNum", rankDTO.getvRankingNum());
                monthRank.put("dataDate", rankDTO.getDataDate());
                monthRank.put("diffWithLastMonth", "--");
                rankArr.add(monthRank);
            } else {
                Map<String, Integer> rankMap = new HashMap<>(16);
                for (BcHistoryRankDTO rank : rankList) {
                    rankMap.put(rank.getDataDate(), rank.getvRankingNum());
                }

                List<String> monthList = getMonthListByStartAndEnd(rankList.get(0).getDataDate(), rankList.get(rankList.size() - 1).getDataDate());

                for (String month : monthList) {
                    JSONObject monthRank = new JSONObject();

                    Integer currRank = rankMap.containsKey(month) ? rankMap.get(month) : null;
                    String lastMonth = getLastMonth(month);
                    Integer lastRank = rankMap.containsKey(lastMonth) ? rankMap.get(lastMonth) : null;

                    monthRank.put("orgId", orgId);
                    monthRank.put("vRankingNum", currRank);
                    monthRank.put("dataDate", month);
                    monthRank.put("diffWithLastMonth", (null == currRank || null == lastRank) ? "--" : String.valueOf(lastRank - currRank));
                    rankArr.add(monthRank);
                }
            }
        }
        result.put("historyRankList", rankArr);
        return result;
    }

    @Override
    public String getLastMonth() {
        BcBranchCardInfo bcBranchCardInfo = bcBranchCardInfoDAO.selectLastMonth();
        return null == bcBranchCardInfo ? null : bcBranchCardInfo.getDataDate();
    }

    @Override
    public BcToDoTaskDetailDTO getLastCycleAmQuestionDetail(String bctaId) {
        String lastCycle_BctaId = getLastCycleBctaId(bctaId);
        if (StringUtils.isBlank(lastCycle_BctaId))
            return null;
        return getToDoBcTaskDetail(lastCycle_BctaId);
    }

    private String getLastCycleBctaId(String bctaId) {
        BcTaskAnswer bcTaskAnswer = bcTaskAnswerDAO.selectByPrimaryKey(bctaId);
        BcTask bcTask = bcTaskDAO.selectByPrimaryKey(bcTaskAnswer.getBctId());
        BcQuestion bcQuestion = bcQuestionDAO.selectByPrimaryKey(bcTaskAnswer.getBcqId());
        BcBranchCardInfo bcBranchCardInfo_LastCycle = bcBranchCardInfoDAO.selectLastCycle(bcQuestion.getBcId());
        return bcTaskAnswerDAO.selectLastCycle_bctaId(bcBranchCardInfo_LastCycle.getId(), bcTask.getOrgId(), bcQuestion.getqCollectType());
    }

    private List<String> getMonthListByStartAndEnd(String startMonth, String endMonth) throws ParseException {
        List<String> monthList = new ArrayList<>(10);
        monthList.add(startMonth);
        if (!startMonth.equals(endMonth)) {
            getMonthList(getLastMonth(startMonth), endMonth, monthList);
        }
        return monthList;
    }

    private void getMonthList(String lastMonth, String endMonth, List<String> monthList) throws ParseException {
        if (!lastMonth.equals(endMonth)) {
            monthList.add(lastMonth);
            getMonthList(getLastMonth(lastMonth), endMonth, monthList);
        } else {
            monthList.add(endMonth);
        }
    }

    private String getLastMonth(String month) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(DateUtil.parse(month, "yyyyMM"));
        cal.add(Calendar.MONTH, -1);
        return DateUtil.format(cal.getTime(), "yyyyMM");
    }

    private void genSingleHaoQuestionDetailForFourLevel(JSONArray result, String taskId, BcQuestionLiuhaoDTO question) {
        JSONObject data = new JSONObject();
        int totalScore = 0;

        List<BcQuestion> childQuestionList = bcQuestionDAO.selectByParentId(question.getqId());
        JSONArray questionList = new JSONArray();
        for (BcQuestion temp : childQuestionList) {
            List<BcQuestion> bottomQuestionList = bcQuestionDAO.selectByParentId(temp.getId());
            JSONArray bottomList = new JSONArray();
            for (BcQuestion bottomTemp : bottomQuestionList) {
                JSONObject bottomData = new JSONObject();
                bottomData.put("qOrder", bottomTemp.getqOrder());
                bottomData.put("qTitle", bottomTemp.getqTitle());
                bottomData.put("qId", bottomTemp.getId());
                bottomData.put("qCollectType", bottomTemp.getqCollectType());
                bottomData.put("qScore", bottomTemp.getqScore());
                bottomData.put("actualScore", getAnswerScore(bottomTemp.getId(), bottomTemp.getqType(), taskId, 0));
                bottomList.add(bottomData);
            }

            JSONObject dataTemp = new JSONObject();
            dataTemp.put("questionList", bottomList);
            dataTemp.put("qOrder", temp.getqOrder());
            dataTemp.put("qId", temp.getId());
            dataTemp.put("qCollectType", temp.getqCollectType());
            dataTemp.put("qTitle", temp.getqTitle());
            dataTemp.put("qScore", temp.getqScore());
            dataTemp.put("actualScore", getAnswerScore(temp.getId(), temp.getqType(), taskId, 0));
            totalScore += Integer.valueOf(dataTemp.get("actualScore").toString());
            questionList.add(dataTemp);
        }
        data.put("questionList", questionList);

        data.put("qOrder", question.getqOrder());
        data.put("qId", question.getqId());
        data.put("qTitle", question.getqTitle());
        data.put("qScore", question.getqScore());
        data.put("actualScore", totalScore);
        data.put("qCollectType", question.getqCollectType());
        result.add(data);
    }

    private void genSingleHaoQuestionDetailForThreeLevel(JSONArray result, String taskId, BcQuestionLiuhaoDTO question) {
        JSONObject data = new JSONObject();
        int totalScore = 0;

        List<BcQuestion> childQuestionList = bcQuestionDAO.selectByParentId(question.getqId());
        JSONArray questionList = new JSONArray();
        for (BcQuestion temp : childQuestionList) {
            JSONObject dataTemp = new JSONObject();
            dataTemp.put("qOrder", temp.getqOrder());
            dataTemp.put("qTitle", temp.getqTitle());
            dataTemp.put("qScore", temp.getqScore());
            dataTemp.put("qId", temp.getId());
            dataTemp.put("qCollectType", temp.getqCollectType());
            dataTemp.put("actualScore", getAnswerScore(temp.getId(), temp.getqType(), taskId, 0));
            totalScore += Integer.valueOf(dataTemp.get("actualScore").toString());
            questionList.add(dataTemp);
        }
        data.put("questionList", questionList);

        data.put("qOrder", question.getqOrder());
        data.put("qId", question.getqId());
        data.put("qTitle", question.getqTitle());
        data.put("qScore", question.getqScore());
        data.put("actualScore", totalScore);
        data.put("qCollectType", question.getqCollectType());
        result.add(data);
    }

    @Override
    public BcToDoTaskDetailDTO getToDoBcTaskDetail(String bctaId) {
        BcToDoTaskDetailDTO bcToDoTaskDetailDTO = bcTaskAnswerDAO.selectBcToDoTaskDetailDTO(bctaId);
        List<BcToDoTaskDetailDTO> children = bcTaskAnswerDAO.selectBcToDoTaskDetailDTO_children(bctaId);

        if (null != children && !children.isEmpty()) {

            List<BcTaskAnswerItemListDTO> bcTaskAnswerItemListDTOS = bcTaskAnswerItemDAO.selectBcTaskAnswerItemListByBcToDoTaskDetailDTO(children);

            Map<String, BcToDoTaskDetailDTO> bcToDoTaskDetailDTOMap = new HashMap<>();
            for (BcToDoTaskDetailDTO bc : children)
                bcToDoTaskDetailDTOMap.put(bc.getBctaId(), bc);

            for (BcTaskAnswerItemListDTO bcTaskAnswerItemListDTO : bcTaskAnswerItemListDTOS) {
                BcToDoTaskDetailDTO bc = bcToDoTaskDetailDTOMap.get(bcTaskAnswerItemListDTO.getBctaId());
                if (null == bc)
                    continue;

                List<BcTaskAnswerItemListDTO> bc_answer_item_list = bc.getBcTaskAnswerItemListDTOS();
                if (null == bc_answer_item_list) {
                    bc_answer_item_list = new ArrayList<>();
                    bc.setBcTaskAnswerItemListDTOS(bc_answer_item_list);
                }
                bc_answer_item_list.add(bcTaskAnswerItemListDTO);
            }
        }

        //前端改为调用日志流转信息公共接口
//        bcToDoTaskDetailDTO.setAuditReviewLogList(reviewLogDAO.selectByTypeAndObjId(ProcessTypeEnum.BC_AUDIT.getCode(), bcToDoTaskDetailDTO.getBctaId()));
        bcToDoTaskDetailDTO.setChildren(children);
        return bcToDoTaskDetailDTO;
    }

    public void generBcTaskQuestionListDTO(List<BcTaskQuestionListDTO> bcTaskQuestionListDTOS) {
        if (null == bcTaskQuestionListDTOS || bcTaskQuestionListDTOS.isEmpty())
            return;
        List<BcTaskAnswerItemListDTO> bcTaskAnswerItemListDTOS = bcTaskAnswerItemDAO.selectBcTaskAnswerItemList(bcTaskQuestionListDTOS);
        if (null == bcTaskAnswerItemListDTOS)
            return;

        Map<String, BcTaskQuestionListDTO> bcTaskQuestionListDTOMap = new HashMap<>();
        for (BcTaskQuestionListDTO bcTaskQuestionListDTO : bcTaskQuestionListDTOS)
            bcTaskQuestionListDTOMap.put(bcTaskQuestionListDTO.getqId(), bcTaskQuestionListDTO);

        for (BcTaskAnswerItemListDTO bcTaskAnswerItemListDTO : bcTaskAnswerItemListDTOS) {
            String bctaqId = bcTaskAnswerItemListDTO.getBctaqId();
            String bctaqpId = bcTaskAnswerItemListDTO.getBctaqpId();

            BcTaskQuestionListDTO taskQuestionListDTO = bcTaskQuestionListDTOMap.get(bctaqId);
            if (null != taskQuestionListDTO) {
                addBcTaskAnswerItemListDTOS(taskQuestionListDTO, bcTaskAnswerItemListDTO);
            } else {
                taskQuestionListDTO = bcTaskQuestionListDTOMap.get(bctaqpId);
                if (null != taskQuestionListDTO) {
                    List<BcTaskQuestionListDTO> children = taskQuestionListDTO.getChildren();
                    if (null == children) {
                        children = new ArrayList<>();
                        taskQuestionListDTO.setChildren(children);
                    }

                    boolean find = false;
                    for (BcTaskQuestionListDTO child : children) {
                        if (bctaqId.equals(child.getqId())) {
                            find = true;
                            addBcTaskAnswerItemListDTOS(child, bcTaskAnswerItemListDTO);
                        }
                    }

                    if (!find) {
                        BcTaskQuestionListDTO child = new BcTaskQuestionListDTO();
                        child.setqId(bcTaskAnswerItemListDTO.getBctaqId());
                        child.setqTitle(bcTaskAnswerItemListDTO.getBctaqTitle());
                        addBcTaskAnswerItemListDTOS(child, bcTaskAnswerItemListDTO);
                        children.add(child);
                    }
                }
            }
        }

    }

    public void addBcTaskAnswerItemListDTOS(BcTaskQuestionListDTO taskQuestionListDTO, BcTaskAnswerItemListDTO bcTaskAnswerItemListDTO) {
        List<BcTaskAnswerItemListDTO> bcTaskAnswerItemListDTOS1 = taskQuestionListDTO.getBcTaskAnswerItemListDTOS();
        if (null == bcTaskAnswerItemListDTOS1) {
            bcTaskAnswerItemListDTOS1 = new ArrayList<>();
            taskQuestionListDTO.setBcTaskAnswerItemListDTOS(bcTaskAnswerItemListDTOS1);
        }
        bcTaskAnswerItemListDTOS1.add(bcTaskAnswerItemListDTO);
    }

    private void generBnBranchNewsListDTOList(List<BnBranchNewsListDTO> bnBranchNewsListDTOS) {
        if (null == bnBranchNewsListDTOS || bnBranchNewsListDTOS.isEmpty())
            return;
        Map<String, BnSimpleUserDTO> bnSimpleUserDTOMap = new HashMap<>();
        List<BnSimpleUserDTO> bnSimpleUserDTOS = usersDAO.selectBnSimpleUserDTOByIds(bnBranchNewsListDTOS);
        for (BnSimpleUserDTO bnSimpleUserDTO : bnSimpleUserDTOS)
            bnSimpleUserDTOMap.put(bnSimpleUserDTO.getId(), bnSimpleUserDTO);

        Map<String, List<BnBranchNewsDocsListDTO>> bnBranchNewsDocsListDTOMap = new HashMap<>();
        List<BnBranchNewsDocsListDTO> bnBranchNewsDocsListDTOS = bnBranchNewsDocsDAO.selectBnBranchNewsDocsListDTOByObjIds(bnBranchNewsListDTOS);

        Map<String, List<BnReviewsListDTO>> bnReviewsListDTOMap = new HashMap<>();
        List<BnReviewsListDTO> bnReviewsListDTOS = reviewsDAO.selectBnReviewsListDTOByObjIds(bnBranchNewsListDTOS);

        Map<String, List<BnThuListDTO>> bnThuListDTOMap = new HashMap<>();
        List<BnThuListDTO> bnThuListDTOS = thumbsDAO.selectBnThuListDTOByObjIds(bnBranchNewsListDTOS);

        for (BnBranchNewsDocsListDTO bnBranchNewsDocsListDTO : bnBranchNewsDocsListDTOS) {
            List<BnBranchNewsDocsListDTO> bnBranchNewsDocsListDTOS1 = bnBranchNewsDocsListDTOMap.get(bnBranchNewsDocsListDTO.getBnId());
            if (null == bnBranchNewsDocsListDTOS1) {
                bnBranchNewsDocsListDTOS1 = new ArrayList<>();
                bnBranchNewsDocsListDTOMap.put(bnBranchNewsDocsListDTO.getBnId(), bnBranchNewsDocsListDTOS1);
            }
            bnBranchNewsDocsListDTOS1.add(bnBranchNewsDocsListDTO);
        }
        for (BnReviewsListDTO bnReviewsListDTO : bnReviewsListDTOS) {
            List<BnReviewsListDTO> bnReviewsListDTOS1 = bnReviewsListDTOMap.get(bnReviewsListDTO.getObjId());
            if (null == bnReviewsListDTOS1) {
                bnReviewsListDTOS1 = new ArrayList<>();
                bnReviewsListDTOMap.put(bnReviewsListDTO.getObjId(), bnReviewsListDTOS1);
            }
            bnReviewsListDTOS1.add(bnReviewsListDTO);
        }

        for (BnThuListDTO bnThuListDTO : bnThuListDTOS) {
            List<BnThuListDTO> bnThuListDTOS1 = bnThuListDTOMap.get(bnThuListDTO.getObjId());
            if (null == bnThuListDTOS1) {
                bnThuListDTOS1 = new ArrayList<>();
                bnThuListDTOMap.put(bnThuListDTO.getObjId(), bnThuListDTOS1);
            }
            bnThuListDTOS1.add(bnThuListDTO);
        }

        for (BnBranchNewsListDTO bnBranchNewsListDTO : bnBranchNewsListDTOS) {
            String id = bnBranchNewsListDTO.getId();
            bnBranchNewsListDTO.setBnSimpleUserDTO(bnSimpleUserDTOMap.get(bnBranchNewsListDTO.getUserId()));

            List<BnBranchNewsDocsListDTO> bnBranchNewsDocsListDTOSList = bnBranchNewsDocsListDTOMap.get(id);
            List<BnReviewsListDTO> bnReviewsListDTOList = bnReviewsListDTOMap.get(id);
            List<BnThuListDTO> bnThuListDTOList = bnThuListDTOMap.get(id);

            if (null != bnBranchNewsDocsListDTOSList)
                Collections.sort(bnBranchNewsDocsListDTOSList, new Comparator<BnBranchNewsDocsListDTO>() {
                    @Override
                    public int compare(BnBranchNewsDocsListDTO o1, BnBranchNewsDocsListDTO o2) {
                        return o1.getCreateddate().compareTo(o2.getCreateddate());
                    }
                });
            if (null != bnReviewsListDTOList)
                Collections.sort(bnReviewsListDTOList, new Comparator<BnReviewsListDTO>() {
                    @Override
                    public int compare(BnReviewsListDTO o1, BnReviewsListDTO o2) {
                        return o1.getCreateddate().compareTo(o2.getCreateddate());
                    }
                });
            if (null != bnThuListDTOList)
                Collections.sort(bnThuListDTOList, new Comparator<BnThuListDTO>() {
                    @Override
                    public int compare(BnThuListDTO o1, BnThuListDTO o2) {
                        return o1.getCreateddate().compareTo(o2.getCreateddate());
                    }
                });

            bnBranchNewsListDTO.setBnBranchNewsDocsListDTOS(bnBranchNewsDocsListDTOSList);
            bnBranchNewsListDTO.setBnReviewsListDTOS(bnReviewsListDTOList);
            bnBranchNewsListDTO.setBnThuListDTOS(bnThuListDTOList);
        }
    }

    @Override
    public PageInfo<BcTaskFinScoreListDTO> getBcTaskFinScoreList(Integer page, Integer limit, String bcId, String orgId) {
        PageHelper.startPage(page, limit);
        return new PageInfo<>(generMissedItem(bcTaskDAO.selectBcTaskFinScoreList(bcId, orgId), bcId));
    }

    @Override
    public List<BcTaskFinScoreListDTO> getAllBcTaskFinScoreList(String bcId, String orgId) {

        return generMissedItem(bcTaskDAO.selectBcTaskFinScoreList(bcId, orgId), bcId);
    }

    private List<BcTaskFinScoreListDTO> generMissedItem(List<BcTaskFinScoreListDTO> bcTaskFinScoreListDTOS, String bcId) {
        if (null == bcTaskFinScoreListDTOS || bcTaskFinScoreListDTOS.isEmpty())
            return bcTaskFinScoreListDTOS;

        Map<Integer, String[]> level_string = new HashMap<>();
        String[] aa1 = {"一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};
        level_string.put(Integer.valueOf(1), aa1);
        String[] aa2 = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "10"};
        level_string.put(Integer.valueOf(2), aa2);
        String[] aa3 = {"(1)", "(2)", "(3)", "(4)", "(5)", "(6)", "(7)", "(8)", "(9)", "(10)"};
        level_string.put(Integer.valueOf(3), aa3);
        String[] aa4 = {"<1>", "<2>", "<3>", "<4>", "<5>", "<6>", "<7>", "<8>", "<9>", "<10>"};
        level_string.put(Integer.valueOf(4), aa4);
        String[] aa5 = {"①", "②", "③", "④", "⑤", "⑥", "⑦", "⑧", "⑨", "⑩"};
        level_string.put(Integer.valueOf(5), aa5);

        List<CountDTO> countDTOS_Question = bcQuestionDAO.selectCountDTOBtBcId(bcId);
        List<CountDTO> countDTOS_Question_top = new ArrayList<>();
        Map<String, CountDTO> countDTOS_Question_map = new HashMap<>();

        for (CountDTO countDTO : countDTOS_Question) {
            if (StringUtils.isBlank(countDTO.getObjId()))
                countDTOS_Question_top.add(countDTO);
            countDTOS_Question_map.put(countDTO.getCodeStr(), countDTO);
        }

        for (CountDTO countDTO : countDTOS_Question) {
            CountDTO countDTO_parent = countDTOS_Question_map.get(countDTO.getObjId());
            if (null == countDTO_parent || 1 == countDTO_parent.getSortNum())
                continue;
            List<CountDTO> countDTOList = countDTO_parent.getCountDTOList();
            if (null == countDTOList) {
                countDTOList = new ArrayList<>();
                countDTO_parent.setCountDTOList(countDTOList);
            }
            countDTOList.add(countDTO);
        }

        Map<String, String> provinceMap = new HashMap<>(16);
        List<DictionaryItems> itemList = dictionaryItemsDAO.selectEnabledByDictCode("PROVINCE");
        for (DictionaryItems temp : itemList) {
            provinceMap.put(temp.getCodestr(), temp.getItemtext());
        }

        for (BcTaskFinScoreListDTO bcTaskFinScoreListDTO : bcTaskFinScoreListDTOS) {

            List<CountDTO> countDTOS = bcTaskAnswerDAO.selectCountDTOBtBctId(bcTaskFinScoreListDTO.getId());
            Map<String, CountDTO> countDTOMap_score = new HashMap<>();
            String bcTaskAnswer = null;
            for (CountDTO countDTO : countDTOS) {
                if ("zbxxs".equals(countDTO.getType())) {
                    bcTaskAnswer = countDTO.getAnswer();
                }

                if (1 == countDTO.getSortNum() && countDTO.getCountNum() != 6)
                    continue;

                countDTOMap_score.put(countDTO.getCodeStr(), countDTO);
            }

            Integer finScore = bcTaskAnswerSV.getFinScore(bcTaskFinScoreListDTO.getId(), countDTOS_Question_top, countDTOMap_score);

            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("堡垒指数=（六好支部总分100分*六号支部权重90%）+（支部新鲜事总分100分*支部新鲜事权重5%）+（书记履职总分100分*书记履职权重5%）\r\n")
                    .append("本期堡垒指数计算明细如下：").append(divide_0(finScore, 100)).append("=");

            for (CountDTO countDTO : countDTOS_Question_top)
                stringBuilder.append(divide_0(countDTO.getFinScore(), 100)).append("+");
            //移除最后一个加号
            stringBuilder.setLength(stringBuilder.length() - 1);

            stringBuilder.append("\r\n")
                    .append("本期扣分项如下：");

            doGenerMissedItem(countDTOS_Question_top, stringBuilder, Integer.valueOf(1), "", level_string, bcTaskAnswer);

            String missedItemStr = stringBuilder.toString();
            if (missedItemStr.endsWith("本期扣分项如下：")) {
                missedItemStr = missedItemStr.substring(0, missedItemStr.indexOf("本期扣分项如下："));
            }
            bcTaskFinScoreListDTO.setMissedItem(missedItemStr);
            if(StringUtils.isNotBlank(bcTaskFinScoreListDTO.getProvince()) && provinceMap.containsKey(bcTaskFinScoreListDTO.getProvince())){
                bcTaskFinScoreListDTO.setProvinceStr(provinceMap.get(bcTaskFinScoreListDTO.getProvince()));
            }
        }
        return bcTaskFinScoreListDTOS;
    }

    private void doGenerMissedItem(List<CountDTO> countDTOS_question_top, StringBuilder stringBuilder, Integer level, String string, Map<Integer, String[]> level_string, String zbxxsDesc) {
        int hiddenNum = 0;
        for (int i = 0; i < countDTOS_question_top.size(); i++) {
            CountDTO countDTO = countDTOS_question_top.get(i);
            if (countDTO.getCountNum() * countDTO.getAllNum() / 100 == countDTO.getFinScore()) {
                hiddenNum++;
                continue;
            }

            stringBuilder.append("\r\n")
                    .append(string)
                    .append(level_string.get(countDTO.getqLevel())[i - hiddenNum])
                    .append("、")
                    .append(countDTO.getTitle());

            if (countDTO.getAllNum() < 100)
                stringBuilder.append(",满分").append(divide_0(countDTO.getCountNum(), 100)).append("分")
                        .append(",初始得分").append(divide_0(countDTO.getFinScore(), countDTO.getAllNum())).append("分")
                        .append(",最终得分").append(divide_0(countDTO.getFinScore(), 100)).append("分");
            else
                stringBuilder.append(",共").append(divide_0(countDTO.getCountNum(), 100) + "分");

            stringBuilder.append(",扣").append(divide_0(countDTO.getCountNum() - countDTO.getFinScore(), 100)).append("分");

            if ("zbxxs".equals(countDTO.getType()) && StringUtils.isNotBlank(zbxxsDesc)) {
                // 本期内新鲜事发布占比为.00%，其中发布新鲜事党员数量0人，党员总数3人
                int firstIndex = zbxxsDesc.indexOf("，");
                int secondIndex = zbxxsDesc.lastIndexOf("，");
                Pattern p = Pattern.compile("[^0-9]");
                Matcher m1 = p.matcher(zbxxsDesc.substring(firstIndex, secondIndex));
                int userNum = Integer.valueOf(m1.replaceAll(""));
                Matcher m2 = p.matcher(zbxxsDesc.substring(secondIndex));
                int totalNum = Integer.valueOf(m2.replaceAll(""));
                stringBuilder.append("。\r\n")
                        .append("本支部共" + totalNum + "名党员，共" + userNum + "人发布新鲜事，" + (totalNum - userNum) + "人未发布新鲜事，得")
                        .append(divide_0(countDTO.getFinScore(), 100) + "分，")
                        .append("扣" + divide_0(countDTO.getCountNum() - countDTO.getFinScore(), 100) + "分");
            }

            if (null != countDTO.getCountDTOList())
                doGenerMissedItem(countDTO.getCountDTOList(), stringBuilder, ++level, " " + string, level_string, zbxxsDesc);
        }
    }

}