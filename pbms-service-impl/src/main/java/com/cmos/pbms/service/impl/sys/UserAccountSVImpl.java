package com.cmos.pbms.service.impl.sys;

import com.alibaba.dubbo.config.annotation.Service;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.sys.UserAccount;
import com.cmos.pbms.dao.sys.UserAccountDao;
import com.cmos.pbms.iservice.sys.IUserAccountSV;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 用户登录账号信息服务实现类
 * 使用了Dubbo服务注解,自动注册为Dubbo服务
 *
 * <AUTHOR>
 */
@Service(group = "pbms", retries = -1)
public class UserAccountSVImpl implements IUserAccountSV {

    private static final Logger logger = LoggerFactory.getLogger(UserAccountSVImpl.class);

    @Autowired
    private UserAccountDao userAccountDao;

    @Override
    public boolean addAccount(UserAccount userAccount) {
        UserAccount oldUserAccount = userAccountDao.selectSpecificAcct(userAccount.getUserid(),
                userAccount.getAccountvalue(),
                userAccount.getAccounttype());
        if (null == oldUserAccount) {
            return userAccountDao.insert(userAccount) == 1;
        } else { //如果存在同个用户同一个凭据，直接更新该凭据
            userAccount.setId(oldUserAccount.getId());
            return userAccountDao.updateByPrimaryKey(userAccount) == 1;
        }
    }

    @Override
    public boolean addAccountByUseridAndAccounttype(UserAccount userAccount) {
        UserAccount oldUserAccount = userAccountDao.selectSpecificAcct(userAccount.getUserid(),
                null,
                userAccount.getAccounttype());
        if (null == oldUserAccount) {
            return userAccountDao.insert(userAccount) == 1;
        } else { //如果存在同个用户同一个凭据，直接更新该凭据
            oldUserAccount.setAccountvalue(userAccount.getAccountvalue());
            oldUserAccount.setAccount(userAccount.getAccount());
            oldUserAccount.setModifieddate(userAccount.getModifieddate());
            oldUserAccount.setModifiedby(userAccount.getModifiedby());
            oldUserAccount.setIsenable(userAccount.getIsenable());
            oldUserAccount.setExptime(userAccount.getExptime());
            oldUserAccount.setExtFld1(userAccount.getExtFld1());
            oldUserAccount.setExtFld2(userAccount.getExtFld2());
            oldUserAccount.setExtFld3(userAccount.getExtFld3());
            return userAccountDao.updateByPrimaryKey(oldUserAccount) == 1;
        }
    }

    @Override
    public boolean updateAccount(UserAccount userAccount) {
        return userAccountDao.updateByPrimaryKey(userAccount) == 1;
    }

    @Override
    public boolean updateAccountSelective(UserAccount userAccount) {
        return userAccountDao.updateByPrimaryKeySelective(userAccount) == 1;
    }

    @Override
    public UserAccount getAccountByUserIdAndMin(String userId, String mId) {
        return userAccountDao.selectSpecificAcct(userId, mId, 30);
    }

    @Override
    public UserAccount getAccountByUserIdAndAccount(String userId, Integer accounttype, String accountvalue) {
        return userAccountDao.selectSpecificAcct(userId, accountvalue, accounttype);
    }

    @Override
    public List<UserAccount> getAccountByUserAccount(UserAccount userAccount) {
        return userAccountDao.select(userAccount);
    }

    @Override
    public boolean insertAccount(UserAccount userAccount) {
        return userAccountDao.insert(userAccount) == 1;
    }

}
