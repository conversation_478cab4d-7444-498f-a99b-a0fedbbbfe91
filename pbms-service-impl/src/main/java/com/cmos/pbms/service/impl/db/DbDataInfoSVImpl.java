package com.cmos.pbms.service.impl.db;

import com.alibaba.dubbo.config.annotation.Service;
import com.cmos.common.exception.GeneralException;
import com.cmos.common.exception.SystemFailureException;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.pbms.beans.common.Attachments;
import com.cmos.pbms.beans.db.DbDataInfo;
import com.cmos.pbms.beans.db.DbDataShareInfo;
import com.cmos.pbms.beans.dto.DbDataInfoAttDTO;
import com.cmos.pbms.beans.dto.DbDataInfoListDTO;
import com.cmos.pbms.beans.dto.DbInfoListDTO;
import com.cmos.pbms.beans.dto.DbInfoToExportDTO;
import com.cmos.pbms.beans.pm.Organization;
import com.cmos.pbms.dao.common.AttachmentsDAO;
import com.cmos.pbms.dao.db.DbDataInfoDAO;
import com.cmos.pbms.dao.db.DbDataShareInfoDAO;
import com.cmos.pbms.dao.pm.OrganizationDAO;
import com.cmos.pbms.iservice.db.IDbDataInfoSV;
import com.cmos.pbms.utils.UIDUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service(group = "pbms", retries = -1)
public class DbDataInfoSVImpl implements IDbDataInfoSV {

    private static final Logger logger = LoggerFactory.getLogger(DbDataInfoSVImpl.class);

    @Autowired
    private AttachmentsDAO attachmentsDAO;
    @Autowired
    private OrganizationDAO organizationDAO;
    @Autowired
    private DbDataInfoDAO dbDataInfoDAO;
    @Autowired
    private DbDataShareInfoDAO dbDataShareInfoDAO;

    @Override
    public DbDataInfo getByPrimaryKey(String dId) {
        return dbDataInfoDAO.selectByPrimaryKey(dId);
    }

    @Override
    public DbDataInfo uploadFile(String dbId, String dId, String parentId, String fileName, Integer fileSize, String nUrl, String ext, Integer isAllowShare, Integer isAllowDownload, Integer sType, String userId, String orgId, Date date, String xmlUrl, String pdfUrl) {

        Attachments attachments = new Attachments();
        attachments.setId(dId);
        attachments.setAttname(fileName);
        attachments.setObjid(dId);
        attachments.setAtttype("dataBase");
        attachments.setUrl(nUrl);
        attachments.setCreatedby(userId);
        attachments.setCreateddate(date);
        attachments.setFilesize(fileSize);
        attachments.setIsdeleted(Integer.valueOf(0));
        attachments.setExtFld1(pdfUrl);
        attachments.setExtFld3(xmlUrl);

        DbDataInfo dbDataInfo = new DbDataInfo();
        dbDataInfo.setId(dId);
        dbDataInfo.setDbId(dbId);
        dbDataInfo.setUserId(userId);
        dbDataInfo.setOrgId(orgId);
        dbDataInfo.setParentId(parentId);
        dbDataInfo.setdName(fileName);
        dbDataInfo.setdSuffix(ext);
        dbDataInfo.setdSize(fileSize);
        dbDataInfo.setDownloadCount(Integer.valueOf(0));
        dbDataInfo.setIsDirectory(Integer.valueOf(0));
        dbDataInfo.setIsAllowShare(isAllowShare);
        dbDataInfo.setIsAllowDownload(isAllowDownload);
        dbDataInfo.setdUrl(nUrl);
        dbDataInfo.setsType(null == sType ? Integer.valueOf(0) : sType);
        dbDataInfo.setIsdeleted(Integer.valueOf(0));
        dbDataInfo.setPdfUrl(pdfUrl);
        dbDataInfo.setXmlUrl(xmlUrl);
        dbDataInfo.setCreatedby(userId);
        dbDataInfo.setCreateddate(date);

        attachmentsDAO.insertSelective(attachments);
        dbDataInfoDAO.insertSelective(dbDataInfo);

        return dbDataInfo;
    }

    @Override
    public DbDataInfo createDirectory(String dbId, String parentId, String directoryName, Integer isAllowShare, Integer isAllowDownload, Integer sType, String userId, String orgId, Date date) {

        DbDataInfo dbDataInfo = new DbDataInfo();

        dbDataInfo.setId(UIDUtil.getUID());
        dbDataInfo.setDbId(dbId);
        dbDataInfo.setUserId(userId);
        dbDataInfo.setOrgId(orgId);
        dbDataInfo.setParentId(parentId);
        dbDataInfo.setdName(directoryName);
        dbDataInfo.setDownloadCount(Integer.valueOf(0));
        dbDataInfo.setIsDirectory(Integer.valueOf(1));
        dbDataInfo.setIsAllowShare(isAllowShare);
        dbDataInfo.setIsAllowDownload(isAllowDownload);
        dbDataInfo.setsType(null == sType ? Integer.valueOf(0) : sType);
        dbDataInfo.setIsdeleted(Integer.valueOf(0));
        dbDataInfo.setCreatedby(userId);
        dbDataInfo.setCreateddate(date);

        dbDataInfoDAO.insertSelective(dbDataInfo);

        return dbDataInfo;
    }

    @Override
    public Integer deleteBydId(String dId, String userId, Date date) throws SystemFailureException {
        DbDataInfo dbDataInfo = dbDataInfoDAO.selectByPrimaryKey(dId);
        if (null == dbDataInfo)
            throw new SystemFailureException("资料不存在");

        dbDataInfo.setIsdeleted(Integer.valueOf(1));
        dbDataInfo.setModifiedby(userId);
        dbDataInfo.setModifieddate(date);

        return dbDataInfoDAO.updateByPrimaryKey(dbDataInfo);
    }

    @Override
    public PageInfo<DbDataInfoListDTO> getFileList(String dsId, String dbId, String parentId, String fileName, String userName, String orgId, String userId, Integer page, Integer limit) throws SystemFailureException {
        PageHelper.startPage(page, limit);
        if (StringUtils.isNoneBlank(dsId)) {
            DbDataShareInfo dbDataShareInfo = dbDataShareInfoDAO.selectByPrimaryKey(dsId);
            if (null != dbDataShareInfo) {
                if (0 == dbDataShareInfo.getsType() && !userId.equals(dbDataShareInfo.getUserId()))
                    throw new SystemFailureException("您无访问权限！");

                if (2 == dbDataShareInfo.getsType() && 1 == dbDataShareInfo.getsReadRoleType())
                    return new PageInfo<>(dbDataInfoDAO.selectDbDataInfoListDTOByDbIdAndParentIdAndFileName(dbId, parentId, fileName, userName, orgId, null));

                return new PageInfo<>(dbDataInfoDAO.selectDbDataInfoListDTOByDbIdAndParentIdAndFileName(dbId, parentId, fileName, userName, orgId, userId));
            }
        }

        return new PageInfo<>(dbDataInfoDAO.selectDbDataInfoListDTOByDbIdAndParentIdAndFileName(dbId, parentId, fileName, userName, orgId, userId));
    }

    @Override
    public List<DbInfoListDTO> getMyDbInfos(String orgId) {
        List<DbInfoListDTO> dbInfoListDTOS = new ArrayList<>();
        Organization organization = organizationDAO.selectByPrimaryKey(orgId);

        while (null != organization) {
            DbInfoListDTO dbInfoListDTO = new DbInfoListDTO();
            dbInfoListDTO.setId(organization.getId());
            dbInfoListDTO.setDbName(organization.getOrgName());
            dbInfoListDTO.setDbType(organization.getOrgtype());

            dbInfoListDTOS.add(dbInfoListDTO);
            if (StringUtils.isBlank(organization.getParentid()))
                break;

            organization = organizationDAO.selectByPrimaryKey(organization.getParentid());
        }

        return dbInfoListDTOS;
    }

    @Override
    public DbDataInfoAttDTO getFileInfo(String dId, String userId) throws GeneralException {
        DbDataInfo dbDataInfo = dbDataInfoDAO.selectByPrimaryKey(dId);

        DbDataInfoAttDTO result = new DbDataInfoAttDTO();

        if (null == dbDataInfo) {
            Organization organization = organizationDAO.selectByPrimaryKey(dId);
            if (null == organization)
                throw new SystemFailureException("资料不存在");

            result.setIsDirectory(Integer.valueOf(1));
            result.setdName(organization.getOrgName());
            result.setCountDTOS(dbDataInfoDAO.countDbDataIsDirectoryInfoByParentId(organization.getId(), userId));
        } else {
            Integer isDirectory = dbDataInfo.getIsDirectory();
            result.setIsDirectory(isDirectory);
            result.setdName(dbDataInfo.getdName());
            result.setsType(dbDataInfo.getsType());
            result.setCreateddate(dbDataInfo.getCreateddate());
            result.setdSuffix(dbDataInfo.getdSuffix());
            result.setdSize(dbDataInfo.getdSize());
            result.setDownloadCount(dbDataInfo.getDownloadCount());

            result.setCreateUserInfo(dbDataInfoDAO.selectCreateUserInfoByUserIdAndOrgId(dbDataInfo.getCreatedby(), dbDataInfo.getOrgId()));
            result.setShareUserInfo(dbDataShareInfoDAO.selectShareUserInfoBydId(dbDataInfo.getId()));

            if (1 == isDirectory)
                result.setCountDTOS(dbDataInfoDAO.countDbDataIsDirectoryInfoByParentId(dbDataInfo.getId(), userId));

            result.setDbDataShareInfoListDTOS(dbDataShareInfoDAO.selectDbDataShareInfoListBydId(dbDataInfo.getId()));
        }
        result.setdId(dId);
        result.setDbDataShareInfo(dbDataShareInfoDAO.selectByPrimaryKey(result.getdId()));
        return result;
    }

    @Override
    public List<DbDataInfoListDTO> getFilePaths(String dId) {
        List<DbDataInfoListDTO> result = new ArrayList<>();
        DbDataInfoListDTO dbDataInfoListDTO = dbDataInfoDAO.selectDbDataInfoListDTOByPrimaryKey(dId);
        String parentId = dbDataInfoListDTO.getParentId();
        if (StringUtils.isBlank(parentId))
            return result;

        dbDataInfoListDTO = dbDataInfoDAO.selectDbDataInfoListDTOByPrimaryKey(parentId);

        while (null != dbDataInfoListDTO) {
            result.add(dbDataInfoListDTO);
            parentId = dbDataInfoListDTO.getParentId();
            if (StringUtils.isBlank(parentId))
                break;

            dbDataInfoListDTO = dbDataInfoDAO.selectDbDataInfoListDTOByPrimaryKey(parentId);
        }
        dbDataInfoListDTO = dbDataInfoDAO.selectDbDataInfoListDTOByDbId(parentId);
        if (null != dbDataInfoListDTO)
            result.add(dbDataInfoListDTO);
        Collections.reverse(result);
        return result;
    }

    @Override
    public Integer changeFileAtt(String dId, String dName, Integer isAllowShare, Integer isAllowDownload, String userId, Date date) throws SystemFailureException {

        DbDataInfo dbDataInfo = dbDataInfoDAO.selectByPrimaryKey(dId);
        if (null == dbDataInfo)
            throw new SystemFailureException("资料不存在");

        dbDataInfo.setdName(dName);
        dbDataInfo.setIsAllowShare(isAllowShare);
        dbDataInfo.setIsAllowDownload(isAllowDownload);
        dbDataInfo.setModifiedby(userId);
        dbDataInfo.setModifieddate(date);

        return dbDataInfoDAO.updateByPrimaryKeySelective(dbDataInfo);
    }

    @Override
    public Integer addDownloadCount(String dId) {
        return dbDataInfoDAO.addDownloadCountByPrimaryKey(dId);
    }

    @Override
    public PageInfo<DbDataInfoListDTO> getInviteFileList(String parentId, String dsId, String userId, Integer page, Integer limit) throws SystemFailureException {

        if (StringUtils.isBlank(parentId) && StringUtils.isBlank(dsId)) {
            PageHelper.startPage(page, limit);
            return new PageInfo<>(dbDataShareInfoDAO.selectDbDataInfoListDTOByShareUserId(userId));
        }

        DbDataShareInfo dbDataShareInfo = dbDataShareInfoDAO.selectByPrimaryKey(dsId);
        if (null == dbDataShareInfo)
            throw new SystemFailureException("资料分享记录不存在");

        PageHelper.startPage(page, limit);
        if (1 == dbDataShareInfo.getsReadRoleType() || userId.equals(dbDataShareInfo.getUserId()))
            return new PageInfo<>(dbDataShareInfoDAO.selectDbDataDetailListDTOByShareUserId(parentId, null));
        return new PageInfo<>(dbDataShareInfoDAO.selectDbDataDetailListDTOByShareUserId(parentId, userId));
    }

    @Override
    public List<DbInfoToExportDTO> getFileListSummaryToExport(String userId, String dbId, String parentId, String fileName, String userName, String orgId) {

        return dbDataInfoDAO.selectFileListSummaryToExport(dbId, parentId, fileName, userName, orgId, userId);
    }

    @Override
    public List<DbInfoToExportDTO> getFileListDetailToExport(String userId, String dbId, String parentId, String fileName, String userName, String orgId) {

        return dbDataInfoDAO.selectFileListDetailToExport(dbId, parentId, fileName, userName, orgId, userId);
    }
}
