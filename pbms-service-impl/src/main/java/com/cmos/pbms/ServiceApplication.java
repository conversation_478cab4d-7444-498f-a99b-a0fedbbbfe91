package com.cmos.pbms;

import com.cmos.common.annotation.EnableDataSource;
import com.cmos.common.annotation.EnableTransaction;
import com.cmos.common.spring.ApplicationStarter;
import com.cmos.common.web.config.DefaultWebMvcConfig;
import com.cmos.core.logger.Logger;
import com.cmos.core.logger.LoggerFactory;
import com.cmos.core.logger.interceptor.EnableLog4xComponent;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR>
 */
@Configuration
@SpringBootApplication
@EnableAspectJAutoProxy
@EnableDataSource
@EnableTransaction
@EnableLog4xComponent
@EnableAsync
@ComponentScan(basePackages = {"com.cmos.pbms.service", "com.cmos.cache"})
@Import(DefaultWebMvcConfig.class)
public class ServiceApplication {

    private static final Logger logger = LoggerFactory.getLogger(ServiceApplication.class);

    public static void main(String[] args) throws Exception {
        try {
            System.setProperty("HOSTNAME", InetAddress.getLocalHost().getHostName());
        } catch (UnknownHostException e) {
            logger.error("设置Log4j日志目录失败，使用默认路径");
        }

        ApplicationStarter.startWebApplication(ServiceApplication.class, args);
    }

}
