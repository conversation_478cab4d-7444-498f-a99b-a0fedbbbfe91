package com.cmos.pbms.service.impl.ce;

import com.alibaba.fastjson.JSON;
import com.cmos.common.test.UnitTestBase;
import com.cmos.pbms.beans.ce.Activities;
import com.cmos.pbms.beans.ce.ActivityForAPP;
import com.cmos.pbms.beans.common.WorkTask;
import com.cmos.pbms.beans.enums.WorkTaskTypeEnum;
import com.cmos.pbms.beans.pm.Organization;
import com.cmos.pbms.dao.ce.ActivitiesDAO;
import com.cmos.pbms.dao.common.WorkTaskDAO;
import com.cmos.pbms.dao.pm.OrganizationDAO;
import com.cmos.pbms.iservice.ce.IActivitiesSV;
import com.github.pagehelper.PageInfo;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Transactional
@Rollback
public class ActivitiesSVImplTest extends UnitTestBase {
    @Autowired
    private IActivitiesSV activitiesSV;

    @Autowired
    private ActivitiesDAO activitiesDAO;

    @Autowired
    private OrganizationDAO organizationDAO;

    @Autowired
    private WorkTaskDAO workTaskDAO;

    private Activities activities = new Activities();

    @Before
    public void setUp() throws Exception {
        super.setUp();
        activities.setId("TEST" + UUID.randomUUID().toString());
        activities.setActname("用于单元测试");
        activities.setActors("");
        activities.setActtype(1);
        activities.setActstatus(0);
        activities.setIsevaluate((byte) 0);
        activities.setIsdeleted(0);
    }

    @After
    public void tearDown() throws Exception {
        super.tearDown();
    }

    @Test
    public void testDeleteByPrimaryKey() throws Exception {
        //添加测试数据
        addTestDate(activities);
        //断言
        Assert.assertTrue(activitiesSV.deleteByPrimaryKey(activities.getId(), "") > 0);
        //清除测试数据
        removeDate();
    }

    @Test
    public void testInsert() throws Exception {
        Assert.assertTrue(activitiesSV.insert(activities) > 0);
        //清除测试数据
        removeDate();
    }

    @Test
    public void testInsertSelective() throws Exception {
        Assert.assertTrue(activitiesSV.insertSelective(activities, null) > 0);
        //清除测试数据
        removeDate();
    }

    @Test
    public void testGetByPrimaryKey() throws Exception {
        //添加测试数据
        addTestDate(activities);
        //断言
        Map<String, Object> activities1 = activitiesSV.getByPrimaryKey(activities.getId());
        Assert.assertEquals(activities.getId(), activities1.get("id"));
        //清除测试数据
        removeDate();
    }

    @Test
    public void testGetByPageWithParam() throws Exception {
        //添加测试数据
        addTestDate(activities);
        Map<String, Object> params = new HashMap<>(16);
        params.put("actname", activities.getActname());
        PageInfo<Activities> page = activitiesSV.getByPageWithParam(1, 10, params);
        boolean re = false;
        for (Activities item : page.getList()) {
            if (item.getId().equals(activities.getId())) {
                re = true;
                break;
            }
        }
        //断言
        Assert.assertTrue(re);
        //清除测试数据
        removeDate();
    }

    @Test
    public void testGetAllForAPP() throws Exception {
        //添加测试数据
        String nid = "TEST" + UUID.randomUUID().toString();
        Organization organization = new Organization();
        organization.setId(nid);
        organization.setCodestr("test-codestr");
        organization.setIsdeleted(0);
        organizationDAO.insertSelective(organization);
        Activities activities1 = new Activities();
        activities1.setId(nid);
        activities1.setActname("用于单元测试activities");
        activities1.setActstatus(40);
        activities1.setSponsor(organization.getId());
        activities1.setIsdeleted(0);
        activitiesDAO.insertSelective(activities1);
        WorkTask workTask = new WorkTask();
        workTask.setId(nid);
        workTask.setTaskname("用于单元测试workTask");
        workTask.setTaskstatus(0);
        workTask.setObjid(activities1.getId());
        workTask.setUserid("test-userid");
        workTask.setTasktype(WorkTaskTypeEnum.COMMUNITY.getCode());
        workTask.setIsdeleted(0);
        workTaskDAO.insertSelective(workTask);
        PageInfo<ActivityForAPP> page = activitiesSV.getAllForAPP(1, 10, activities.getActname(), organization.getId(), workTask.getUserid());
        boolean re = false;
        for (ActivityForAPP item : page.getList()) {
            if (item.getId().equals(activities1.getId())) {
                re = true;
                break;
            }
        }
        //断言
        Assert.assertTrue(re);
        //清除测试数据
        activitiesDAO.deleteRealByPrimaryKey(activities1.getId());
        organizationDAO.deleteRealByPrimaryKey(organization.getId());
        workTaskDAO.deleteByPrimaryKey(workTask.getId());
    }

    @Test
    public void testGetByIdForAPP() throws Exception {
        //添加测试数据
        addTestDate(activities);
        //断言
        Map<String, Object> activities1 = activitiesSV.getByIdForAPP(activities.getId(), "");
        Assert.assertEquals(activities.getId(), activities1.get("id"));
        //清除测试数据
        removeDate();
    }

    @Test
    public void testUpdateByPrimaryKeySelective() throws Exception {
        //添加测试数据
        addTestDate(activities);
        Activities activities1 = new Activities();
        activities1.setId(activities.getId());
        activities1.setActname("用于单元测试update");
        activities1.setActors("");
        //断言
        Assert.assertTrue(activitiesSV.updateByPrimaryKeySelective(activities1, null) > 0);
        //清除测试数据
        removeDate();
    }

    @Test
    public void testUpdateByPrimaryKey() throws Exception {
        //添加测试数据
        addTestDate(activities);
        Activities activities1 = new Activities();
        activities1.setId(activities.getId());
        activities1.setActname("用于单元测试update");
        //断言
        Assert.assertTrue(activitiesSV.updateByPrimaryKey(activities1) > 0);
        //清除测试数据
        removeDate();
    }

    @Test
    public void testChangeStatus() throws Exception {
        String str = activitiesSV.changeStatus("sn111");
        Map<String, Object> re = JSON.parseObject(str);
        Assert.assertEquals(re.get("rtnCode").toString(), "0");
    }

    //添加一个测试数据
    private void addTestDate(Activities activities) {
        activitiesDAO.insertSelective(activities);
    }

    //清除测试数据
    private void removeDate() {
        activitiesDAO.deleteRealByPrimaryKey(activities.getId());
    }

}