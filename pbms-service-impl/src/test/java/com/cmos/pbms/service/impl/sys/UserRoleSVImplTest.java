package com.cmos.pbms.service.impl.sys;

import com.cmos.common.test.UnitTestBase;
import com.cmos.pbms.beans.sys.UserRole;
import com.cmos.pbms.dao.sys.UserRoleDAO;
import com.cmos.pbms.iservice.sys.IUserRoleSV;
import com.github.pagehelper.PageInfo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Transactional
@Rollback
public class UserRoleSVImplTest extends UnitTestBase {

    @Autowired
    private IUserRoleSV userRoleSV;

    @Autowired
    private UserRoleDAO userRoleDAO;

    private UserRole userRole = new UserRole();

    @Before
    public void setUp() throws Exception {
        super.setUp();
        userRole.setId("TEST" + UUID.randomUUID().toString());
        userRole.setRoleid("TEST" + UUID.randomUUID().toString());
        userRole.setOrgid("TEST" + UUID.randomUUID().toString());
        userRole.setIsdeleted(0);
    }

    @Test
    public void testInsertBatch() {
        //添加测试数据
        List<UserRole> list = new ArrayList<>(16);
        list.add(userRole);
        //断言
        Assert.assertTrue(userRoleSV.insertBatch(list) > 0);
        //查询是否插入的一样
        UserRole userRole1 = userRoleDAO.selectByPrimaryKey(userRole.getId());
        Assert.assertEquals(userRole1.getId(), userRole.getId());
        //清除数据
        removeDate();
    }

    @Test
    public void testDeleteBatch() {
        //添加测试数据
        addTestDate(userRole);
        List<String> list = new ArrayList<>(16);
        list.add(userRole.getId());
        //断言
        Assert.assertTrue(userRoleSV.deleteBatch(list) > 0);
        //清除数据
        removeDate();
    }

    @Test
    public void testDeleteByRoleAndOrg() {
        //添加测试数据
        addTestDate(userRole);
        //断言
        Assert.assertTrue(userRoleSV.deleteByRoleAndOrg(userRole.getRoleid(), userRole.getOrgid()) > 0);
        //清除数据
        removeDate();
    }

    @Test
    public void testGetByPageWithParam() {
        //添加测试数据
        addTestDate(userRole);
        Map<String, Object> params = new HashMap<>(16);
        params.put("roleid", userRole.getRoleid());
        PageInfo<UserRole> page = userRoleSV.getByPageWithParam(1, 1, params);
        boolean re = false;
        for (UserRole item : page.getList()) {
            if (item.getId().equals(userRole.getId())) {
                re = true;
                break;
            }
        }
        //断言
        Assert.assertTrue(re);
        //清除数据
        removeDate();
    }

    //添加一个测试数据
    private void addTestDate(UserRole userRole) {
        userRoleDAO.insertSelective(userRole);
    }

    //清除测试数据
    private void removeDate() {
        userRoleDAO.deleteByPrimaryKey(userRole.getId());
    }

}
