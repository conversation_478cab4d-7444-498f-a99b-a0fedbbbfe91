package com.cmos.pbms.service.impl.rn;

import com.cmos.common.test.UnitTestBase;
import com.cmos.pbms.beans.dto.ComboSelectBean;
import com.cmos.pbms.beans.pm.Organization;
import com.cmos.pbms.beans.rn.Book;
import com.cmos.pbms.beans.sys.Users;
import com.cmos.pbms.dao.pm.OrganizationDAO;
import com.cmos.pbms.dao.rn.BookDAO;
import com.cmos.pbms.dao.sys.UsersDAO;
import com.cmos.pbms.iservice.rn.IBookSV;
import com.github.pagehelper.PageInfo;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * ${DESCRIPTION}
 *
 * <AUTHOR>
 * created on 2018-04-11 下午3:01
 */
@Transactional
@Rollback
public class BookSVImplTest extends UnitTestBase {

    private Organization bookOrg = new Organization();

    private Users bookUser = new Users();

    private Book book = new Book();

    @Autowired
    private IBookSV bookSV;

    @Autowired
    private BookDAO bookDAO;

    @Autowired
    private OrganizationDAO orgDAO;

    @Autowired
    private UsersDAO userDAO;

    @Before
    public void setUp() throws Exception {
        super.setUp();

        Calendar calendar = Calendar.getInstance();
        Date current = calendar.getTime();

        // 插入单元测试党组数据
        String id = UUID.randomUUID().toString().replace("-", "");
        bookOrg.setId(id + "TEST");
        bookOrg.setProvince("998");
        bookOrg.setOrgfname("单元测试数据记录");
        bookOrg.setOrgsname("单元测试数据记录");
        bookOrg.setCodestr(id + "TEST");
        bookOrg.setOrdernum(999);
        bookOrg.setLevelnum(1);
        bookOrg.setOrgtype(1);
        bookOrg.setOrgstatus(1);

        orgDAO.insert(bookOrg);

        // 插入单元测试用户数据
        id = UUID.randomUUID().toString().replace("-", "");
        bookUser.setId(id + "TEST");
        bookUser.setUsername("单元测试用户");
        bookUser.setOrgid(bookOrg.getId());

        userDAO.insert(bookUser);

        // 插入书籍信息
        id = UUID.randomUUID().toString().replace("-", "");
        book.setId(id + "TEST");
        book.setOrgid(bookOrg.getId());
        book.setBookname("mock书籍");
        book.setAuthor("mock作者");
        book.setIsbn("12345678");
        book.setPublisher("mockPublisher");
        book.setPubtime(current);
        book.setCover("mock-cover");
        book.setSummary("mock-summary");
        book.setEbook("mock-ebook-url");
        book.setContents("mock-contents");
        book.setIsstop(0);
        book.setIsdeleted(0);
        book.setBooktype("SOCIALSCIENCE");
        book.setPubnum("1");
        book.setCreatedby(bookUser.getId());
        book.setCreateddate(current);
        book.setModifiedby(bookUser.getId());
        book.setModifieddate(current);
    }

    @After
    public void tearDown() throws Exception {
        super.tearDown();
    }

    @Test
    public void testDeleteByPrimaryKey() {

        Calendar calendar = Calendar.getInstance();
        Date current = calendar.getTime();

        bookDAO.insert(book);

        int result = bookSV.deleteByParams(book.getId(), bookUser.getId(), current);

        Book dbBook = bookDAO.selectByPrimaryKey(book.getId());

        assertEquals(1, result);
        assertNotNull(dbBook);

        int isDelete = dbBook.getIsdeleted();
        assertEquals(1, isDelete);
    }

    @Test
    public void testDeleteRealByPrimaryKey() {

        bookDAO.insert(book);

        int result = bookSV.deleteRealByPrimaryKey(book.getId());

        Book dbBook = bookDAO.selectByPrimaryKey(book.getId());

        assertEquals(1, result);
        assertNull(dbBook);
    }

    @Test
    public void testInsert() {

        int result = bookSV.insert(book);

        Book dbBook = bookDAO.selectByPrimaryKey(book.getId());

        assertEquals(1, result);
        assertNotNull(dbBook);
    }

    @Test
    public void testInsertSelective() {
        int result = bookSV.insertSelective(book);

        Book dbBook = bookDAO.selectByPrimaryKey(book.getId());

        assertEquals(1, result);
        assertNotNull(dbBook);
    }

    @Test
    public void testGetByPrimaryKey() {
        bookDAO.insert(book);

        Book dbBook = bookSV.getByPrimaryKey(book.getId());

        assertNotNull(dbBook);

        dbBook = bookSV.getByPrimaryKey("");

        assertNull(dbBook);
    }

    @Test
    public void testListByBookname() {
        bookDAO.insert(book);

        List<ComboSelectBean> comboSelectBeans = bookSV.listByBookname(book.getBookname());

        assertFalse(comboSelectBeans.isEmpty());
        assertEquals(1, comboSelectBeans.size());
    }

    @Test
    public void testUpdateByPrimaryKeySelective() {

        String id = UUID.randomUUID().toString().replace("-", "");
        Book book = new Book();
        book.setId(id + "TEST");
        book.setBookname("mock-test");
        book.setAuthor("mockPublisher");

        bookDAO.insert(book);

        Book dbBookBefore = bookDAO.selectByPrimaryKey(book.getId());

        book.setBookname("更新之后的书名");
        book.setAuthor(null);

        bookSV.updateByPrimaryKeySelective(book);

        Book dbBookAfter = bookDAO.selectByPrimaryKey(book.getId());

        assertNotNull(dbBookAfter);
        assertNotNull(dbBookAfter.getAuthor());
        assertNotSame(dbBookBefore.getAuthor(), dbBookAfter.getAuthor());
    }

    @Test
    public void testUpdateByPrimaryKey() {
        String id = UUID.randomUUID().toString().replace("-", "");
        Book book = new Book();
        book.setId(id + "TEST");
        book.setBookname("mock-test");
        book.setAuthor("mockPublisher");

        bookDAO.insert(book);

        Book dbBookBefore = bookDAO.selectByPrimaryKey(book.getId());

        book.setBookname("更新之后的书名");
        book.setAuthor(null);

        bookSV.updateByPrimaryKey(book);

        Book dbBookAfter = bookDAO.selectByPrimaryKey(book.getId());

        assertNotNull(dbBookAfter);
        assertNull(dbBookAfter.getAuthor());
        assertNotSame(dbBookBefore.getAuthor(), dbBookAfter.getAuthor());
    }

    @Test
    public void testGetBookListByParams() {
        bookDAO.insert(book);

        Book paramBook = new Book();
        paramBook.setPage(1);
        paramBook.setLimit(1);
        paramBook.setBookname(book.getBookname());

        PageInfo<Book> bookPageInfo = bookSV.getBookListByParams(paramBook);

        assertNotNull(bookPageInfo);
        assertEquals(book.getId(), bookPageInfo.getList().get(0).getId());
    }

    @Test
    public void testGetListByParamsForApp() {
        bookDAO.insert(book);

        PageInfo<Map<String, Object>> bookPageInfo = bookSV.getListByParamsForApp(1, 1, book.getBookname(), null);

        assertNotNull(bookPageInfo);
        assertEquals(book.getId(), bookPageInfo.getList().get(0).get("id"));
    }

    @Test
    public void testGetBookDetailById() {
        bookDAO.insert(book);

        Map<String, Object> bookMap = bookSV.getBookDetailById(book.getId(), null);

        assertNotNull(bookMap);
        assertEquals(book.getId(), bookMap.get("id"));
    }

    @Test
    public void testIsLiked() {
        bookDAO.insert(book);
        Boolean isLiked = bookSV.isLiked(book.getId(), bookUser.getId());
        assertFalse(isLiked);
    }
}