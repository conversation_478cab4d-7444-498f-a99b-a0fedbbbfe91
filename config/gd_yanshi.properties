#修改此处，务必将另一个文件里面的路径一同修改了：pbms-web\src\main\resources\public\index.html,pbms-web\src\main\resources\config\bulletinConfig.json
pbms.webapp.baseUrl=/dangjian
pbms.phoneapp.baseUrl=/phoneapp
# 登录页URL配置
pbms.loginHsot=http://*************
pbms.loginHsotIp=http://*************
pbms.loginurl=/web/modules/login/anonymous.html
pbms.roomUrl=/module/pbmsappvm.html
#PDF默认头像URL配置
pbms.headimgurl=pbms-headimgurl:http://oss-cn-luoyang-onlinestor-d01-a.res.online.stor/prd-pbms/news/undefined/201902221449059460000009195135/2019/7/16/201907161728137490000019719430.png?AWSAccessKeyId=cPwZkfjdBOzFOR9M&Expires=**********&Signature=4Jq0vVcTn%2F5jobxkhTWPGXrNyMY%3D
pbms.ShuiYinImgData=pbms-ShuiYinImgData:data:image/PNG;base64,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
#系统默认密码
pbms.defaultPwd=pbms-defaultPwd:123456a
#PDF图片访问前缀地址，绝对路径
pbms.prefixPdf=http://*************/onest/
#三会一课签到二维码logo
pbms.logo_me_sign=pbms-logo_me_sign:logo/logo_me_sign.png
#三会一课二维码背景底图
pbms.qr_me_bg=pbms-qr_me_bg:logo/qr_me_bg.png
#视频直播平台点播视频列表接入地址
pbms.vcaddress=pbms-vcaddress:
#视频直播平台账号
pbms.vcaccount=pbms-vcaccount:
#视频直播平台直播ID
pbms.vcplayid=pbms-vcplayid:
#视频直播平台tokenkey
pbms.vctokenkey=pbms-vctokenkey:
# 环境判断
pbms.env.isprd=false
#-- 分布式唯一标识配置
pbms.sequencekey=pbms-sequencekey:PBMS_PRDUIDSEQ
#-- 附件存储前缀地址，跟Nginx中一致
pbms.prefixonest=pbms-prefixonest:onest

pbms.animeet.host=
pbms.animeet.key=
#登录
pbms.animeet.loginUrl=/icbase/enterprise/user/login
#查询会议详情
pbms.animeet.conferenceDetails=/icbase/conference/conferenceDetails
#离线转写
pbms.animeet.offlineTransfer=/icvoiceconvert/order/offlineTransfer
#获取最新文本id
pbms.animeet.getTextJson=/icvoicetransfer/userText/getTextJson
#上传流媒体视频下载地址
pbms.animeet.transferVideo=/icvoicetransfer/audio/transferVideo

#-- 点播平台，接入平台渠道编码（cid） 接入平台的接入密钥（tokenKey）  接入平台的接口调用地址
pbms.vodVideoHost=pbms-vodVideoHost:
#**************:31011 **************:31011
pbms.vodVcHost=pbms-vodVcHost:
pbms.vodcid=pbms-vodcid:
pbms.vodtokenkey=pbms-vodtokenkey:
pbms.vodBaseUrl=pbms-vodBaseUrl:
pbms.audiocid=pbms-audiocid:
pbms.audiotokenkey=pbms-audiotokenkey:
pbms.audioBaseUrl=pbms-audioBaseUrl:
#-- 入口字符串加解密配置
pbms.asekey=pbms-asekey:ACMP_PBMS_4ZX7MD
pbms.aseiv=pbms-aseiv:ACMP_PBMS_XFRBAI
pbms.verifycode=pbms-verifycode:PBMS_PRD
#-- 新版86君接入摘要
pbms.ng86appkey=pbms-ng86appkey:
#-- 入口地址配置
pbms.weburl=pbms-weburl:/web/modules/index/index.html
pbms.appurl=pbms-appurl:/module/pm.html
pbms.newsurl=pbms-newsurl:/module/newscontent.html
pbms.plurl=pbms-plurl:/module/pm.html#/pl/list
pbms.stusouurl=pbms-stusouurl:/pl/studysourceinfo.html
pbms.ceurl=pbms-ceurl:/module/pm.html#/activity/detail
pbms.ddurl=pbms-ddurl:/module/pm.html#/activity/detail
pbms.rnurl=pbms-rnurl:/rn/readinginfo.html
pbms.bookurl=pbms-bookurl:/module/pm.html#/rn/activity
#-- 消息推送相关地址配置
pbms.mobilelogin=pbms-mobilelogin:/user/loginBy86APPToken
pbms.outterprotocol=pbms-outterprotocol:http
pbms.outterhost=pbms-outterhost:
pbms.outterport=pbms-outterport:8980
#-- 数据同步相关信息，用于定位数据节点
pbms.syncrootorgcode=pbms-syncrootorgcode:
pbms.syncsyscode=pbms-syncsyscode:
#-- 短信平台相关接口
cmos.sms.username=
cmos.sms.apikey=
#--短信验证码模板id
cmos.sms.rspId001=26972
#--活动招募/指派/问卷调查/意见征集/投票	模板id
cmos.sms.rspId002=27075
#--三会一课计划制定/会议通知/会议出席	模板id
cmos.sms.rspId003=27076
#--读书活动/支部大会/党课笔记提交	模板id
cmos.sms.rspId004=27077
#--活动取消/会议取消/已通知会议修改	模板id
cmos.sms.rspId005=27078
#--用户开通通知
cmos.sms.rspId006=27189
#--用户修改密码的验证码
cmos.sms.rspId007=27190
#--待办任务通知:（1：计划内的会议，每月/季度最后一天上午10：对没有归档完成的会议，短信通知所在小组/支部的上一级的党务工作者。2：计划内的会议，每月10号 上午10：00，对仍然没有开始做计划的党支部或者党小组，短信通知所在小组/支部的上一级党务工作者。）
cmos.sms.rspId008=27324
#预警任务提醒
cmos.sms.rspId009=27555
#同步党建云数据后提示党务工作者分配人员至党小组
cmos.sms.rspId010=28476
#三会一课参加会议提醒
cmos.sms.rspId011=28764
#三会一课列席人员会议提醒
cmos.sms.rspId012=29011
#--首次登录短信验证码模板id，包含初始登录密码
cmos.sms.rspId013=29134
#--党建在线-集中学习-参与通知
cmos.sms.rspId014=29149
#--党建在线-集中学习-待办提醒
cmos.sms.rspId015=29148
#--党建在线-集中学习-待办提醒
cmos.sms.rspId016=29148
#--年度评审待办提醒
cmos.sms.rspId017=29273
#--协同办公待办提醒
cmos.sms.rspId018=29469
#--会议开始前未参加人员提醒
cmos.sms.rspId019=29508
#党建代办提醒
cmos.sms.rspId020=29857
#党建在线学习提醒
cmos.sms.rspId021=29858
#请假审批提醒
cmos.sms.rspId022=29859
#请假审批结果提醒
cmos.sms.rspId023=29860
#换届提醒
cmos.sms.rspId026=30028
#换届委员缺额提醒
cmos.sms.rspId027=30029
#视频专题培训提醒
cmos.sms.rspId028=30045
#视频专题培训提醒
cmos.sms.rspId029=30053
#党员论坛有新问题产生时，系统需自动给专家团队发送短信提醒
cmos.sms.rspId030=30158
#月初提醒书记有书记检视任务
cmos.sms.rspId031=30231
#月末提醒未完成的书记完成书记检视任务
cmos.sms.rspId032=30232
#视频会议--首次提醒、二次提醒
cmos.sms.rspId033=30795
#视频会议--开始时间变更提醒
cmos.sms.rspId034=30796
#视频会议--取消提醒
cmos.sms.rspId035=30797
#书记履职-提醒党员查看
cmos.sms.rspId036=30890
#三会一课中台稽核退回
cmos.sms.rspId037=31176
#支部名片中台稽核退回
cmos.sms.rspId038=31206
#智慧党支部-书记签名待办提醒
cmos.sms.rspId039=31211
#智慧党支部-书记签名退回提醒党务工作者
cmos.sms.rspId040=31212
#智慧党支部-书记签名退回提醒其他签名人
cmos.sms.rspId041=31213
#智慧党支部-工作台任务提前N天提醒
cmos.sms.rspId042=31309
#智慧党支部-工作台任务即时短信提醒
cmos.sms.rspId043=31310
#智慧党支部-工作台任务持续N天未处理提醒
cmos.sms.rspId044=31311
#智慧党支部-书记履职月末提醒
cmos.sms.rspId045=31339

logging.config=logging-config:classpath:log4j2.xml
logging.file-web=app.log
logging.file-core=core.log
spring.aop.proxy-target-class=aop-proxy-target: false
spring.aop.auto=aop-auto:false
#-- 应用服务器配置
#-- 内嵌的SERVER应用服务器监听端口
server.port-core=port:8080
#-- 内嵌的WEB应用服务器监听端口
server.port=port:18080
#-- 内嵌的WEB应用服务器监听地址
server.address=address:0.0.0.0
#-- 应用上下文路径
server.context-core-path=context-core-path:
server.context-web-path=context-web-path:/pbms
#-- 接收新连接的队列大小最大值
server.tomcat.accept-count=accept-count:10
#-- 并发处理的最大线程数
server.tomcat.max-threads=max-threads:100
#-- 同时并发处理的最大连接数
server.tomcat.max-connections=max-conns:100
#-- Session过期时间
server.session.timeout=3600

#-- Dubbo相关配置
#-- Dubbo服务端监听端口
dubbo.provider.port=dubbo-port:20880
#-- dubbo生产者的应用标识
dubbo.provider.application-name=dubbo-appname:pbms-provider
#-- dubbo生产者：默认的服务超时时间，单位：毫秒
dubbo.provider.timeout=dubbo-timeout:100000
dubbo.provider.registry-address=dubbo-zk:N/A
dubbo.provider.annotation-package = dubbo-pkg:com.cmos.pbms.service

#-- Dubbo服务注册与发现的zookeeper集群地址
dubbo.consumer.registry-address=dubbo-zk:N/A
#-- dubbo消费者的应用标识
dubbo.consumer.application-name=dubbo-appname:pbms-consumer
#-- dubbo消费者：对应的dubbo生产者服务地址
dubbo.consumer.reference-url=dubbo-ref:dubbo://service:20880
#-- dubbo消息者：默认的服务访问超时时间，单位：毫秒
dubbo.consumer.timeout=dubbo-timeout:100000
dubbo.consumer.annotation-package = dubbo-pkg:com.cmos.pbms.web

#-- 日志中心配置
#-- 日志配置的配置中心配置开关
cmos.logger.cfg.flag=true
#-- 日志中心开关
cmos.logger.switch.enable-control=true
cmos.logger.switch.enable-core=true
#-- 日志调试开关
cmos.logger.debug.enable-control=false
cmos.logger.debug.enable-core=false
#-- 日志级别
cmos.logger.log.level-control=info
cmos.logger.log.level-core=info
#-- 日志发送方式，部署在核心域和接口域使用kafka方式，互联网域使用file方式。
cmos.logger.msg.sender-control=file
cmos.logger.msg.sender-core=file
#-- kafka地址，根据部署环境进行选择配置
#-- 洛阳生产kafka集群
cmos.logger.kafka.brokerlist=
cmos.logger.trace.probeType=WEB
cmos.logger.logger.context.impl=com.cmos.pbms.web.config.LogContextImpl
#-- ONEST配置

#ONEST.ENDPOINT=http://oss-cn-luoyang-onlinestor-d01-a.res.online.stor
#ONEST.UID=prd-pbms
#ONEST.DISPLAY-NAME=prd-pbms
#ONEST.ACCESS-KEY=cPwZkfjdBOzFOR9M
#ONEST.SECRET-KEY=ljK2hBjAzxhijSULFrzG87PWTOacNd
ONEST.bucketName=ONEST-bucketName:prd-pbms

#-- csf配置
#-- csf服务zookeeper地址，多个地址用逗号(,)隔开
cmos.csf.client.zk.address=
#-- true:直连模式，false:路由模式，默认false
cmos.csf.client.isdirect=false
#-- 失败重试次数，默认0，不进行重试
coms.csf.client.retrytimes=0
#-- 缓存中心配置
#-- redis地址
cache.redisAddress=redis1:6379,redis2:6379,redis3:6379
# 默认数据源
db.default=base
#-- 数据源配置
base.driverClassName=com.mysql.jdbc.Driver
base.url=*******************************************************************
base.username=pbms
base.password=rO0ABXciABBPGiEkIQw9TTgx6Pdpa6hiFvD7a5kyU+dnlfOU/r5+IQ==
base.initialSize=2
# 自定义初始化
spring.app.initializers=inits:
#-- 消息推送相关地址配置
pbms.appKey=pbms-appKey:
pbms.masterSecret=pbms-masterSecret:

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true
# 初始密码
pbms.loginPasswd=123456

# OA新聞圖片多餘地址
pbms.oa.newFilePrePath=
# OA系统ip地址（获取新闻用 洛阳环境两个都可以用************** 58080  ************** 58080）
# OA系统ip地址（获取新闻用 淮安环境两个都可以用***********:38080）
pbms.oa.getNewsHostName=
# OA系统端口号（获取新闻用）
pbms.oa.getNewsPort=
# OA系统获取新闻的URI（获取新闻用）
pbms.oa.getNewsUri=/news/partyBuilding
# OA系统ip地址（获取新闻图片用 洛阳环境两个都可以用************** 28080   ************** 28080）
# OA系统ip地址（获取新闻图片用 淮安环境两个都可以用************* 28080   *********** 28080）
pbms.oa.getFileHostName=
# OA系统端口号（获取新闻图片用）
pbms.oa.getFilePort=
# OA系统获取图片的URI（获取新闻图片用）
pbms.oa.getFileUri=/webrdp-web/system/service/jsonService.db/downloadAtt.port?open&dontWriteOut=yes&fileId=
#-- 党课直播房间列表
lecture_live_room=
#-- 党课直播房间占用时间（分钟）
lecture_live_user_time=1440
#-- 服务白名单
#pbms.serverWhiteList=pbms-serverWhiteList:["*************","***************"]
pbms.serverWhiteList=pbms-serverWhiteList:[]

#-- 云视讯企业ID（小鱼）
xy.enterpriseId=xy-enterpriseId:
#-- 云视讯token（小鱼）
xy.token=xy-token:

pbms.tmpDir=pbms-tmpDir:/root/app/
pbms.staticDir=pbms-staticDir:/usr/share/fonts/
